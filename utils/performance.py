"""
Performance Monitoring and Optimization
Tracks system performance, latency, and resource usage
"""

import time
import threading
import asyncio
from collections import deque, defaultdict
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
import statistics

import psutil
import torch

from config import PerformanceConfig
from utils import get_logger

@dataclass
class PerformanceMetrics:
    """Data class for performance metrics"""
    timestamp: float
    latency_ms: float
    cpu_usage: float
    memory_usage_mb: float
    gpu_memory_mb: float = 0
    gpu_utilization: float = 0
    transcription_accuracy: float = 0
    processing_time_ms: float = 0

@dataclass
class SystemStats:
    """System statistics summary"""
    avg_latency_ms: float = 0
    max_latency_ms: float = 0
    min_latency_ms: float = 0
    avg_cpu_usage: float = 0
    avg_memory_usage_mb: float = 0
    avg_gpu_memory_mb: float = 0
    avg_gpu_utilization: float = 0
    total_transcriptions: int = 0
    uptime_seconds: float = 0
    performance_score: float = 0

class PerformanceMonitor:
    """
    Real-time performance monitoring system
    Tracks latency, resource usage, and system health
    """
    
    def __init__(self, max_history: int = 1000):
        self.logger = get_logger(__name__)
        self.max_history = max_history
        
        # Performance data storage
        self.metrics_history = deque(maxlen=max_history)
        self.latency_history = deque(maxlen=max_history)
        self.transcription_times = deque(maxlen=max_history)
        
        # Real-time metrics
        self.current_metrics = PerformanceMetrics(
            timestamp=time.time(),
            latency_ms=0,
            cpu_usage=0,
            memory_usage_mb=0
        )
        
        # Monitoring state
        self.is_monitoring = False
        self.monitor_thread = None
        self.start_time = time.time()
        
        # Statistics
        self.stats = SystemStats()
        self.transcription_count = 0
        
        # Alerts and thresholds
        self.alert_thresholds = {
            'max_latency_ms': PerformanceConfig.MAX_ACCEPTABLE_LATENCY,
            'max_cpu_usage': PerformanceConfig.MAX_CPU_USAGE,
            'max_memory_mb': 8000,  # 8GB
            'max_gpu_memory_mb': 5000  # 5GB for GTX 1660 Ti
        }
        
        # Performance tracking
        self.performance_lock = threading.Lock()
        self.alert_callbacks = []
        
        self.logger.info("PerformanceMonitor initialized")
    
    def start(self):
        """Start performance monitoring"""
        if self.is_monitoring:
            self.logger.warning("Performance monitoring already running")
            return
        
        self.logger.info("Starting performance monitoring...")
        self.is_monitoring = True
        self.start_time = time.time()
        
        # Start monitoring thread
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info("Performance monitoring started")
    
    def stop(self):
        """Stop performance monitoring"""
        if not self.is_monitoring:
            return
        
        self.logger.info("Stopping performance monitoring...")
        self.is_monitoring = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("Performance monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                # Collect system metrics
                metrics = self._collect_system_metrics()
                
                # Store metrics
                with self.performance_lock:
                    self.current_metrics = metrics
                    self.metrics_history.append(metrics)
                
                # Update statistics
                self._update_statistics()
                
                # Check for alerts
                self._check_alerts(metrics)
                
                # Log performance metrics periodically
                if len(self.metrics_history) % (PerformanceConfig.PERFORMANCE_LOG_INTERVAL * 10) == 0:
                    self._log_performance_summary()
                
                # Sleep until next check
                time.sleep(PerformanceConfig.MEMORY_CHECK_INTERVAL)
                
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {str(e)}", exc_info=True)
                time.sleep(1)  # Prevent tight error loop
    
    def _collect_system_metrics(self) -> PerformanceMetrics:
        """Collect current system metrics"""
        try:
            # CPU and memory
            cpu_usage = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            memory_usage_mb = memory.used / (1024 * 1024)
            
            # GPU metrics
            gpu_memory_mb = 0
            gpu_utilization = 0
            
            if torch.cuda.is_available():
                try:
                    gpu_memory_mb = torch.cuda.memory_allocated(0) / (1024 * 1024)
                    
                    # Try to get GPU utilization (requires nvidia-ml-py)
                    try:
                        import pynvml
                        pynvml.nvmlInit()
                        handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                        utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)
                        gpu_utilization = utilization.gpu
                    except ImportError:
                        # nvidia-ml-py not available
                        pass
                    except Exception:
                        # Other GPU monitoring errors
                        pass
                        
                except Exception as e:
                    self.logger.debug(f"Error getting GPU metrics: {str(e)}")
            
            # Calculate current latency (average of recent measurements)
            current_latency = 0
            if self.latency_history:
                recent_latencies = list(self.latency_history)[-10:]  # Last 10 measurements
                current_latency = statistics.mean(recent_latencies)
            
            return PerformanceMetrics(
                timestamp=time.time(),
                latency_ms=current_latency,
                cpu_usage=cpu_usage,
                memory_usage_mb=memory_usage_mb,
                gpu_memory_mb=gpu_memory_mb,
                gpu_utilization=gpu_utilization
            )
            
        except Exception as e:
            self.logger.error(f"Error collecting system metrics: {str(e)}")
            return self.current_metrics  # Return last known metrics
    
    def _update_statistics(self):
        """Update performance statistics"""
        if not self.metrics_history:
            return
        
        try:
            # Calculate averages from recent history
            recent_metrics = list(self.metrics_history)[-100:]  # Last 100 measurements
            
            latencies = [m.latency_ms for m in recent_metrics if m.latency_ms > 0]
            cpu_usages = [m.cpu_usage for m in recent_metrics]
            memory_usages = [m.memory_usage_mb for m in recent_metrics]
            gpu_memory_usages = [m.gpu_memory_mb for m in recent_metrics]
            gpu_utilizations = [m.gpu_utilization for m in recent_metrics]
            
            # Update stats
            self.stats.avg_cpu_usage = statistics.mean(cpu_usages) if cpu_usages else 0
            self.stats.avg_memory_usage_mb = statistics.mean(memory_usages) if memory_usages else 0
            self.stats.avg_gpu_memory_mb = statistics.mean(gpu_memory_usages) if gpu_memory_usages else 0
            self.stats.avg_gpu_utilization = statistics.mean(gpu_utilizations) if gpu_utilizations else 0
            
            if latencies:
                self.stats.avg_latency_ms = statistics.mean(latencies)
                self.stats.max_latency_ms = max(latencies)
                self.stats.min_latency_ms = min(latencies)
            
            self.stats.total_transcriptions = self.transcription_count
            self.stats.uptime_seconds = time.time() - self.start_time
            
            # Calculate performance score (0-100)
            self.stats.performance_score = self._calculate_performance_score()
            
        except Exception as e:
            self.logger.error(f"Error updating statistics: {str(e)}")
    
    def _calculate_performance_score(self) -> float:
        """Calculate overall performance score (0-100)"""
        try:
            score = 100.0
            
            # Latency penalty
            if self.stats.avg_latency_ms > PerformanceConfig.TARGET_LATENCY:
                latency_penalty = min(50, (self.stats.avg_latency_ms - PerformanceConfig.TARGET_LATENCY) / 10)
                score -= latency_penalty
            
            # CPU usage penalty
            if self.stats.avg_cpu_usage > 70:
                cpu_penalty = min(20, (self.stats.avg_cpu_usage - 70) / 2)
                score -= cpu_penalty
            
            # Memory usage penalty
            if self.stats.avg_gpu_memory_mb > 4000:  # 4GB threshold
                memory_penalty = min(15, (self.stats.avg_gpu_memory_mb - 4000) / 200)
                score -= memory_penalty
            
            return max(0, score)
            
        except Exception:
            return 50.0  # Default score if calculation fails
    
    def _check_alerts(self, metrics: PerformanceMetrics):
        """Check for performance alerts"""
        alerts = []
        
        # Check latency
        if metrics.latency_ms > self.alert_thresholds['max_latency_ms']:
            alerts.append(f"High latency: {metrics.latency_ms:.1f}ms")
        
        # Check CPU usage
        if metrics.cpu_usage > self.alert_thresholds['max_cpu_usage']:
            alerts.append(f"High CPU usage: {metrics.cpu_usage:.1f}%")
        
        # Check memory usage
        if metrics.memory_usage_mb > self.alert_thresholds['max_memory_mb']:
            alerts.append(f"High memory usage: {metrics.memory_usage_mb:.0f}MB")
        
        # Check GPU memory
        if metrics.gpu_memory_mb > self.alert_thresholds['max_gpu_memory_mb']:
            alerts.append(f"High GPU memory usage: {metrics.gpu_memory_mb:.0f}MB")
        
        # Log alerts
        for alert in alerts:
            self.logger.warning(f"Performance alert: {alert}")
        
        # Call alert callbacks
        if alerts:
            for callback in self.alert_callbacks:
                try:
                    callback(alerts, metrics)
                except Exception as e:
                    self.logger.error(f"Error in alert callback: {str(e)}")
    
    def _log_performance_summary(self):
        """Log performance summary"""
        self.logger.info(
            f"Performance summary - "
            f"Latency: {self.stats.avg_latency_ms:.1f}ms avg, "
            f"CPU: {self.stats.avg_cpu_usage:.1f}%, "
            f"Memory: {self.stats.avg_memory_usage_mb:.0f}MB, "
            f"GPU Memory: {self.stats.avg_gpu_memory_mb:.0f}MB, "
            f"Score: {self.stats.performance_score:.1f}/100"
        )
    
    def record_latency(self, latency_ms: float):
        """Record a latency measurement"""
        with self.performance_lock:
            self.latency_history.append(latency_ms)
    
    def record_transcription(self, result):
        """Record a transcription result"""
        with self.performance_lock:
            self.transcription_count += 1
            
            if hasattr(result, 'processing_time'):
                processing_time_ms = result.processing_time * 1000
                self.transcription_times.append(processing_time_ms)
    
    def get_current_metrics(self) -> PerformanceMetrics:
        """Get current performance metrics"""
        with self.performance_lock:
            return self.current_metrics
    
    def get_statistics(self) -> SystemStats:
        """Get performance statistics"""
        with self.performance_lock:
            return self.stats
    
    def get_history(self, limit: int = 100) -> List[PerformanceMetrics]:
        """Get performance history"""
        with self.performance_lock:
            return list(self.metrics_history)[-limit:]
    
    def add_alert_callback(self, callback):
        """Add callback for performance alerts"""
        self.alert_callbacks.append(callback)
    
    def reset_statistics(self):
        """Reset all statistics"""
        with self.performance_lock:
            self.metrics_history.clear()
            self.latency_history.clear()
            self.transcription_times.clear()
            self.transcription_count = 0
            self.start_time = time.time()
            self.stats = SystemStats()
        
        self.logger.info("Performance statistics reset")
    
    def export_metrics(self, filepath: str):
        """Export metrics to file"""
        try:
            import json
            
            data = {
                'statistics': {
                    'avg_latency_ms': self.stats.avg_latency_ms,
                    'max_latency_ms': self.stats.max_latency_ms,
                    'min_latency_ms': self.stats.min_latency_ms,
                    'avg_cpu_usage': self.stats.avg_cpu_usage,
                    'avg_memory_usage_mb': self.stats.avg_memory_usage_mb,
                    'avg_gpu_memory_mb': self.stats.avg_gpu_memory_mb,
                    'total_transcriptions': self.stats.total_transcriptions,
                    'uptime_seconds': self.stats.uptime_seconds,
                    'performance_score': self.stats.performance_score
                },
                'recent_metrics': [
                    {
                        'timestamp': m.timestamp,
                        'latency_ms': m.latency_ms,
                        'cpu_usage': m.cpu_usage,
                        'memory_usage_mb': m.memory_usage_mb,
                        'gpu_memory_mb': m.gpu_memory_mb,
                        'gpu_utilization': m.gpu_utilization
                    }
                    for m in list(self.metrics_history)[-100:]
                ]
            }
            
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)
            
            self.logger.info(f"Metrics exported to {filepath}")
            
        except Exception as e:
            self.logger.error(f"Error exporting metrics: {str(e)}")

if __name__ == "__main__":
    # Test performance monitor
    monitor = PerformanceMonitor()
    monitor.start()
    
    # Simulate some activity
    for i in range(10):
        monitor.record_latency(100 + i * 10)
        time.sleep(1)
    
    # Print statistics
    stats = monitor.get_statistics()
    print(f"Statistics: {stats}")
    
    monitor.stop()
