"""
File Operations Handler
Provides file management utilities for transcriptions, exports, and data persistence
"""

import os
import json
import csv
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import threading
import shutil

from config import DATA_DIR
from utils import get_logger

class FileHandler:
    """
    Handles file operations for the voice-to-text system
    Supports multiple export formats and data persistence
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # File paths
        self.data_dir = DATA_DIR
        self.transcriptions_dir = self.data_dir / "transcriptions"
        self.logs_dir = self.data_dir / "logs"
        self.audio_samples_dir = self.data_dir / "audio_samples"
        
        # Ensure directories exist
        self._ensure_directories()
        
        # File operations lock
        self.file_lock = threading.Lock()
        
        # Statistics
        self.files_saved = 0
        self.files_loaded = 0
        self.total_size_saved = 0
        
        self.logger.info("FileHandler initialized")
    
    def _ensure_directories(self):
        """Ensure all required directories exist"""
        directories = [
            self.data_dir,
            self.transcriptions_dir,
            self.logs_dir,
            self.audio_samples_dir
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
        
        self.logger.debug("Data directories ensured")
    
    def save_transcription(self, transcription_data: Dict[str, Any], 
                          filename: Optional[str] = None,
                          format_type: str = "json") -> str:
        """
        Save transcription data to file
        
        Args:
            transcription_data: Transcription data to save
            filename: Optional filename (auto-generated if None)
            format_type: File format (json, txt, csv)
            
        Returns:
            Path to saved file
        """
        try:
            with self.file_lock:
                # Generate filename if not provided
                if not filename:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    session_id = transcription_data.get('session_id', 'unknown')
                    filename = f"transcription_{session_id}_{timestamp}.{format_type}"
                
                filepath = self.transcriptions_dir / filename
                
                # Save based on format
                if format_type.lower() == "json":
                    self._save_json(filepath, transcription_data)
                elif format_type.lower() == "txt":
                    self._save_text(filepath, transcription_data)
                elif format_type.lower() == "csv":
                    self._save_csv(filepath, transcription_data)
                else:
                    raise ValueError(f"Unsupported format: {format_type}")
                
                # Update statistics
                self.files_saved += 1
                self.total_size_saved += filepath.stat().st_size
                
                self.logger.info(f"Transcription saved: {filepath}")
                return str(filepath)
                
        except Exception as e:
            self.logger.error(f"Error saving transcription: {str(e)}", exc_info=True)
            raise
    
    def _save_json(self, filepath: Path, data: Dict[str, Any]):
        """Save data as JSON file"""
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, default=str, ensure_ascii=False)
    
    def _save_text(self, filepath: Path, data: Dict[str, Any]):
        """Save data as plain text file"""
        with open(filepath, 'w', encoding='utf-8') as f:
            # Write header
            f.write(f"Transcription Session: {data.get('session_id', 'Unknown')}\n")
            f.write(f"Created: {data.get('created_at', 'Unknown')}\n")
            f.write(f"Model: {data.get('model_type', 'Unknown')}\n")
            f.write(f"Language: {data.get('language', 'Unknown')}\n")
            f.write("=" * 50 + "\n\n")
            
            # Write transcription results
            results = data.get('transcription_results', [])
            for i, result in enumerate(results, 1):
                timestamp = result.get('timestamp', '')
                if isinstance(timestamp, str):
                    timestamp_str = timestamp
                else:
                    timestamp_str = datetime.fromtimestamp(timestamp).strftime('%H:%M:%S')
                
                text = result.get('text', '')
                confidence = result.get('confidence', 0)
                is_final = result.get('is_final', False)
                
                f.write(f"[{timestamp_str}] {'FINAL' if is_final else 'REALTIME'} "
                       f"(confidence: {confidence:.2f})\n")
                f.write(f"{text}\n\n")
    
    def _save_csv(self, filepath: Path, data: Dict[str, Any]):
        """Save data as CSV file"""
        with open(filepath, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # Write header
            writer.writerow([
                'timestamp', 'text', 'confidence', 'is_final', 'emotion', 
                'emotion_confidence', 'processing_time_ms'
            ])
            
            # Write transcription results
            results = data.get('transcription_results', [])
            for result in results:
                emotion_info = result.get('emotion', {})
                emotion = emotion_info.get('primary_emotion', '') if emotion_info else ''
                emotion_confidence = emotion_info.get('confidence', 0) if emotion_info else 0
                
                writer.writerow([
                    result.get('timestamp', ''),
                    result.get('text', ''),
                    result.get('confidence', 0),
                    result.get('is_final', False),
                    emotion,
                    emotion_confidence,
                    result.get('processing_time_ms', 0)
                ])
    
    def load_transcription(self, filepath: Union[str, Path]) -> Dict[str, Any]:
        """
        Load transcription data from file
        
        Args:
            filepath: Path to transcription file
            
        Returns:
            Loaded transcription data
        """
        try:
            filepath = Path(filepath)
            
            if not filepath.exists():
                raise FileNotFoundError(f"File not found: {filepath}")
            
            with self.file_lock:
                # Load based on file extension
                if filepath.suffix.lower() == '.json':
                    data = self._load_json(filepath)
                else:
                    raise ValueError(f"Unsupported file format: {filepath.suffix}")
                
                self.files_loaded += 1
                self.logger.info(f"Transcription loaded: {filepath}")
                return data
                
        except Exception as e:
            self.logger.error(f"Error loading transcription: {str(e)}", exc_info=True)
            raise
    
    def _load_json(self, filepath: Path) -> Dict[str, Any]:
        """Load data from JSON file"""
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def list_transcriptions(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        List available transcription files
        
        Args:
            limit: Maximum number of files to return
            
        Returns:
            List of file information
        """
        try:
            files_info = []
            
            # Get all transcription files
            pattern = "transcription_*"
            files = list(self.transcriptions_dir.glob(pattern))
            
            # Sort by modification time (newest first)
            files.sort(key=lambda f: f.stat().st_mtime, reverse=True)
            
            # Apply limit if specified
            if limit:
                files = files[:limit]
            
            # Collect file information
            for filepath in files:
                stat = filepath.stat()
                files_info.append({
                    'filename': filepath.name,
                    'filepath': str(filepath),
                    'size_bytes': stat.st_size,
                    'created_at': datetime.fromtimestamp(stat.st_ctime),
                    'modified_at': datetime.fromtimestamp(stat.st_mtime),
                    'format': filepath.suffix[1:] if filepath.suffix else 'unknown'
                })
            
            return files_info
            
        except Exception as e:
            self.logger.error(f"Error listing transcriptions: {str(e)}")
            return []
    
    def delete_transcription(self, filepath: Union[str, Path]) -> bool:
        """
        Delete a transcription file
        
        Args:
            filepath: Path to file to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            filepath = Path(filepath)
            
            if not filepath.exists():
                self.logger.warning(f"File not found for deletion: {filepath}")
                return False
            
            with self.file_lock:
                filepath.unlink()
                self.logger.info(f"Transcription deleted: {filepath}")
                return True
                
        except Exception as e:
            self.logger.error(f"Error deleting transcription: {str(e)}")
            return False
    
    def export_session_data(self, session_data: Dict[str, Any], 
                           export_format: str = "json") -> str:
        """
        Export complete session data
        
        Args:
            session_data: Complete session data
            export_format: Export format (json, txt, csv)
            
        Returns:
            Path to exported file
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            session_id = session_data.get('session_id', 'unknown')
            filename = f"session_export_{session_id}_{timestamp}.{export_format}"
            
            return self.save_transcription(session_data, filename, export_format)
            
        except Exception as e:
            self.logger.error(f"Error exporting session data: {str(e)}")
            raise
    
    def backup_data(self, backup_dir: Optional[Union[str, Path]] = None) -> str:
        """
        Create backup of all data
        
        Args:
            backup_dir: Directory to store backup (default: data/backups)
            
        Returns:
            Path to backup directory
        """
        try:
            if backup_dir is None:
                backup_dir = self.data_dir / "backups"
            else:
                backup_dir = Path(backup_dir)
            
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Create timestamped backup directory
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = backup_dir / f"backup_{timestamp}"
            
            # Copy all data
            shutil.copytree(self.data_dir, backup_path, 
                          ignore=shutil.ignore_patterns('backups', '*.tmp', '*.lock'))
            
            self.logger.info(f"Data backup created: {backup_path}")
            return str(backup_path)
            
        except Exception as e:
            self.logger.error(f"Error creating backup: {str(e)}")
            raise
    
    def cleanup_old_files(self, days_old: int = 30) -> int:
        """
        Clean up old transcription files
        
        Args:
            days_old: Delete files older than this many days
            
        Returns:
            Number of files deleted
        """
        try:
            cutoff_time = datetime.now().timestamp() - (days_old * 24 * 3600)
            deleted_count = 0
            
            # Check transcription files
            for filepath in self.transcriptions_dir.glob("transcription_*"):
                if filepath.stat().st_mtime < cutoff_time:
                    try:
                        filepath.unlink()
                        deleted_count += 1
                        self.logger.debug(f"Deleted old file: {filepath}")
                    except Exception as e:
                        self.logger.warning(f"Failed to delete {filepath}: {str(e)}")
            
            if deleted_count > 0:
                self.logger.info(f"Cleaned up {deleted_count} old transcription files")
            
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {str(e)}")
            return 0
    
    def get_storage_info(self) -> Dict[str, Any]:
        """Get storage usage information"""
        try:
            def get_dir_size(directory: Path) -> int:
                """Calculate directory size recursively"""
                total_size = 0
                for filepath in directory.rglob('*'):
                    if filepath.is_file():
                        total_size += filepath.stat().st_size
                return total_size
            
            # Calculate sizes
            transcriptions_size = get_dir_size(self.transcriptions_dir)
            logs_size = get_dir_size(self.logs_dir)
            audio_samples_size = get_dir_size(self.audio_samples_dir)
            total_size = transcriptions_size + logs_size + audio_samples_size
            
            # Count files
            transcription_count = len(list(self.transcriptions_dir.glob("transcription_*")))
            
            # Get disk usage
            disk_usage = shutil.disk_usage(self.data_dir)
            
            return {
                'transcriptions_size_mb': transcriptions_size / (1024 * 1024),
                'logs_size_mb': logs_size / (1024 * 1024),
                'audio_samples_size_mb': audio_samples_size / (1024 * 1024),
                'total_size_mb': total_size / (1024 * 1024),
                'transcription_files_count': transcription_count,
                'disk_total_gb': disk_usage.total / (1024 * 1024 * 1024),
                'disk_used_gb': disk_usage.used / (1024 * 1024 * 1024),
                'disk_free_gb': disk_usage.free / (1024 * 1024 * 1024),
                'files_saved': self.files_saved,
                'files_loaded': self.files_loaded,
                'total_size_saved_mb': self.total_size_saved / (1024 * 1024)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting storage info: {str(e)}")
            return {}
    
    def get_file_statistics(self) -> Dict[str, Any]:
        """Get file operation statistics"""
        return {
            'files_saved': self.files_saved,
            'files_loaded': self.files_loaded,
            'total_size_saved_bytes': self.total_size_saved
        }

if __name__ == "__main__":
    # Test file handler
    handler = FileHandler()
    
    # Test data
    test_data = {
        'session_id': 'test_session',
        'created_at': datetime.now().isoformat(),
        'model_type': 'medium.en',
        'language': 'en',
        'transcription_results': [
            {
                'timestamp': time.time(),
                'text': 'Hello, this is a test transcription.',
                'confidence': 0.95,
                'is_final': True,
                'processing_time_ms': 125.5
            }
        ]
    }
    
    # Save test transcription
    filepath = handler.save_transcription(test_data, format_type='json')
    print(f"Saved: {filepath}")
    
    # List transcriptions
    files = handler.list_transcriptions(limit=5)
    print(f"Files: {len(files)}")
    
    # Get storage info
    storage_info = handler.get_storage_info()
    print(f"Storage info: {storage_info}")
