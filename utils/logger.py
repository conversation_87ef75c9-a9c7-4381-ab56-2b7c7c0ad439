"""
Logging System for Real-time Voice-to-Text System
Provides centralized logging with file rotation and performance tracking
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional
import threading
from datetime import datetime

from config import LoggingConfig, LOGS_DIR

# Global logger instances
_loggers = {}
_logger_lock = threading.Lock()

class ColoredFormatter(logging.Formatter):
    """Custom formatter with color support for console output"""
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',     # Cyan
        'INFO': '\033[32m',      # Green
        'WARNING': '\033[33m',   # Yellow
        'ERROR': '\033[31m',     # Red
        'CRITICAL': '\033[35m',  # Magenta
        'RESET': '\033[0m'       # Reset
    }
    
    def format(self, record):
        # Add color to levelname
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)

class PerformanceFilter(logging.Filter):
    """Filter for performance-related log messages"""
    
    def filter(self, record):
        # Only allow performance-related messages
        performance_keywords = ['latency', 'performance', 'memory', 'gpu', 'processing_time']
        message = record.getMessage().lower()
        return any(keyword in message for keyword in performance_keywords)

def setup_logger(name: str = "realstt", 
                console_level: str = None,
                file_level: str = None) -> logging.Logger:
    """
    Set up a logger with both console and file handlers
    
    Args:
        name: Logger name
        console_level: Console logging level
        file_level: File logging level
        
    Returns:
        Configured logger instance
    """
    
    with _logger_lock:
        # Return existing logger if already configured
        if name in _loggers:
            return _loggers[name]
        
        # Create logger
        logger = logging.getLogger(name)
        logger.setLevel(logging.DEBUG)  # Set to lowest level, handlers will filter
        
        # Clear any existing handlers
        logger.handlers.clear()
        
        # Use config defaults if not specified
        console_level = console_level or LoggingConfig.CONSOLE_LOG_LEVEL
        file_level = file_level or LoggingConfig.FILE_LOG_LEVEL
        
        # Create formatters
        console_formatter = ColoredFormatter(
            '%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
            datefmt='%H:%M:%S'
        )
        
        file_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)s | %(funcName)s:%(lineno)d | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, console_level.upper()))
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
        
        # Ensure log directory exists
        LOGS_DIR.mkdir(exist_ok=True)
        
        # Main log file handler with rotation
        main_log_handler = logging.handlers.RotatingFileHandler(
            LoggingConfig.MAIN_LOG_FILE,
            maxBytes=LoggingConfig.MAX_LOG_SIZE,
            backupCount=LoggingConfig.BACKUP_COUNT,
            encoding='utf-8'
        )
        main_log_handler.setLevel(getattr(logging, file_level.upper()))
        main_log_handler.setFormatter(file_formatter)
        logger.addHandler(main_log_handler)
        
        # Error log file handler (errors and critical only)
        error_log_handler = logging.handlers.RotatingFileHandler(
            LoggingConfig.ERROR_LOG_FILE,
            maxBytes=LoggingConfig.MAX_LOG_SIZE,
            backupCount=LoggingConfig.BACKUP_COUNT,
            encoding='utf-8'
        )
        error_log_handler.setLevel(logging.ERROR)
        error_log_handler.setFormatter(file_formatter)
        logger.addHandler(error_log_handler)
        
        # Performance log file handler
        if LoggingConfig.PERFORMANCE_LOG_FILE:
            performance_log_handler = logging.handlers.RotatingFileHandler(
                LoggingConfig.PERFORMANCE_LOG_FILE,
                maxBytes=LoggingConfig.MAX_LOG_SIZE,
                backupCount=LoggingConfig.BACKUP_COUNT,
                encoding='utf-8'
            )
            performance_log_handler.setLevel(logging.INFO)
            performance_log_handler.setFormatter(file_formatter)
            performance_log_handler.addFilter(PerformanceFilter())
            logger.addHandler(performance_log_handler)
        
        # Store logger
        _loggers[name] = logger
        
        # Log initialization
        logger.info(f"Logger '{name}' initialized")
        logger.info(f"Console level: {console_level}, File level: {file_level}")
        logger.info(f"Log files: {LOGS_DIR}")
        
        return logger

def get_logger(name: str = None) -> logging.Logger:
    """
    Get an existing logger or create a new one
    
    Args:
        name: Logger name (uses module name if None)
        
    Returns:
        Logger instance
    """
    if name is None:
        # Get the calling module name
        import inspect
        frame = inspect.currentframe().f_back
        name = frame.f_globals.get('__name__', 'realstt')
    
    # Return existing logger or create new one
    if name in _loggers:
        return _loggers[name]
    else:
        return setup_logger(name)

class LogContext:
    """Context manager for temporary log level changes"""
    
    def __init__(self, logger: logging.Logger, level: str):
        self.logger = logger
        self.new_level = getattr(logging, level.upper())
        self.old_level = logger.level
    
    def __enter__(self):
        self.logger.setLevel(self.new_level)
        return self.logger
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.logger.setLevel(self.old_level)

class TimedLogger:
    """Logger with automatic timing for operations"""
    
    def __init__(self, logger: logging.Logger, operation: str, level: str = "INFO"):
        self.logger = logger
        self.operation = operation
        self.level = getattr(logging, level.upper())
        self.start_time = None
    
    def __enter__(self):
        self.start_time = datetime.now()
        self.logger.log(self.level, f"Starting {self.operation}...")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = (datetime.now() - self.start_time).total_seconds()
        
        if exc_type is None:
            self.logger.log(self.level, f"Completed {self.operation} in {duration:.3f}s")
        else:
            self.logger.error(f"Failed {self.operation} after {duration:.3f}s: {exc_val}")

def log_performance(func):
    """Decorator to log function performance"""
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        
        with TimedLogger(logger, f"{func.__name__}", "DEBUG"):
            return func(*args, **kwargs)
    
    return wrapper

def log_exceptions(func):
    """Decorator to log exceptions"""
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Exception in {func.__name__}: {str(e)}", exc_info=True)
            raise
    
    return wrapper

def setup_system_logging():
    """Set up system-wide logging configuration"""
    
    # Set up main system logger
    main_logger = setup_logger("realstt")
    
    # Set up component loggers
    component_loggers = [
        "realstt.core",
        "realstt.api", 
        "realstt.ui",
        "realstt.utils",
        "realstt.models"
    ]
    
    for component in component_loggers:
        setup_logger(component)
    
    # Configure third-party loggers
    logging.getLogger("transformers").setLevel(logging.WARNING)
    logging.getLogger("torch").setLevel(logging.WARNING)
    logging.getLogger("librosa").setLevel(logging.WARNING)
    logging.getLogger("RealtimeSTT").setLevel(logging.INFO)
    
    main_logger.info("System logging configured")
    return main_logger

def get_log_stats() -> dict:
    """Get logging statistics"""
    stats = {
        'active_loggers': len(_loggers),
        'logger_names': list(_loggers.keys()),
        'log_files': {
            'main': LoggingConfig.MAIN_LOG_FILE.exists(),
            'error': LoggingConfig.ERROR_LOG_FILE.exists(),
            'performance': LoggingConfig.PERFORMANCE_LOG_FILE.exists() if LoggingConfig.PERFORMANCE_LOG_FILE else False
        }
    }
    
    # Get file sizes if they exist
    for log_type, exists in stats['log_files'].items():
        if exists:
            if log_type == 'main':
                size = LoggingConfig.MAIN_LOG_FILE.stat().st_size
            elif log_type == 'error':
                size = LoggingConfig.ERROR_LOG_FILE.stat().st_size
            elif log_type == 'performance' and LoggingConfig.PERFORMANCE_LOG_FILE:
                size = LoggingConfig.PERFORMANCE_LOG_FILE.stat().st_size
            else:
                size = 0
            
            stats['log_files'][f'{log_type}_size_mb'] = size / (1024 * 1024)
    
    return stats

# Initialize system logging when module is imported
if __name__ != "__main__":
    # Only set up logging if not running as main module
    try:
        setup_system_logging()
    except Exception as e:
        print(f"Warning: Failed to initialize logging: {e}")

if __name__ == "__main__":
    # Test logging functionality
    logger = setup_logger("test")
    
    logger.debug("Debug message")
    logger.info("Info message")
    logger.warning("Warning message")
    logger.error("Error message")
    logger.critical("Critical message")
    
    # Test timed logger
    with TimedLogger(logger, "test operation"):
        import time
        time.sleep(1)
    
    # Test performance decorator
    @log_performance
    def test_function():
        time.sleep(0.5)
        return "test result"
    
    result = test_function()
    
    # Print stats
    stats = get_log_stats()
    logger.info(f"Logging stats: {stats}")
