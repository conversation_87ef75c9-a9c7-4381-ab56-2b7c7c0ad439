"""
Advanced Session Management & Analytics
Comprehensive session management with detailed analytics, user profiles,
and performance tracking with visualization capabilities
"""

import uuid
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field, asdict
from collections import defaultdict, deque
import threading
import sqlite3
from pathlib import Path

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

from config import DATA_DIR
from utils import get_logger

@dataclass
class TranscriptionEntry:
    """Individual transcription entry"""
    timestamp: float
    text: str
    confidence: float
    is_final: bool
    processing_time_ms: float
    language: str = 'en'
    emotion: Optional[Dict[str, Any]] = None
    pauses: Optional[List[Dict[str, Any]]] = None
    filler_words: Optional[List[str]] = None
    audio_quality: Optional[float] = None

@dataclass
class SessionMetrics:
    """Session performance metrics"""
    total_transcriptions: int = 0
    final_transcriptions: int = 0
    average_confidence: float = 0.0
    average_processing_time_ms: float = 0.0
    total_processing_time_ms: float = 0.0
    words_per_minute: float = 0.0
    accuracy_estimate: float = 0.0
    
    # Language statistics
    languages_detected: Dict[str, int] = field(default_factory=dict)
    language_switches: int = 0
    
    # Emotion statistics
    emotions_detected: Dict[str, int] = field(default_factory=dict)
    dominant_emotion: Optional[str] = None
    
    # Audio quality metrics
    average_audio_quality: float = 0.0
    silence_ratio: float = 0.0
    noise_level: float = 0.0
    
    # Performance metrics
    real_time_factor: float = 0.0
    latency_p95_ms: float = 0.0
    error_rate: float = 0.0

@dataclass
class SessionInfo:
    """Complete session information"""
    session_id: str
    user_id: Optional[str]
    created_at: datetime
    updated_at: datetime
    ended_at: Optional[datetime]
    
    # Configuration
    model_type: str
    language: str
    features_enabled: Dict[str, bool]
    
    # Status
    status: str  # 'active', 'paused', 'completed', 'error'
    duration_seconds: float = 0.0
    
    # Data
    transcriptions: List[TranscriptionEntry] = field(default_factory=list)
    metrics: SessionMetrics = field(default_factory=SessionMetrics)
    
    # Metadata
    tags: List[str] = field(default_factory=list)
    notes: str = ""
    export_formats: List[str] = field(default_factory=list)

class SessionDatabase:
    """SQLite database for session persistence"""
    
    def __init__(self, db_path: Optional[Path] = None):
        self.db_path = db_path or DATA_DIR / "sessions.db"
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self.logger = get_logger(__name__)
        
        # Initialize database
        self._init_database()
        
        self.logger.info(f"SessionDatabase initialized: {self.db_path}")
    
    def _init_database(self):
        """Initialize database tables"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS sessions (
                    session_id TEXT PRIMARY KEY,
                    user_id TEXT,
                    created_at TIMESTAMP,
                    updated_at TIMESTAMP,
                    ended_at TIMESTAMP,
                    model_type TEXT,
                    language TEXT,
                    features_enabled TEXT,
                    status TEXT,
                    duration_seconds REAL,
                    metrics TEXT,
                    tags TEXT,
                    notes TEXT,
                    export_formats TEXT
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS transcriptions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT,
                    timestamp REAL,
                    text TEXT,
                    confidence REAL,
                    is_final BOOLEAN,
                    processing_time_ms REAL,
                    language TEXT,
                    emotion TEXT,
                    pauses TEXT,
                    filler_words TEXT,
                    audio_quality REAL,
                    FOREIGN KEY (session_id) REFERENCES sessions (session_id)
                )
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_sessions_created_at ON sessions(created_at)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_transcriptions_session_id ON transcriptions(session_id)
            """)
    
    def save_session(self, session: SessionInfo):
        """Save session to database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO sessions 
                    (session_id, user_id, created_at, updated_at, ended_at, model_type, 
                     language, features_enabled, status, duration_seconds, metrics, 
                     tags, notes, export_formats)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    session.session_id,
                    session.user_id,
                    session.created_at,
                    session.updated_at,
                    session.ended_at,
                    session.model_type,
                    session.language,
                    json.dumps(session.features_enabled),
                    session.status,
                    session.duration_seconds,
                    json.dumps(asdict(session.metrics)),
                    json.dumps(session.tags),
                    session.notes,
                    json.dumps(session.export_formats)
                ))
                
                # Save transcriptions
                for transcription in session.transcriptions:
                    conn.execute("""
                        INSERT OR REPLACE INTO transcriptions
                        (session_id, timestamp, text, confidence, is_final, 
                         processing_time_ms, language, emotion, pauses, 
                         filler_words, audio_quality)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        session.session_id,
                        transcription.timestamp,
                        transcription.text,
                        transcription.confidence,
                        transcription.is_final,
                        transcription.processing_time_ms,
                        transcription.language,
                        json.dumps(transcription.emotion) if transcription.emotion else None,
                        json.dumps(transcription.pauses) if transcription.pauses else None,
                        json.dumps(transcription.filler_words) if transcription.filler_words else None,
                        transcription.audio_quality
                    ))
                
        except Exception as e:
            self.logger.error(f"Error saving session: {str(e)}")
    
    def load_session(self, session_id: str) -> Optional[SessionInfo]:
        """Load session from database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                # Load session
                cursor = conn.execute("""
                    SELECT * FROM sessions WHERE session_id = ?
                """, (session_id,))
                
                row = cursor.fetchone()
                if not row:
                    return None
                
                # Load transcriptions
                transcriptions = []
                cursor = conn.execute("""
                    SELECT * FROM transcriptions WHERE session_id = ? ORDER BY timestamp
                """, (session_id,))
                
                for trans_row in cursor.fetchall():
                    transcription = TranscriptionEntry(
                        timestamp=trans_row['timestamp'],
                        text=trans_row['text'],
                        confidence=trans_row['confidence'],
                        is_final=bool(trans_row['is_final']),
                        processing_time_ms=trans_row['processing_time_ms'],
                        language=trans_row['language'],
                        emotion=json.loads(trans_row['emotion']) if trans_row['emotion'] else None,
                        pauses=json.loads(trans_row['pauses']) if trans_row['pauses'] else None,
                        filler_words=json.loads(trans_row['filler_words']) if trans_row['filler_words'] else None,
                        audio_quality=trans_row['audio_quality']
                    )
                    transcriptions.append(transcription)
                
                # Create session
                session = SessionInfo(
                    session_id=row['session_id'],
                    user_id=row['user_id'],
                    created_at=datetime.fromisoformat(row['created_at']),
                    updated_at=datetime.fromisoformat(row['updated_at']),
                    ended_at=datetime.fromisoformat(row['ended_at']) if row['ended_at'] else None,
                    model_type=row['model_type'],
                    language=row['language'],
                    features_enabled=json.loads(row['features_enabled']),
                    status=row['status'],
                    duration_seconds=row['duration_seconds'],
                    transcriptions=transcriptions,
                    metrics=SessionMetrics(**json.loads(row['metrics'])),
                    tags=json.loads(row['tags']),
                    notes=row['notes'],
                    export_formats=json.loads(row['export_formats'])
                )
                
                return session
                
        except Exception as e:
            self.logger.error(f"Error loading session: {str(e)}")
            return None
    
    def list_sessions(self, user_id: Optional[str] = None, limit: int = 100, 
                     offset: int = 0) -> List[Dict[str, Any]]:
        """List sessions with optional filtering"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                query = "SELECT * FROM sessions"
                params = []
                
                if user_id:
                    query += " WHERE user_id = ?"
                    params.append(user_id)
                
                query += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
                params.extend([limit, offset])
                
                cursor = conn.execute(query, params)
                
                sessions = []
                for row in cursor.fetchall():
                    session_dict = dict(row)
                    session_dict['features_enabled'] = json.loads(session_dict['features_enabled'])
                    session_dict['metrics'] = json.loads(session_dict['metrics'])
                    session_dict['tags'] = json.loads(session_dict['tags'])
                    session_dict['export_formats'] = json.loads(session_dict['export_formats'])
                    sessions.append(session_dict)
                
                return sessions
                
        except Exception as e:
            self.logger.error(f"Error listing sessions: {str(e)}")
            return []
    
    def delete_session(self, session_id: str) -> bool:
        """Delete session and its transcriptions"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("DELETE FROM transcriptions WHERE session_id = ?", (session_id,))
                conn.execute("DELETE FROM sessions WHERE session_id = ?", (session_id,))
                return True
                
        except Exception as e:
            self.logger.error(f"Error deleting session: {str(e)}")
            return False

class AdvancedSessionManager:
    """Advanced session management with analytics and visualization"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Database
        self.database = SessionDatabase()
        
        # Active sessions
        self.active_sessions: Dict[str, SessionInfo] = {}
        self.session_lock = threading.Lock()
        
        # Analytics cache
        self.analytics_cache = {}
        self.cache_expiry = 300  # 5 minutes
        self.last_cache_update = 0
        
        # Statistics
        self.total_sessions_created = 0
        self.total_transcriptions_processed = 0
        
        self.logger.info("AdvancedSessionManager initialized")
    
    def create_session(self, user_id: Optional[str] = None, model_type: str = "medium.en",
                      language: str = "en", features_enabled: Optional[Dict[str, bool]] = None,
                      tags: Optional[List[str]] = None) -> str:
        """Create a new session"""
        try:
            session_id = f"sess_{uuid.uuid4().hex[:12]}"
            
            session = SessionInfo(
                session_id=session_id,
                user_id=user_id,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                ended_at=None,
                model_type=model_type,
                language=language,
                features_enabled=features_enabled or {
                    'emotion_detection': True,
                    'pause_detection': True,
                    'filler_detection': True
                },
                status='active',
                tags=tags or []
            )
            
            with self.session_lock:
                self.active_sessions[session_id] = session
                self.total_sessions_created += 1
            
            # Save to database
            self.database.save_session(session)
            
            self.logger.info(f"Session created: {session_id}")
            return session_id
            
        except Exception as e:
            self.logger.error(f"Error creating session: {str(e)}")
            raise
    
    def add_transcription(self, session_id: str, transcription: TranscriptionEntry):
        """Add transcription to session"""
        try:
            with self.session_lock:
                if session_id not in self.active_sessions:
                    # Try to load from database
                    session = self.database.load_session(session_id)
                    if session:
                        self.active_sessions[session_id] = session
                    else:
                        raise ValueError(f"Session not found: {session_id}")
                
                session = self.active_sessions[session_id]
                session.transcriptions.append(transcription)
                session.updated_at = datetime.now()
                
                # Update metrics
                self._update_session_metrics(session)
                self.total_transcriptions_processed += 1
            
            # Save to database periodically
            if len(session.transcriptions) % 10 == 0:
                self.database.save_session(session)
            
        except Exception as e:
            self.logger.error(f"Error adding transcription: {str(e)}")
    
    def _update_session_metrics(self, session: SessionInfo):
        """Update session metrics based on transcriptions"""
        if not session.transcriptions:
            return
        
        transcriptions = session.transcriptions
        final_transcriptions = [t for t in transcriptions if t.is_final]
        
        # Basic metrics
        session.metrics.total_transcriptions = len(transcriptions)
        session.metrics.final_transcriptions = len(final_transcriptions)
        
        if transcriptions:
            session.metrics.average_confidence = np.mean([t.confidence for t in transcriptions])
            session.metrics.average_processing_time_ms = np.mean([t.processing_time_ms for t in transcriptions])
            session.metrics.total_processing_time_ms = sum(t.processing_time_ms for t in transcriptions)
        
        # Language statistics
        language_counts = defaultdict(int)
        for t in transcriptions:
            language_counts[t.language] += 1
        session.metrics.languages_detected = dict(language_counts)
        
        # Emotion statistics
        emotion_counts = defaultdict(int)
        for t in transcriptions:
            if t.emotion and 'primary_emotion' in t.emotion:
                emotion_counts[t.emotion['primary_emotion']] += 1
        
        session.metrics.emotions_detected = dict(emotion_counts)
        if emotion_counts:
            session.metrics.dominant_emotion = max(emotion_counts, key=emotion_counts.get)
        
        # Calculate words per minute
        if final_transcriptions and session.duration_seconds > 0:
            total_words = sum(len(t.text.split()) for t in final_transcriptions)
            session.metrics.words_per_minute = (total_words / session.duration_seconds) * 60
        
        # Audio quality metrics
        quality_scores = [t.audio_quality for t in transcriptions if t.audio_quality is not None]
        if quality_scores:
            session.metrics.average_audio_quality = np.mean(quality_scores)
        
        # Performance metrics
        processing_times = [t.processing_time_ms for t in transcriptions]
        if processing_times:
            session.metrics.latency_p95_ms = np.percentile(processing_times, 95)
    
    def end_session(self, session_id: str) -> bool:
        """End a session"""
        try:
            with self.session_lock:
                if session_id not in self.active_sessions:
                    return False
                
                session = self.active_sessions[session_id]
                session.status = 'completed'
                session.ended_at = datetime.now()
                session.duration_seconds = (session.ended_at - session.created_at).total_seconds()
                
                # Final metrics update
                self._update_session_metrics(session)
            
            # Save to database
            self.database.save_session(session)
            
            # Remove from active sessions
            with self.session_lock:
                del self.active_sessions[session_id]
            
            self.logger.info(f"Session ended: {session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error ending session: {str(e)}")
            return False
    
    def get_session(self, session_id: str) -> Optional[SessionInfo]:
        """Get session information"""
        with self.session_lock:
            if session_id in self.active_sessions:
                return self.active_sessions[session_id]
        
        # Try to load from database
        return self.database.load_session(session_id)
    
    def list_sessions(self, user_id: Optional[str] = None, limit: int = 100,
                     offset: int = 0) -> List[Dict[str, Any]]:
        """List sessions"""
        return self.database.list_sessions(user_id, limit, offset)
    
    def get_analytics_dashboard(self, user_id: Optional[str] = None,
                               days: int = 30) -> Dict[str, Any]:
        """Generate comprehensive analytics dashboard"""
        try:
            # Check cache
            cache_key = f"{user_id}_{days}"
            current_time = time.time()
            
            if (cache_key in self.analytics_cache and 
                current_time - self.last_cache_update < self.cache_expiry):
                return self.analytics_cache[cache_key]
            
            # Generate analytics
            sessions = self.database.list_sessions(user_id, limit=1000)
            
            # Filter by date range
            cutoff_date = datetime.now() - timedelta(days=days)
            recent_sessions = [s for s in sessions if 
                             datetime.fromisoformat(s['created_at']) >= cutoff_date]
            
            analytics = {
                'summary': self._generate_summary_analytics(recent_sessions),
                'performance': self._generate_performance_analytics(recent_sessions),
                'language': self._generate_language_analytics(recent_sessions),
                'emotion': self._generate_emotion_analytics(recent_sessions),
                'trends': self._generate_trend_analytics(recent_sessions),
                'quality': self._generate_quality_analytics(recent_sessions)
            }
            
            # Cache results
            self.analytics_cache[cache_key] = analytics
            self.last_cache_update = current_time
            
            return analytics
            
        except Exception as e:
            self.logger.error(f"Error generating analytics: {str(e)}")
            return {}
    
    def _generate_summary_analytics(self, sessions: List[Dict]) -> Dict[str, Any]:
        """Generate summary analytics"""
        if not sessions:
            return {}
        
        total_sessions = len(sessions)
        total_duration = sum(s['duration_seconds'] for s in sessions)
        total_transcriptions = sum(s['metrics']['total_transcriptions'] for s in sessions)
        
        avg_confidence = np.mean([s['metrics']['average_confidence'] for s in sessions if s['metrics']['average_confidence'] > 0])
        avg_processing_time = np.mean([s['metrics']['average_processing_time_ms'] for s in sessions if s['metrics']['average_processing_time_ms'] > 0])
        
        return {
            'total_sessions': total_sessions,
            'total_duration_hours': total_duration / 3600,
            'total_transcriptions': total_transcriptions,
            'average_session_duration_minutes': (total_duration / total_sessions) / 60 if total_sessions > 0 else 0,
            'average_confidence': float(avg_confidence) if not np.isnan(avg_confidence) else 0,
            'average_processing_time_ms': float(avg_processing_time) if not np.isnan(avg_processing_time) else 0,
            'transcriptions_per_session': total_transcriptions / total_sessions if total_sessions > 0 else 0
        }
    
    def _generate_performance_analytics(self, sessions: List[Dict]) -> Dict[str, Any]:
        """Generate performance analytics"""
        if not sessions:
            return {}
        
        processing_times = []
        confidences = []
        latencies = []
        
        for session in sessions:
            metrics = session['metrics']
            if metrics['average_processing_time_ms'] > 0:
                processing_times.append(metrics['average_processing_time_ms'])
            if metrics['average_confidence'] > 0:
                confidences.append(metrics['average_confidence'])
            if metrics['latency_p95_ms'] > 0:
                latencies.append(metrics['latency_p95_ms'])
        
        return {
            'processing_time_stats': {
                'mean': float(np.mean(processing_times)) if processing_times else 0,
                'median': float(np.median(processing_times)) if processing_times else 0,
                'p95': float(np.percentile(processing_times, 95)) if processing_times else 0,
                'std': float(np.std(processing_times)) if processing_times else 0
            },
            'confidence_stats': {
                'mean': float(np.mean(confidences)) if confidences else 0,
                'median': float(np.median(confidences)) if confidences else 0,
                'min': float(np.min(confidences)) if confidences else 0,
                'max': float(np.max(confidences)) if confidences else 0
            },
            'latency_stats': {
                'mean': float(np.mean(latencies)) if latencies else 0,
                'p95': float(np.percentile(latencies, 95)) if latencies else 0,
                'p99': float(np.percentile(latencies, 99)) if latencies else 0
            }
        }
    
    def _generate_language_analytics(self, sessions: List[Dict]) -> Dict[str, Any]:
        """Generate language usage analytics"""
        language_counts = defaultdict(int)
        language_switches = 0
        
        for session in sessions:
            metrics = session['metrics']
            for lang, count in metrics['languages_detected'].items():
                language_counts[lang] += count
            language_switches += metrics.get('language_switches', 0)
        
        return {
            'language_distribution': dict(language_counts),
            'total_language_switches': language_switches,
            'most_common_language': max(language_counts, key=language_counts.get) if language_counts else None,
            'languages_used': len(language_counts)
        }
    
    def _generate_emotion_analytics(self, sessions: List[Dict]) -> Dict[str, Any]:
        """Generate emotion analytics"""
        emotion_counts = defaultdict(int)
        dominant_emotions = defaultdict(int)
        
        for session in sessions:
            metrics = session['metrics']
            for emotion, count in metrics['emotions_detected'].items():
                emotion_counts[emotion] += count
            
            if metrics.get('dominant_emotion'):
                dominant_emotions[metrics['dominant_emotion']] += 1
        
        return {
            'emotion_distribution': dict(emotion_counts),
            'dominant_emotion_distribution': dict(dominant_emotions),
            'most_common_emotion': max(emotion_counts, key=emotion_counts.get) if emotion_counts else None,
            'emotional_diversity': len(emotion_counts)
        }
    
    def _generate_trend_analytics(self, sessions: List[Dict]) -> Dict[str, Any]:
        """Generate trend analytics"""
        # Group sessions by day
        daily_stats = defaultdict(lambda: {
            'sessions': 0,
            'transcriptions': 0,
            'duration': 0,
            'avg_confidence': []
        })
        
        for session in sessions:
            date = datetime.fromisoformat(session['created_at']).date()
            daily_stats[date]['sessions'] += 1
            daily_stats[date]['transcriptions'] += session['metrics']['total_transcriptions']
            daily_stats[date]['duration'] += session['duration_seconds']
            if session['metrics']['average_confidence'] > 0:
                daily_stats[date]['avg_confidence'].append(session['metrics']['average_confidence'])
        
        # Convert to time series
        dates = sorted(daily_stats.keys())
        trends = {
            'dates': [str(date) for date in dates],
            'daily_sessions': [daily_stats[date]['sessions'] for date in dates],
            'daily_transcriptions': [daily_stats[date]['transcriptions'] for date in dates],
            'daily_duration_hours': [daily_stats[date]['duration'] / 3600 for date in dates],
            'daily_avg_confidence': [
                np.mean(daily_stats[date]['avg_confidence']) if daily_stats[date]['avg_confidence'] else 0
                for date in dates
            ]
        }
        
        return trends
    
    def _generate_quality_analytics(self, sessions: List[Dict]) -> Dict[str, Any]:
        """Generate quality analytics"""
        quality_scores = []
        error_rates = []
        
        for session in sessions:
            metrics = session['metrics']
            if metrics.get('average_audio_quality', 0) > 0:
                quality_scores.append(metrics['average_audio_quality'])
            if metrics.get('error_rate', 0) > 0:
                error_rates.append(metrics['error_rate'])
        
        return {
            'audio_quality_stats': {
                'mean': float(np.mean(quality_scores)) if quality_scores else 0,
                'median': float(np.median(quality_scores)) if quality_scores else 0,
                'std': float(np.std(quality_scores)) if quality_scores else 0
            },
            'error_rate_stats': {
                'mean': float(np.mean(error_rates)) if error_rates else 0,
                'median': float(np.median(error_rates)) if error_rates else 0,
                'max': float(np.max(error_rates)) if error_rates else 0
            },
            'quality_distribution': self._calculate_quality_distribution(quality_scores)
        }
    
    def _calculate_quality_distribution(self, scores: List[float]) -> Dict[str, int]:
        """Calculate quality score distribution"""
        if not scores:
            return {}
        
        distribution = {'excellent': 0, 'good': 0, 'fair': 0, 'poor': 0}
        
        for score in scores:
            if score >= 0.9:
                distribution['excellent'] += 1
            elif score >= 0.7:
                distribution['good'] += 1
            elif score >= 0.5:
                distribution['fair'] += 1
            else:
                distribution['poor'] += 1
        
        return distribution
    
    def export_session_data(self, session_id: str, format_type: str = 'json') -> Optional[str]:
        """Export session data in various formats"""
        try:
            session = self.get_session(session_id)
            if not session:
                return None
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if format_type == 'json':
                filename = f"session_{session_id}_{timestamp}.json"
                filepath = DATA_DIR / "exports" / filename
                filepath.parent.mkdir(parents=True, exist_ok=True)
                
                with open(filepath, 'w') as f:
                    json.dump(asdict(session), f, indent=2, default=str)
                
                return str(filepath)
            
            elif format_type == 'csv':
                filename = f"session_{session_id}_{timestamp}.csv"
                filepath = DATA_DIR / "exports" / filename
                filepath.parent.mkdir(parents=True, exist_ok=True)
                
                # Convert transcriptions to DataFrame
                transcriptions_data = []
                for t in session.transcriptions:
                    transcriptions_data.append({
                        'timestamp': t.timestamp,
                        'text': t.text,
                        'confidence': t.confidence,
                        'is_final': t.is_final,
                        'processing_time_ms': t.processing_time_ms,
                        'language': t.language,
                        'emotion': t.emotion.get('primary_emotion') if t.emotion else None,
                        'emotion_confidence': t.emotion.get('confidence') if t.emotion else None,
                        'audio_quality': t.audio_quality
                    })
                
                df = pd.DataFrame(transcriptions_data)
                df.to_csv(filepath, index=False)
                
                return str(filepath)
            
            else:
                self.logger.warning(f"Unsupported export format: {format_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error exporting session data: {str(e)}")
            return None
    
    def get_manager_statistics(self) -> Dict[str, Any]:
        """Get session manager statistics"""
        with self.session_lock:
            active_count = len(self.active_sessions)
        
        return {
            'total_sessions_created': self.total_sessions_created,
            'active_sessions': active_count,
            'total_transcriptions_processed': self.total_transcriptions_processed,
            'cache_entries': len(self.analytics_cache),
            'database_path': str(self.database.db_path)
        }

if __name__ == "__main__":
    # Test session manager
    manager = AdvancedSessionManager()
    
    # Create test session
    session_id = manager.create_session(
        user_id="test_user",
        model_type="medium.en",
        language="en",
        tags=["test", "demo"]
    )
    
    # Add test transcriptions
    for i in range(10):
        transcription = TranscriptionEntry(
            timestamp=time.time(),
            text=f"This is test transcription number {i+1}",
            confidence=0.9 + (i % 3) * 0.03,
            is_final=i % 2 == 0,
            processing_time_ms=100 + i * 10,
            language="en",
            emotion={'primary_emotion': 'neutral', 'confidence': 0.8},
            audio_quality=0.85 + (i % 4) * 0.03
        )
        manager.add_transcription(session_id, transcription)
    
    # End session
    manager.end_session(session_id)
    
    # Get analytics
    analytics = manager.get_analytics_dashboard()
    print(f"Analytics: {analytics}")
    
    # Export session
    export_path = manager.export_session_data(session_id, 'json')
    print(f"Session exported to: {export_path}")
    
    print("Advanced session management test completed!")
