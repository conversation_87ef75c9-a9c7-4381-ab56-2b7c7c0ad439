"""
WebSocket API for Real-time Communication
Provides real-time bidirectional communication for live transcription streaming
"""

import asyncio
import json
import time
import uuid
from typing import Dict, List, Set, Any, Optional
from datetime import datetime
import threading

from fastapi import WebSocket, WebSocketDisconnect, HTTPException
from fastapi.routing import API<PERSON>outer
import websockets

from .models import (
    WebSocketMessage, TranscriptionUpdateMessage, EmotionUpdateMessage,
    StatusUpdateMessage, ErrorMessage, SystemMessage, MessageType,
    TranscriptionResult, EmotionResult
)
from config import APIConfig
from utils import get_logger

logger = get_logger(__name__)

class ConnectionManager:
    """Manages WebSocket connections and message broadcasting"""
    
    def __init__(self):
        # Active connections
        self.active_connections: Dict[str, WebSocket] = {}
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        self.connections_lock = threading.Lock()
        
        # Message queues for each connection
        self.message_queues: Dict[str, asyncio.Queue] = {}
        
        # Statistics
        self.total_connections = 0
        self.messages_sent = 0
        self.messages_received = 0
        
        logger.info("WebSocket ConnectionManager initialized")
    
    async def connect(self, websocket: WebSocket, client_id: str = None) -> str:
        """Accept a new WebSocket connection"""
        await websocket.accept()
        
        # Generate client ID if not provided
        if not client_id:
            client_id = f"client_{uuid.uuid4().hex[:8]}"
        
        with self.connections_lock:
            self.active_connections[client_id] = websocket
            self.connection_metadata[client_id] = {
                'connected_at': datetime.now(),
                'last_activity': datetime.now(),
                'messages_sent': 0,
                'messages_received': 0
            }
            self.message_queues[client_id] = asyncio.Queue()
            self.total_connections += 1
        
        logger.info(f"WebSocket client connected: {client_id}")
        
        # Send welcome message
        await self.send_system_message(
            client_id,
            "connection_established",
            {"client_id": client_id, "server_time": datetime.now().isoformat()}
        )
        
        return client_id
    
    def disconnect(self, client_id: str):
        """Remove a WebSocket connection"""
        with self.connections_lock:
            if client_id in self.active_connections:
                del self.active_connections[client_id]
                del self.connection_metadata[client_id]
                if client_id in self.message_queues:
                    del self.message_queues[client_id]
        
        logger.info(f"WebSocket client disconnected: {client_id}")
    
    async def send_personal_message(self, client_id: str, message: Dict[str, Any]):
        """Send message to a specific client"""
        with self.connections_lock:
            if client_id not in self.active_connections:
                logger.warning(f"Attempted to send message to disconnected client: {client_id}")
                return False
            
            websocket = self.active_connections[client_id]
        
        try:
            await websocket.send_text(json.dumps(message, default=str))
            
            # Update statistics
            with self.connections_lock:
                if client_id in self.connection_metadata:
                    self.connection_metadata[client_id]['messages_sent'] += 1
                    self.connection_metadata[client_id]['last_activity'] = datetime.now()
            
            self.messages_sent += 1
            return True
            
        except Exception as e:
            logger.error(f"Error sending message to {client_id}: {str(e)}")
            self.disconnect(client_id)
            return False
    
    async def broadcast(self, message: Dict[str, Any], exclude: Optional[Set[str]] = None):
        """Broadcast message to all connected clients"""
        exclude = exclude or set()
        
        with self.connections_lock:
            client_ids = list(self.active_connections.keys())
        
        # Send to all clients (except excluded ones)
        tasks = []
        for client_id in client_ids:
            if client_id not in exclude:
                tasks.append(self.send_personal_message(client_id, message))
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def send_transcription_update(self, client_id: str, result: TranscriptionResult):
        """Send transcription update to client"""
        message = TranscriptionUpdateMessage(data=result)
        await self.send_personal_message(client_id, message.dict())
    
    async def send_emotion_update(self, client_id: str, emotion: EmotionResult):
        """Send emotion update to client"""
        message = EmotionUpdateMessage(data=emotion)
        await self.send_personal_message(client_id, message.dict())
    
    async def send_status_update(self, client_id: str, status: Dict[str, Any]):
        """Send status update to client"""
        from .models import SystemStatus
        status_obj = SystemStatus(**status)
        message = StatusUpdateMessage(data=status_obj)
        await self.send_personal_message(client_id, message.dict())
    
    async def send_error_message(self, client_id: str, error: str, details: str):
        """Send error message to client"""
        from .models import ErrorResponse
        error_obj = ErrorResponse(error=error, message=details)
        message = ErrorMessage(data=error_obj)
        await self.send_personal_message(client_id, message.dict())
    
    async def send_system_message(self, client_id: str, message_type: str, data: Dict[str, Any]):
        """Send system message to client"""
        message = SystemMessage(data={"type": message_type, **data})
        await self.send_personal_message(client_id, message.dict())
    
    def get_connection_count(self) -> int:
        """Get number of active connections"""
        with self.connections_lock:
            return len(self.active_connections)
    
    def get_connection_info(self, client_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific connection"""
        with self.connections_lock:
            if client_id in self.connection_metadata:
                return self.connection_metadata[client_id].copy()
        return None
    
    def get_all_connections_info(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all connections"""
        with self.connections_lock:
            return {
                client_id: metadata.copy()
                for client_id, metadata in self.connection_metadata.items()
            }
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get WebSocket statistics"""
        return {
            'active_connections': self.get_connection_count(),
            'total_connections': self.total_connections,
            'messages_sent': self.messages_sent,
            'messages_received': self.messages_received
        }

class WebSocketManager:
    """Main WebSocket manager for the application"""
    
    def __init__(self):
        self.connection_manager = ConnectionManager()
        self.voice_processor = None
        self.status_monitor = None
        
        # Message handlers
        self.message_handlers = {
            'ping': self._handle_ping,
            'start_transcription': self._handle_start_transcription,
            'stop_transcription': self._handle_stop_transcription,
            'get_status': self._handle_get_status,
            'configure': self._handle_configure
        }
        
        logger.info("WebSocketManager initialized")
    
    def set_components(self, voice_processor, status_monitor):
        """Set system components"""
        self.voice_processor = voice_processor
        self.status_monitor = status_monitor
        logger.info("WebSocket components set")
    
    async def handle_connection(self, websocket: WebSocket, client_id: str = None):
        """Handle a new WebSocket connection"""
        client_id = await self.connection_manager.connect(websocket, client_id)
        
        try:
            # Start heartbeat task
            heartbeat_task = asyncio.create_task(self._heartbeat_loop(client_id))
            
            # Handle incoming messages
            while True:
                try:
                    # Receive message with timeout
                    data = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                    await self._handle_message(client_id, data)
                    
                except asyncio.TimeoutError:
                    # Send ping to check if connection is alive
                    await self.connection_manager.send_system_message(
                        client_id, "ping", {"timestamp": time.time()}
                    )
                    
                except WebSocketDisconnect:
                    break
                    
        except Exception as e:
            logger.error(f"Error handling WebSocket connection {client_id}: {str(e)}")
            await self.connection_manager.send_error_message(
                client_id, "ConnectionError", str(e)
            )
        finally:
            # Cleanup
            heartbeat_task.cancel()
            self.connection_manager.disconnect(client_id)
    
    async def _handle_message(self, client_id: str, data: str):
        """Handle incoming WebSocket message"""
        try:
            message = json.loads(data)
            message_type = message.get('type')
            
            if message_type in self.message_handlers:
                await self.message_handlers[message_type](client_id, message)
            else:
                await self.connection_manager.send_error_message(
                    client_id, "UnknownMessageType", f"Unknown message type: {message_type}"
                )
            
            # Update statistics
            self.connection_manager.messages_received += 1
            
        except json.JSONDecodeError:
            await self.connection_manager.send_error_message(
                client_id, "InvalidJSON", "Invalid JSON message format"
            )
        except Exception as e:
            logger.error(f"Error handling message from {client_id}: {str(e)}")
            await self.connection_manager.send_error_message(
                client_id, "MessageHandlingError", str(e)
            )
    
    async def _handle_ping(self, client_id: str, message: Dict[str, Any]):
        """Handle ping message"""
        await self.connection_manager.send_system_message(
            client_id, "pong", {"timestamp": time.time()}
        )
    
    async def _handle_start_transcription(self, client_id: str, message: Dict[str, Any]):
        """Handle start transcription request"""
        try:
            # Start transcription (implementation depends on voice processor)
            await self.connection_manager.send_system_message(
                client_id, "transcription_started", {"client_id": client_id}
            )
        except Exception as e:
            await self.connection_manager.send_error_message(
                client_id, "TranscriptionStartError", str(e)
            )
    
    async def _handle_stop_transcription(self, client_id: str, message: Dict[str, Any]):
        """Handle stop transcription request"""
        try:
            # Stop transcription (implementation depends on voice processor)
            await self.connection_manager.send_system_message(
                client_id, "transcription_stopped", {"client_id": client_id}
            )
        except Exception as e:
            await self.connection_manager.send_error_message(
                client_id, "TranscriptionStopError", str(e)
            )
    
    async def _handle_get_status(self, client_id: str, message: Dict[str, Any]):
        """Handle get status request"""
        try:
            if self.status_monitor:
                status = self.status_monitor.get_status_summary()
                await self.connection_manager.send_status_update(client_id, status)
            else:
                await self.connection_manager.send_error_message(
                    client_id, "StatusUnavailable", "Status monitor not available"
                )
        except Exception as e:
            await self.connection_manager.send_error_message(
                client_id, "StatusError", str(e)
            )
    
    async def _handle_configure(self, client_id: str, message: Dict[str, Any]):
        """Handle configuration update request"""
        try:
            config_data = message.get('data', {})
            # Apply configuration (implementation depends on system components)
            await self.connection_manager.send_system_message(
                client_id, "configuration_updated", config_data
            )
        except Exception as e:
            await self.connection_manager.send_error_message(
                client_id, "ConfigurationError", str(e)
            )
    
    async def _heartbeat_loop(self, client_id: str):
        """Send periodic heartbeat messages"""
        try:
            while True:
                await asyncio.sleep(APIConfig.WS_HEARTBEAT_INTERVAL)
                await self.connection_manager.send_system_message(
                    client_id, "heartbeat", {"timestamp": time.time()}
                )
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"Error in heartbeat loop for {client_id}: {str(e)}")
    
    async def broadcast_transcription_update(self, result: TranscriptionResult):
        """Broadcast transcription update to all clients"""
        message = TranscriptionUpdateMessage(data=result)
        await self.connection_manager.broadcast(message.dict())
    
    async def broadcast_emotion_update(self, emotion: EmotionResult):
        """Broadcast emotion update to all clients"""
        message = EmotionUpdateMessage(data=emotion)
        await self.connection_manager.broadcast(message.dict())
    
    async def broadcast_status_update(self, status: Dict[str, Any]):
        """Broadcast status update to all clients"""
        from .models import SystemStatus
        status_obj = SystemStatus(**status)
        message = StatusUpdateMessage(data=status_obj)
        await self.connection_manager.broadcast(message.dict())
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get WebSocket statistics"""
        return self.connection_manager.get_statistics()

# Global WebSocket manager instance
websocket_manager = WebSocketManager()

# WebSocket router
router = APIRouter()

@router.websocket("/ws/transcribe")
async def websocket_transcribe_endpoint(websocket: WebSocket):
    """Main WebSocket endpoint for real-time transcription"""
    await websocket_manager.handle_connection(websocket)

@router.websocket("/ws/transcribe/{client_id}")
async def websocket_transcribe_with_id_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket endpoint with custom client ID"""
    await websocket_manager.handle_connection(websocket, client_id)

# Function to initialize WebSocket components
def initialize_websocket_components(voice_processor, status_monitor):
    """Initialize WebSocket manager with system components"""
    websocket_manager.set_components(voice_processor, status_monitor)
    logger.info("WebSocket components initialized")
