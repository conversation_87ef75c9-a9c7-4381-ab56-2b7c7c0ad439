"""
API Data Models
Pydantic models for request/response validation and API documentation
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field, validator

class TranscriptionStatus(str, Enum):
    """Transcription session status"""
    IDLE = "idle"
    RECORDING = "recording"
    PROCESSING = "processing"
    COMPLETED = "completed"
    ERROR = "error"
    STOPPED = "stopped"

class EmotionType(str, Enum):
    """Supported emotion types"""
    NEUTRAL = "neutral"
    HAPPY = "happy"
    SAD = "sad"
    ANGRY = "angry"
    EXCITED = "excited"
    FRUSTRATED = "frustrated"
    SURPRISED = "surprised"

class MessageType(str, Enum):
    """WebSocket message types"""
    TRANSCRIPTION_UPDATE = "transcription_update"
    TRANSCRIPTION_FINAL = "transcription_final"
    EMOTION_UPDATE = "emotion_update"
    STATUS_UPDATE = "status_update"
    ERROR = "error"
    SYSTEM_MESSAGE = "system_message"

# Request Models

class StartTranscriptionRequest(BaseModel):
    """Request to start transcription session"""
    model_type: Optional[str] = Field(default="medium.en", description="Whisper model to use")
    language: Optional[str] = Field(default="en", description="Language code")
    enable_emotion_detection: bool = Field(default=True, description="Enable emotion detection")
    enable_pause_detection: bool = Field(default=True, description="Enable pause detection")
    enable_filler_detection: bool = Field(default=True, description="Enable filler word detection")
    sensitivity: Optional[float] = Field(default=0.7, ge=0.0, le=1.0, description="VAD sensitivity")
    
    class Config:
        schema_extra = {
            "example": {
                "model_type": "medium.en",
                "language": "en",
                "enable_emotion_detection": True,
                "enable_pause_detection": True,
                "enable_filler_detection": True,
                "sensitivity": 0.7
            }
        }

class ConfigurationRequest(BaseModel):
    """Request to update system configuration"""
    vad_sensitivity: Optional[float] = Field(None, ge=0.0, le=1.0)
    emotion_detection: Optional[bool] = None
    pause_detection: Optional[bool] = None
    filler_detection: Optional[bool] = None
    model_type: Optional[str] = None
    language: Optional[str] = None

# Response Models

class EmotionResult(BaseModel):
    """Emotion detection result"""
    primary_emotion: EmotionType = Field(description="Primary detected emotion")
    confidence: float = Field(ge=0.0, le=1.0, description="Confidence score")
    all_emotions: Dict[str, float] = Field(description="All emotion scores")
    processing_time_ms: float = Field(description="Processing time in milliseconds")
    
    class Config:
        schema_extra = {
            "example": {
                "primary_emotion": "happy",
                "confidence": 0.85,
                "all_emotions": {
                    "happy": 0.85,
                    "neutral": 0.10,
                    "excited": 0.05
                },
                "processing_time_ms": 15.2
            }
        }

class PauseInfo(BaseModel):
    """Pause information"""
    duration: float = Field(description="Pause duration in seconds")
    pause_type: str = Field(description="Type of pause (short, medium, long)")
    position: Optional[int] = Field(None, description="Position in text")

class FillerWord(BaseModel):
    """Detected filler word"""
    word: str = Field(description="The filler word")
    position: int = Field(description="Position in text")
    confidence: float = Field(ge=0.0, le=1.0, description="Detection confidence")

class TranscriptionResult(BaseModel):
    """Transcription result"""
    text: str = Field(description="Transcribed text")
    confidence: float = Field(ge=0.0, le=1.0, description="Transcription confidence")
    is_final: bool = Field(description="Whether this is a final result")
    timestamp: datetime = Field(description="Timestamp of transcription")
    processing_time_ms: float = Field(description="Processing time in milliseconds")
    
    # Optional metadata
    emotion: Optional[EmotionResult] = Field(None, description="Emotion detection result")
    pauses: Optional[List[PauseInfo]] = Field(None, description="Detected pauses")
    filler_words: Optional[List[FillerWord]] = Field(None, description="Detected filler words")
    
    class Config:
        schema_extra = {
            "example": {
                "text": "Hello, how are you doing today?",
                "confidence": 0.95,
                "is_final": True,
                "timestamp": "2024-01-15T10:30:00Z",
                "processing_time_ms": 125.5,
                "emotion": {
                    "primary_emotion": "happy",
                    "confidence": 0.85,
                    "all_emotions": {"happy": 0.85, "neutral": 0.15},
                    "processing_time_ms": 15.2
                },
                "pauses": [
                    {
                        "duration": 0.5,
                        "pause_type": "short",
                        "position": 6
                    }
                ],
                "filler_words": []
            }
        }

class TranscriptionSession(BaseModel):
    """Transcription session information"""
    session_id: str = Field(description="Unique session identifier")
    status: TranscriptionStatus = Field(description="Current session status")
    created_at: datetime = Field(description="Session creation time")
    updated_at: datetime = Field(description="Last update time")
    
    # Configuration
    model_type: str = Field(description="Whisper model being used")
    language: str = Field(description="Language code")
    features_enabled: Dict[str, bool] = Field(description="Enabled features")
    
    # Statistics
    total_transcriptions: int = Field(default=0, description="Total transcriptions in session")
    total_processing_time_ms: float = Field(default=0.0, description="Total processing time")
    average_confidence: float = Field(default=0.0, description="Average confidence score")
    
    class Config:
        schema_extra = {
            "example": {
                "session_id": "sess_123456789",
                "status": "recording",
                "created_at": "2024-01-15T10:00:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "model_type": "medium.en",
                "language": "en",
                "features_enabled": {
                    "emotion_detection": True,
                    "pause_detection": True,
                    "filler_detection": True
                },
                "total_transcriptions": 25,
                "total_processing_time_ms": 3250.5,
                "average_confidence": 0.92
            }
        }

class SystemStatus(BaseModel):
    """System status information"""
    status: str = Field(description="Overall system status")
    uptime_seconds: float = Field(description="System uptime in seconds")
    
    # Performance metrics
    cpu_usage: float = Field(description="CPU usage percentage")
    memory_usage_mb: float = Field(description="Memory usage in MB")
    gpu_memory_mb: float = Field(description="GPU memory usage in MB")
    gpu_utilization: float = Field(description="GPU utilization percentage")
    
    # Processing metrics
    current_latency_ms: float = Field(description="Current processing latency")
    average_latency_ms: float = Field(description="Average processing latency")
    total_transcriptions: int = Field(description="Total transcriptions processed")
    
    # Health indicators
    health_score: float = Field(ge=0.0, le=100.0, description="Overall health score")
    active_sessions: int = Field(description="Number of active sessions")
    
    class Config:
        schema_extra = {
            "example": {
                "status": "healthy",
                "uptime_seconds": 3600.5,
                "cpu_usage": 45.2,
                "memory_usage_mb": 2048.5,
                "gpu_memory_mb": 3072.0,
                "gpu_utilization": 65.8,
                "current_latency_ms": 125.5,
                "average_latency_ms": 145.2,
                "total_transcriptions": 150,
                "health_score": 92.5,
                "active_sessions": 2
            }
        }

class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(description="Error type")
    message: str = Field(description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.now, description="Error timestamp")
    
    class Config:
        schema_extra = {
            "example": {
                "error": "ValidationError",
                "message": "Invalid session ID provided",
                "details": {
                    "session_id": "invalid_id",
                    "valid_format": "sess_xxxxxxxxx"
                },
                "timestamp": "2024-01-15T10:30:00Z"
            }
        }

# WebSocket Models

class WebSocketMessage(BaseModel):
    """Base WebSocket message"""
    type: MessageType = Field(description="Message type")
    timestamp: datetime = Field(default_factory=datetime.now, description="Message timestamp")
    session_id: Optional[str] = Field(None, description="Session ID if applicable")

class TranscriptionUpdateMessage(WebSocketMessage):
    """Real-time transcription update message"""
    type: MessageType = Field(default=MessageType.TRANSCRIPTION_UPDATE)
    data: TranscriptionResult = Field(description="Transcription result")

class EmotionUpdateMessage(WebSocketMessage):
    """Emotion detection update message"""
    type: MessageType = Field(default=MessageType.EMOTION_UPDATE)
    data: EmotionResult = Field(description="Emotion detection result")

class StatusUpdateMessage(WebSocketMessage):
    """Status update message"""
    type: MessageType = Field(default=MessageType.STATUS_UPDATE)
    data: SystemStatus = Field(description="System status")

class ErrorMessage(WebSocketMessage):
    """Error message"""
    type: MessageType = Field(default=MessageType.ERROR)
    data: ErrorResponse = Field(description="Error information")

class SystemMessage(WebSocketMessage):
    """System message"""
    type: MessageType = Field(default=MessageType.SYSTEM_MESSAGE)
    data: Dict[str, Any] = Field(description="System message data")

# Response wrapper models

class SuccessResponse(BaseModel):
    """Generic success response"""
    success: bool = Field(default=True)
    message: str = Field(description="Success message")
    data: Optional[Any] = Field(None, description="Response data")
    timestamp: datetime = Field(default_factory=datetime.now)

class ListResponse(BaseModel):
    """Generic list response"""
    items: List[Any] = Field(description="List of items")
    total: int = Field(description="Total number of items")
    page: Optional[int] = Field(None, description="Current page number")
    page_size: Optional[int] = Field(None, description="Items per page")

# Validation helpers

def validate_session_id(session_id: str) -> str:
    """Validate session ID format"""
    if not session_id.startswith("sess_") or len(session_id) != 14:
        raise ValueError("Invalid session ID format. Must be 'sess_' followed by 9 characters")
    return session_id

def validate_confidence(confidence: float) -> float:
    """Validate confidence score"""
    if not 0.0 <= confidence <= 1.0:
        raise ValueError("Confidence must be between 0.0 and 1.0")
    return confidence
