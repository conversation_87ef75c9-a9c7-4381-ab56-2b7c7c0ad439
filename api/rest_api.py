"""
FastAPI REST API Server
Provides REST endpoints for transcription control and system management
"""

import asyncio
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
import threading
import time

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON><PERSON>ponse
from contextlib import asynccontextmanager

from .websocket_api import router as websocket_router, initialize_websocket_components

from .models import (
    StartTranscriptionRequest, ConfigurationRequest, TranscriptionResult,
    TranscriptionSession, SystemStatus, ErrorResponse, SuccessResponse,
    TranscriptionStatus, ListResponse
)
from config import APIConfig
from utils import get_logger

# Global variables for system components
voice_processor = None
model_manager = None
status_monitor = None
performance_monitor = None

# Session management
active_sessions: Dict[str, Dict[str, Any]] = {}
sessions_lock = threading.Lock()

logger = get_logger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    logger.info("Starting API server...")
    yield
    logger.info("Shutting down API server...")

# Create FastAPI app
app = FastAPI(
    title="Real-time Voice-to-Text API",
    description="Advanced real-time speech-to-text system with emotion detection",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=APIConfig.ALLOW_ORIGINS,
    allow_credentials=True,
    allow_methods=APIConfig.ALLOW_METHODS,
    allow_headers=APIConfig.ALLOW_HEADERS,
)

# Include WebSocket router
app.include_router(websocket_router)

# Dependency injection
def get_voice_processor():
    """Get voice processor instance"""
    if voice_processor is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Voice processor not initialized"
        )
    return voice_processor

def get_status_monitor():
    """Get status monitor instance"""
    if status_monitor is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Status monitor not available"
        )
    return status_monitor

def get_performance_monitor():
    """Get performance monitor instance"""
    if performance_monitor is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Performance monitor not available"
        )
    return performance_monitor

# Utility functions
def generate_session_id() -> str:
    """Generate unique session ID"""
    return f"sess_{uuid.uuid4().hex[:9]}"

def get_session(session_id: str) -> Dict[str, Any]:
    """Get session by ID"""
    with sessions_lock:
        if session_id not in active_sessions:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Session {session_id} not found"
            )
        return active_sessions[session_id]

def update_session(session_id: str, updates: Dict[str, Any]):
    """Update session data"""
    with sessions_lock:
        if session_id in active_sessions:
            active_sessions[session_id].update(updates)
            active_sessions[session_id]['updated_at'] = datetime.now()

# API Endpoints

@app.get("/", response_model=SuccessResponse)
async def root():
    """Root endpoint with API information"""
    return SuccessResponse(
        message="Real-time Voice-to-Text API is running",
        data={
            "version": "1.0.0",
            "features": [
                "Real-time speech-to-text",
                "Emotion detection",
                "Pause detection", 
                "Filler word detection",
                "WebSocket streaming"
            ],
            "endpoints": {
                "docs": "/docs",
                "health": "/health",
                "sessions": "/api/v1/sessions",
                "websocket": "/ws/transcribe"
            }
        }
    )

@app.get("/health", response_model=SystemStatus)
async def health_check(
    status_mon: Any = Depends(get_status_monitor),
    perf_mon: Any = Depends(get_performance_monitor)
):
    """System health check endpoint"""
    try:
        # Get current system status
        current_status = status_mon.get_current_status()
        performance_metrics = perf_mon.get_statistics()
        health_info = status_mon.get_health_status()
        
        return SystemStatus(
            status="healthy" if health_info['health_score'] > 70 else "degraded",
            uptime_seconds=current_status.uptime_seconds,
            cpu_usage=current_status.cpu_usage,
            memory_usage_mb=current_status.memory_usage_mb,
            gpu_memory_mb=current_status.gpu_memory_mb,
            gpu_utilization=current_status.gpu_utilization,
            current_latency_ms=current_status.current_latency_ms,
            average_latency_ms=current_status.avg_latency_ms,
            total_transcriptions=current_status.transcription_count,
            health_score=health_info['health_score'],
            active_sessions=len(active_sessions)
        )
        
    except Exception as e:
        logger.error(f"Error in health check: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Health check failed"
        )

@app.post("/api/v1/sessions", response_model=TranscriptionSession)
async def create_session(
    request: StartTranscriptionRequest,
    background_tasks: BackgroundTasks,
    processor: Any = Depends(get_voice_processor)
):
    """Create a new transcription session"""
    try:
        session_id = generate_session_id()
        
        # Create session data
        session_data = {
            'session_id': session_id,
            'status': TranscriptionStatus.IDLE,
            'created_at': datetime.now(),
            'updated_at': datetime.now(),
            'model_type': request.model_type,
            'language': request.language,
            'features_enabled': {
                'emotion_detection': request.enable_emotion_detection,
                'pause_detection': request.enable_pause_detection,
                'filler_detection': request.enable_filler_detection
            },
            'sensitivity': request.sensitivity,
            'total_transcriptions': 0,
            'total_processing_time_ms': 0.0,
            'average_confidence': 0.0,
            'transcription_results': []
        }
        
        # Store session
        with sessions_lock:
            active_sessions[session_id] = session_data
        
        logger.info(f"Created transcription session: {session_id}")
        
        return TranscriptionSession(
            session_id=session_id,
            status=TranscriptionStatus.IDLE,
            created_at=session_data['created_at'],
            updated_at=session_data['updated_at'],
            model_type=request.model_type,
            language=request.language,
            features_enabled=session_data['features_enabled']
        )
        
    except Exception as e:
        logger.error(f"Error creating session: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create transcription session"
        )

@app.get("/api/v1/sessions", response_model=ListResponse)
async def list_sessions():
    """List all active transcription sessions"""
    try:
        with sessions_lock:
            sessions_list = []
            for session_id, session_data in active_sessions.items():
                sessions_list.append(TranscriptionSession(
                    session_id=session_id,
                    status=session_data['status'],
                    created_at=session_data['created_at'],
                    updated_at=session_data['updated_at'],
                    model_type=session_data['model_type'],
                    language=session_data['language'],
                    features_enabled=session_data['features_enabled'],
                    total_transcriptions=session_data['total_transcriptions'],
                    total_processing_time_ms=session_data['total_processing_time_ms'],
                    average_confidence=session_data['average_confidence']
                ))
        
        return ListResponse(
            items=sessions_list,
            total=len(sessions_list)
        )
        
    except Exception as e:
        logger.error(f"Error listing sessions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list sessions"
        )

@app.get("/api/v1/sessions/{session_id}", response_model=TranscriptionSession)
async def get_session_info(session_id: str):
    """Get information about a specific session"""
    try:
        session_data = get_session(session_id)
        
        return TranscriptionSession(
            session_id=session_id,
            status=session_data['status'],
            created_at=session_data['created_at'],
            updated_at=session_data['updated_at'],
            model_type=session_data['model_type'],
            language=session_data['language'],
            features_enabled=session_data['features_enabled'],
            total_transcriptions=session_data['total_transcriptions'],
            total_processing_time_ms=session_data['total_processing_time_ms'],
            average_confidence=session_data['average_confidence']
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session info: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get session information"
        )

@app.post("/api/v1/sessions/{session_id}/start", response_model=SuccessResponse)
async def start_transcription(
    session_id: str,
    processor: Any = Depends(get_voice_processor)
):
    """Start transcription for a session"""
    try:
        session_data = get_session(session_id)
        
        if session_data['status'] == TranscriptionStatus.RECORDING:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Session is already recording"
            )
        
        # Update session status
        update_session(session_id, {'status': TranscriptionStatus.RECORDING})
        
        logger.info(f"Started transcription for session: {session_id}")
        
        return SuccessResponse(
            message=f"Transcription started for session {session_id}",
            data={'session_id': session_id, 'status': 'recording'}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting transcription: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start transcription"
        )

@app.post("/api/v1/sessions/{session_id}/stop", response_model=SuccessResponse)
async def stop_transcription(session_id: str):
    """Stop transcription for a session"""
    try:
        session_data = get_session(session_id)
        
        if session_data['status'] not in [TranscriptionStatus.RECORDING, TranscriptionStatus.PROCESSING]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Session is not currently active"
            )
        
        # Update session status
        update_session(session_id, {'status': TranscriptionStatus.STOPPED})
        
        logger.info(f"Stopped transcription for session: {session_id}")
        
        return SuccessResponse(
            message=f"Transcription stopped for session {session_id}",
            data={'session_id': session_id, 'status': 'stopped'}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error stopping transcription: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to stop transcription"
        )

@app.get("/api/v1/sessions/{session_id}/results", response_model=ListResponse)
async def get_transcription_results(
    session_id: str,
    limit: Optional[int] = 50,
    offset: Optional[int] = 0
):
    """Get transcription results for a session"""
    try:
        session_data = get_session(session_id)
        
        results = session_data.get('transcription_results', [])
        
        # Apply pagination
        start_idx = offset
        end_idx = start_idx + limit if limit else len(results)
        paginated_results = results[start_idx:end_idx]
        
        return ListResponse(
            items=paginated_results,
            total=len(results),
            page=offset // limit + 1 if limit else 1,
            page_size=limit
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting transcription results: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get transcription results"
        )

@app.delete("/api/v1/sessions/{session_id}", response_model=SuccessResponse)
async def delete_session(session_id: str):
    """Delete a transcription session"""
    try:
        get_session(session_id)  # Verify session exists
        
        # Remove session
        with sessions_lock:
            del active_sessions[session_id]
        
        logger.info(f"Deleted session: {session_id}")
        
        return SuccessResponse(
            message=f"Session {session_id} deleted successfully",
            data={'session_id': session_id}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting session: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete session"
        )

@app.post("/api/v1/configure", response_model=SuccessResponse)
async def update_configuration(
    config: ConfigurationRequest,
    processor: Any = Depends(get_voice_processor)
):
    """Update system configuration"""
    try:
        updates = {}
        
        if config.vad_sensitivity is not None:
            updates['vad_sensitivity'] = config.vad_sensitivity
        
        if config.emotion_detection is not None:
            updates['emotion_detection'] = config.emotion_detection
        
        if config.pause_detection is not None:
            updates['pause_detection'] = config.pause_detection
        
        if config.filler_detection is not None:
            updates['filler_detection'] = config.filler_detection
        
        if config.model_type is not None:
            updates['model_type'] = config.model_type
        
        if config.language is not None:
            updates['language'] = config.language
        
        logger.info(f"Configuration updated: {updates}")
        
        return SuccessResponse(
            message="Configuration updated successfully",
            data=updates
        )
        
    except Exception as e:
        logger.error(f"Error updating configuration: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update configuration"
        )

@app.get("/api/v1/statistics", response_model=Dict[str, Any])
async def get_system_statistics(
    status_mon: Any = Depends(get_status_monitor),
    perf_mon: Any = Depends(get_performance_monitor)
):
    """Get comprehensive system statistics"""
    try:
        performance_stats = perf_mon.get_statistics()
        status_summary = status_mon.get_status_summary()
        health_status = status_mon.get_health_status()
        
        return {
            'performance': performance_stats.__dict__ if hasattr(performance_stats, '__dict__') else performance_stats,
            'status': status_summary,
            'health': health_status,
            'sessions': {
                'active_count': len(active_sessions),
                'total_transcriptions': sum(s.get('total_transcriptions', 0) for s in active_sessions.values())
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting statistics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get system statistics"
        )

# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Handle HTTP exceptions"""
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error=exc.__class__.__name__,
            message=exc.detail,
            timestamp=datetime.now()
        ).dict()
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Handle general exceptions"""
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=ErrorResponse(
            error="InternalServerError",
            message="An internal server error occurred",
            timestamp=datetime.now()
        ).dict()
    )

# Initialization function
def initialize_api_components(vp, mm, sm, pm):
    """Initialize API with system components"""
    global voice_processor, model_manager, status_monitor, performance_monitor
    voice_processor = vp
    model_manager = mm
    status_monitor = sm
    performance_monitor = pm

    # Initialize WebSocket components
    initialize_websocket_components(vp, sm)

    logger.info("API components initialized")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host=APIConfig.HOST, port=APIConfig.PORT, reload=APIConfig.RELOAD)
