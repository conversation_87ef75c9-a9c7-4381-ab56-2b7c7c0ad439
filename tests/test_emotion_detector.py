"""
Unit Tests for Emotion Detector
Tests emotion detection functionality from text and audio features
"""

import pytest
import asyncio
import time
import numpy as np
from unittest.mock import Mock, patch, MagicMock

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.emotion_detector import EmotionDetector, EmotionResult

class TestEmotionDetector:
    """Test suite for EmotionDetector class"""
    
    @pytest.fixture
    def emotion_detector(self):
        """Create EmotionDetector instance"""
        return EmotionDetector()
    
    def test_emotion_detector_initialization(self, emotion_detector):
        """Test EmotionDetector initialization"""
        assert emotion_detector is not None
        assert emotion_detector.emotions == ['neutral', 'happy', 'sad', 'angry', 'excited', 'frustrated', 'surprised']
        assert emotion_detector.min_confidence > 0
        assert emotion_detector.detection_count == 0
        assert len(emotion_detector.emotion_history) == 0
    
    @pytest.mark.asyncio
    async def test_initialize_success(self, emotion_detector):
        """Test successful initialization"""
        with patch.object(emotion_detector, '_initialize_text_classifier') as mock_text_init, \
             patch.object(emotion_detector, '_initialize_audio_classifier') as mock_audio_init:
            
            result = await emotion_detector.initialize()
            
            assert result is True
            mock_text_init.assert_called_once()
            mock_audio_init.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_initialize_failure(self, emotion_detector):
        """Test initialization failure"""
        with patch.object(emotion_detector, '_initialize_text_classifier', side_effect=Exception("Init error")):
            result = await emotion_detector.initialize()
            assert result is False
    
    @pytest.mark.asyncio
    async def test_text_emotion_detection_with_transformer(self, emotion_detector):
        """Test text emotion detection with transformer model"""
        test_text = "I am very happy today!"
        
        # Mock transformer pipeline
        mock_pipeline = Mock()
        mock_pipeline.return_value = [[
            {'label': 'joy', 'score': 0.8},
            {'label': 'neutral', 'score': 0.2}
        ]]
        emotion_detector.text_classifier = mock_pipeline
        
        result = await emotion_detector._detect_text_emotion(test_text)
        
        assert 'happy' in result  # 'joy' should be mapped to 'happy'
        assert result['happy'] > 0
        assert sum(result.values()) == pytest.approx(1.0, rel=1e-2)  # Should sum to 1
    
    @pytest.mark.asyncio
    async def test_text_emotion_detection_with_fallback(self, emotion_detector):
        """Test text emotion detection with fallback classifier"""
        test_text = "I am very happy and excited!"
        
        # Set up fallback classifier
        emotion_detector.text_classifier = emotion_detector._create_fallback_text_classifier()
        
        result = await emotion_detector._detect_text_emotion(test_text)
        
        assert isinstance(result, dict)
        assert 'happy' in result
        assert 'excited' in result
        assert all(0 <= score <= 1 for score in result.values())
    
    @pytest.mark.asyncio
    async def test_audio_emotion_detection(self, emotion_detector):
        """Test audio emotion detection"""
        # Mock audio features
        audio_features = {
            'pitch_mean': 180,  # Higher pitch
            'pitch_std': 30,
            'rms_energy': 0.15,  # Higher energy
            'spectral_centroid_mean': 1500,
            'tempo': 130,
            'zero_crossing_rate': 0.08
        }
        
        # Set up audio classifier
        emotion_detector.audio_classifier = emotion_detector._create_audio_feature_classifier()
        
        result = await emotion_detector._detect_audio_emotion(audio_features)
        
        assert isinstance(result, dict)
        assert len(result) > 0
        assert all(emotion in emotion_detector.emotions for emotion in result.keys())
    
    @pytest.mark.asyncio
    async def test_detect_emotion_text_only(self, emotion_detector):
        """Test emotion detection with text only"""
        test_text = "I am feeling sad today"
        
        # Mock text classifier
        emotion_detector.text_classifier = emotion_detector._create_fallback_text_classifier()
        
        result = await emotion_detector.detect_emotion(test_text)
        
        assert isinstance(result, EmotionResult)
        assert result.primary_emotion in emotion_detector.emotions
        assert 0 <= result.confidence <= 1
        assert result.source in ['text', 'combined']
        assert result.processing_time > 0
    
    @pytest.mark.asyncio
    async def test_detect_emotion_combined(self, emotion_detector):
        """Test emotion detection with text and audio"""
        test_text = "I am happy"
        audio_features = {
            'pitch_mean': 200,
            'rms_energy': 0.2,
            'spectral_centroid_mean': 2000
        }
        
        # Set up classifiers
        emotion_detector.text_classifier = emotion_detector._create_fallback_text_classifier()
        emotion_detector.audio_classifier = emotion_detector._create_audio_feature_classifier()
        
        result = await emotion_detector.detect_emotion(test_text, audio_features)
        
        assert isinstance(result, EmotionResult)
        assert result.source == 'combined'
        assert result.primary_emotion in emotion_detector.emotions
    
    def test_emotion_mapping(self, emotion_detector):
        """Test emotion label mapping"""
        # Test mapping from model outputs to standard emotions
        assert emotion_detector.emotion_mapping['joy'] == 'happy'
        assert emotion_detector.emotion_mapping['sadness'] == 'sad'
        assert emotion_detector.emotion_mapping['anger'] == 'angry'
        assert emotion_detector.emotion_mapping['fear'] == 'surprised'
    
    def test_combine_emotion_results(self, emotion_detector):
        """Test combining text and audio emotion results"""
        text_emotions = {'happy': 0.8, 'neutral': 0.2}
        audio_emotions = {'excited': 0.6, 'happy': 0.4}
        
        combined = emotion_detector._combine_emotion_results(text_emotions, audio_emotions)
        
        assert isinstance(combined, dict)
        assert 'happy' in combined
        assert 'excited' in combined
        assert 'neutral' in combined
        
        # Check that text has higher weight (0.7 vs 0.3)
        expected_happy = 0.7 * 0.8 + 0.3 * 0.4  # 0.56 + 0.12 = 0.68
        assert combined['happy'] == pytest.approx(expected_happy, rel=1e-2)
    
    def test_temporal_smoothing(self, emotion_detector):
        """Test temporal smoothing of emotion results"""
        # Add some history
        for i in range(3):
            result = EmotionResult(
                primary_emotion='happy',
                confidence=0.8,
                all_emotions={'happy': 0.8, 'neutral': 0.2},
                processing_time=0.1,
                source='test',
                timestamp=time.time()
            )
            emotion_detector.emotion_history.append(result)
        
        current_emotions = {'sad': 0.9, 'neutral': 0.1}
        smoothed = emotion_detector._apply_temporal_smoothing(current_emotions)
        
        # Should be smoothed with history
        assert 'happy' in smoothed
        assert smoothed['sad'] < 0.9  # Should be reduced due to smoothing
    
    def test_emotion_statistics(self, emotion_detector):
        """Test emotion detection statistics"""
        # Add some test results
        for i in range(5):
            result = EmotionResult(
                primary_emotion='happy' if i % 2 == 0 else 'sad',
                confidence=0.8 + i * 0.02,
                all_emotions={'happy': 0.8, 'sad': 0.2},
                processing_time=0.1 + i * 0.01,
                source='test',
                timestamp=time.time()
            )
            emotion_detector.emotion_history.append(result)
        
        emotion_detector.detection_count = 5
        emotion_detector.total_processing_time = 0.6
        
        stats = emotion_detector.get_emotion_statistics()
        
        assert stats['total_detections'] == 5
        assert stats['average_processing_time_ms'] == 120  # 0.6s / 5 * 1000ms
        assert 'emotion_distribution' in stats
        assert 'recent_emotions' in stats
    
    def test_sensitivity_setting(self, emotion_detector):
        """Test setting emotion detection sensitivity"""
        original_confidence = emotion_detector.min_confidence
        
        emotion_detector.set_sensitivity(0.8)
        assert emotion_detector.min_confidence == 0.8
        
        # Test bounds
        emotion_detector.set_sensitivity(-0.1)
        assert emotion_detector.min_confidence == 0.0
        
        emotion_detector.set_sensitivity(1.5)
        assert emotion_detector.min_confidence == 1.0
    
    def test_reset_history(self, emotion_detector):
        """Test resetting emotion history"""
        # Add some history
        result = EmotionResult(
            primary_emotion='happy',
            confidence=0.8,
            all_emotions={'happy': 0.8, 'neutral': 0.2},
            processing_time=0.1,
            source='test',
            timestamp=time.time()
        )
        emotion_detector.emotion_history.append(result)
        emotion_detector.detection_count = 1
        emotion_detector.total_processing_time = 0.1
        
        emotion_detector.reset_history()
        
        assert len(emotion_detector.emotion_history) == 0
        assert emotion_detector.detection_count == 0
        assert emotion_detector.total_processing_time == 0
    
    def test_availability_check(self, emotion_detector):
        """Test availability check"""
        # Without classifier
        assert emotion_detector.is_available() is False
        
        # With classifier
        emotion_detector.text_classifier = Mock()
        assert emotion_detector.is_available() is True

class TestEmotionResult:
    """Test suite for EmotionResult dataclass"""
    
    def test_emotion_result_creation(self):
        """Test EmotionResult creation"""
        result = EmotionResult(
            primary_emotion='happy',
            confidence=0.85,
            all_emotions={'happy': 0.85, 'neutral': 0.15},
            processing_time=0.125,
            source='text',
            timestamp=time.time()
        )
        
        assert result.primary_emotion == 'happy'
        assert result.confidence == 0.85
        assert result.all_emotions['happy'] == 0.85
        assert result.processing_time == 0.125
        assert result.source == 'text'
        assert result.timestamp > 0

class TestEmotionDetectorIntegration:
    """Integration tests for EmotionDetector"""
    
    @pytest.fixture
    def integrated_detector(self):
        """Create EmotionDetector for integration testing"""
        detector = EmotionDetector()
        # Use fallback classifiers for testing
        detector.text_classifier = detector._create_fallback_text_classifier()
        detector.audio_classifier = detector._create_audio_feature_classifier()
        return detector
    
    @pytest.mark.asyncio
    async def test_full_emotion_detection_pipeline(self, integrated_detector):
        """Test complete emotion detection pipeline"""
        test_cases = [
            ("I am very happy today!", "happy"),
            ("I feel sad and depressed", "sad"),
            ("This makes me angry!", "angry"),
            ("I am so excited about this!", "excited"),
            ("This is frustrating me", "frustrated"),
            ("What a surprise!", "surprised"),
            ("Nothing special happening", "neutral")
        ]
        
        for text, expected_emotion in test_cases:
            result = await integrated_detector.detect_emotion(text)
            
            assert isinstance(result, EmotionResult)
            assert result.primary_emotion in integrated_detector.emotions
            assert 0 <= result.confidence <= 1
            assert result.processing_time > 0
            
            # Note: We don't assert exact emotion match because the fallback
            # classifier is simple and may not always detect the expected emotion
    
    @pytest.mark.asyncio
    async def test_audio_feature_emotion_detection(self, integrated_detector):
        """Test emotion detection from audio features"""
        # Test different audio feature patterns
        test_cases = [
            # Happy: higher pitch, higher energy
            {'pitch_mean': 200, 'rms_energy': 0.15, 'pitch_std': 40},
            # Sad: lower pitch, lower energy
            {'pitch_mean': 100, 'rms_energy': 0.04, 'pitch_std': 20},
            # Angry: high energy, variable pitch, high tempo
            {'pitch_mean': 150, 'rms_energy': 0.20, 'pitch_std': 80, 'tempo': 140},
        ]
        
        for features in test_cases:
            result = await integrated_detector.detect_emotion("test", features)
            
            assert isinstance(result, EmotionResult)
            assert result.source == 'combined'
            assert result.primary_emotion in integrated_detector.emotions

class TestEmotionDetectorPerformance:
    """Performance tests for EmotionDetector"""
    
    @pytest.mark.asyncio
    async def test_detection_speed(self):
        """Test emotion detection speed"""
        detector = EmotionDetector()
        detector.text_classifier = detector._create_fallback_text_classifier()
        
        test_text = "This is a performance test for emotion detection speed"
        
        start_time = time.time()
        result = await detector.detect_emotion(test_text)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        # Should process in less than 100ms for good performance
        assert processing_time < 0.1
        assert result.processing_time < 0.1
    
    @pytest.mark.asyncio
    async def test_batch_processing_performance(self):
        """Test performance with multiple detections"""
        detector = EmotionDetector()
        detector.text_classifier = detector._create_fallback_text_classifier()
        
        test_texts = [
            "I am happy",
            "I feel sad",
            "This is exciting",
            "I am frustrated",
            "What a surprise"
        ] * 10  # 50 texts total
        
        start_time = time.time()
        
        for text in test_texts:
            await detector.detect_emotion(text)
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time_per_detection = total_time / len(test_texts)
        
        # Should average less than 50ms per detection
        assert avg_time_per_detection < 0.05

# Test utilities
@pytest.fixture
def sample_audio_features():
    """Generate sample audio features for testing"""
    return {
        'rms_energy': 0.12,
        'zero_crossing_rate': 0.08,
        'spectral_centroid_mean': 1800,
        'spectral_centroid_std': 400,
        'spectral_rolloff_mean': 3500,
        'spectral_bandwidth_mean': 2200,
        'pitch_mean': 150,
        'pitch_std': 45,
        'pitch_min': 80,
        'pitch_max': 250,
        'tempo': 120,
        **{f'mfcc_{i}_mean': np.random.randn() for i in range(13)},
        **{f'mfcc_{i}_std': abs(np.random.randn()) for i in range(13)}
    }

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
