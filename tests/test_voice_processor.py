"""
Unit Tests for Voice Processor
Tests the core voice processing functionality
"""

import pytest
import asyncio
import time
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from dataclasses import dataclass

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.voice_processor import VoiceProcessor, TranscriptionResult
from core.emotion_detector import EmotionDetector
from core.text_formatter import Text<PERSON><PERSON>atter
from core.audio_utils import AudioUtils

class TestVoiceProcessor:
    """Test suite for VoiceProcessor class"""
    
    @pytest.fixture
    def mock_components(self):
        """Create mock components for testing"""
        model_manager = Mock()
        console_display = Mock()
        status_monitor = Mock()
        performance_monitor = Mock()
        
        return {
            'model_manager': model_manager,
            'console_display': console_display,
            'status_monitor': status_monitor,
            'performance_monitor': performance_monitor
        }
    
    @pytest.fixture
    def voice_processor(self, mock_components):
        """Create VoiceProcessor instance with mocked components"""
        return VoiceProcessor(**mock_components)
    
    def test_voice_processor_initialization(self, voice_processor):
        """Test VoiceProcessor initialization"""
        assert voice_processor is not None
        assert voice_processor.is_running is False
        assert voice_processor.is_recording is False
        assert voice_processor.transcription_count == 0
        assert voice_processor.audio_utils is not None
        assert voice_processor.emotion_detector is not None
        assert voice_processor.text_formatter is not None
    
    @pytest.mark.asyncio
    async def test_initialize_success(self, voice_processor):
        """Test successful initialization"""
        with patch.object(voice_processor, '_check_gpu_resources') as mock_gpu_check, \
             patch.object(voice_processor, '_initialize_recorder') as mock_init_recorder, \
             patch.object(voice_processor.emotion_detector, 'initialize', return_value=True) as mock_emotion_init, \
             patch.object(voice_processor.text_formatter, 'initialize') as mock_text_init:
            
            result = await voice_processor.initialize()
            
            assert result is True
            mock_gpu_check.assert_called_once()
            mock_init_recorder.assert_called_once()
            mock_emotion_init.assert_called_once()
            mock_text_init.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_initialize_failure(self, voice_processor):
        """Test initialization failure"""
        with patch.object(voice_processor, '_check_gpu_resources', side_effect=Exception("GPU error")):
            result = await voice_processor.initialize()
            assert result is False
    
    def test_recording_callbacks(self, voice_processor):
        """Test recording callback methods"""
        # Test recording start
        voice_processor._on_recording_start()
        assert voice_processor.is_recording is True
        assert voice_processor.last_speech_time is not None
        voice_processor.status_monitor.update_status.assert_called_with("recording", True)
        
        # Test recording stop
        voice_processor._on_recording_stop()
        assert voice_processor.is_recording is False
        voice_processor.status_monitor.update_status.assert_called_with("recording", False)
    
    def test_speech_detection_callbacks(self, voice_processor):
        """Test speech detection callback methods"""
        # Test speech detected
        voice_processor._on_speech_detected()
        assert voice_processor.pause_start_time is None
        assert voice_processor.last_speech_time is not None
        voice_processor.status_monitor.update_status.assert_called_with("speech_detected", True)
        
        # Test speech ended
        voice_processor._on_speech_ended()
        assert voice_processor.pause_start_time is not None
        voice_processor.status_monitor.update_status.assert_called_with("speech_detected", False)
    
    def test_pause_calculation(self, voice_processor):
        """Test pause information calculation"""
        # Set up pause scenario
        voice_processor.pause_start_time = time.time() - 1.5  # 1.5 seconds ago
        
        pause_info = voice_processor._calculate_pause_info()
        
        assert 'duration' in pause_info
        assert 'type' in pause_info
        assert pause_info['duration'] >= 1.4  # Allow for small timing variations
        assert pause_info['type'] == 'medium'  # 1.5s should be medium pause
    
    def test_realtime_update_processing(self, voice_processor):
        """Test real-time transcription update processing"""
        test_text = "Hello world"
        
        with patch.object(voice_processor, '_calculate_pause_info', return_value={'duration': 0.5, 'type': 'short'}), \
             patch.object(voice_processor.text_formatter, 'format_realtime_text', return_value="Hello world (pause:0.5s)"), \
             patch.object(voice_processor.performance_monitor, 'record_latency'):
            
            voice_processor._on_realtime_update(test_text)
            
            # Check that console display was called
            voice_processor.console_display.display_realtime_text.assert_called_once()
            
            # Check that performance was recorded
            voice_processor.performance_monitor.record_latency.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_final_transcription_processing(self, voice_processor):
        """Test final transcription processing"""
        test_text = "This is a final transcription"
        
        mock_emotion_result = {
            'primary_emotion': 'happy',
            'confidence': 0.85,
            'all_emotions': {'happy': 0.85, 'neutral': 0.15}
        }
        
        with patch.object(voice_processor.emotion_detector, 'detect_emotion', return_value=mock_emotion_result), \
             patch.object(voice_processor.text_formatter, 'format_final_text', return_value="This is a final transcription (happy:85%)"), \
             patch.object(voice_processor, '_calculate_pause_info', return_value={}):
            
            await voice_processor._process_final_transcription(test_text)
            
            # Check that transcription count increased
            assert voice_processor.transcription_count == 1
            
            # Check that console display was called
            voice_processor.console_display.display_final_text.assert_called_once()
            
            # Check that performance monitor was called
            voice_processor.performance_monitor.record_transcription.assert_called_once()
    
    def test_statistics_tracking(self, voice_processor):
        """Test statistics tracking"""
        # Simulate some processing
        voice_processor.transcription_count = 5
        voice_processor.total_processing_time = 2.5
        
        stats = voice_processor.get_statistics()
        
        assert stats['transcription_count'] == 5
        assert stats['average_latency_ms'] == 500.0  # 2.5s / 5 * 1000ms
        assert stats['total_processing_time'] == 2.5
        assert stats['is_running'] is False
        assert stats['is_recording'] is False
    
    def test_callback_setting(self, voice_processor):
        """Test callback function setting"""
        transcription_callback = Mock()
        emotion_callback = Mock()
        status_callback = Mock()
        
        voice_processor.set_callbacks(
            transcription_callback=transcription_callback,
            emotion_callback=emotion_callback,
            status_callback=status_callback
        )
        
        assert voice_processor.on_transcription_callback == transcription_callback
        assert voice_processor.on_emotion_callback == emotion_callback
        assert voice_processor.on_status_callback == status_callback
    
    def test_stop_functionality(self, voice_processor):
        """Test stop functionality"""
        # Set up running state
        voice_processor.is_running = True
        voice_processor.recorder = Mock()
        
        voice_processor.stop()
        
        assert voice_processor.is_running is False
        voice_processor.recorder.shutdown.assert_called_once()

class TestTranscriptionResult:
    """Test suite for TranscriptionResult dataclass"""
    
    def test_transcription_result_creation(self):
        """Test TranscriptionResult creation"""
        result = TranscriptionResult(
            text="Hello world",
            confidence=0.95,
            timestamp=time.time(),
            is_final=True,
            processing_time=0.125
        )
        
        assert result.text == "Hello world"
        assert result.confidence == 0.95
        assert result.is_final is True
        assert result.processing_time == 0.125
        assert result.emotion is None
        assert result.pauses is None
    
    def test_transcription_result_with_metadata(self):
        """Test TranscriptionResult with metadata"""
        emotion_data = {'primary_emotion': 'happy', 'confidence': 0.8}
        pause_data = {'duration': 1.5, 'type': 'medium'}
        
        result = TranscriptionResult(
            text="Hello world",
            confidence=0.95,
            timestamp=time.time(),
            is_final=True,
            processing_time=0.125,
            emotion=emotion_data,
            pauses=pause_data
        )
        
        assert result.emotion == emotion_data
        assert result.pauses == pause_data

class TestVoiceProcessorIntegration:
    """Integration tests for VoiceProcessor with real components"""
    
    @pytest.fixture
    def real_components(self):
        """Create real components for integration testing"""
        model_manager = Mock()  # Still mock this as it requires actual models
        console_display = Mock()
        status_monitor = Mock()
        performance_monitor = Mock()
        
        return {
            'model_manager': model_manager,
            'console_display': console_display,
            'status_monitor': status_monitor,
            'performance_monitor': performance_monitor
        }
    
    @pytest.fixture
    def integrated_processor(self, real_components):
        """Create VoiceProcessor with real audio and text components"""
        processor = VoiceProcessor(**real_components)
        
        # Initialize real components
        processor.audio_utils = AudioUtils()
        processor.emotion_detector = EmotionDetector()
        processor.text_formatter = TextFormatter()
        processor.text_formatter.initialize()
        
        return processor
    
    def test_audio_utils_integration(self, integrated_processor):
        """Test integration with AudioUtils"""
        # Test audio preprocessing
        test_audio = np.random.randn(16000).astype(np.float32)  # 1 second of audio
        
        processed_audio = integrated_processor.audio_utils.preprocess_audio(test_audio)
        
        assert processed_audio is not None
        assert len(processed_audio) == len(test_audio)
        assert processed_audio.dtype == np.float32
    
    def test_text_formatter_integration(self, integrated_processor):
        """Test integration with TextFormatter"""
        test_text = "um hello there how are you doing"
        pause_info = {'duration': 0.8, 'type': 'short'}
        
        formatted_text = integrated_processor.text_formatter.format_realtime_text(test_text, pause_info)
        
        assert formatted_text is not None
        assert len(formatted_text) >= len(test_text)  # Should be same or longer with formatting
    
    @pytest.mark.asyncio
    async def test_emotion_detector_integration(self, integrated_processor):
        """Test integration with EmotionDetector"""
        test_text = "I am very happy today!"
        
        # Initialize emotion detector with fallback
        await integrated_processor.emotion_detector.initialize()
        
        emotion_result = await integrated_processor.emotion_detector.detect_emotion(test_text)
        
        assert emotion_result is not None
        assert hasattr(emotion_result, 'primary_emotion')
        assert hasattr(emotion_result, 'confidence')
        assert emotion_result.primary_emotion in ['neutral', 'happy', 'sad', 'angry', 'excited', 'frustrated', 'surprised']

# Test fixtures and utilities
@pytest.fixture
def sample_audio_data():
    """Generate sample audio data for testing"""
    sample_rate = 16000
    duration = 2.0
    frequency = 440  # A4 note
    
    t = np.linspace(0, duration, int(sample_rate * duration))
    audio = 0.3 * np.sin(2 * np.pi * frequency * t)
    
    return audio.astype(np.float32)

@pytest.fixture
def mock_transcription_result():
    """Create a mock transcription result"""
    return TranscriptionResult(
        text="This is a test transcription",
        confidence=0.92,
        timestamp=time.time(),
        is_final=True,
        processing_time=0.156,
        emotion={'primary_emotion': 'neutral', 'confidence': 0.7},
        pauses={'duration': 0.5, 'type': 'short'}
    )

# Performance tests
class TestVoiceProcessorPerformance:
    """Performance tests for VoiceProcessor"""
    
    def test_realtime_processing_speed(self, voice_processor):
        """Test that real-time processing is fast enough"""
        test_text = "This is a performance test of real-time processing"
        
        with patch.object(voice_processor, '_calculate_pause_info', return_value={}), \
             patch.object(voice_processor.text_formatter, 'format_realtime_text', return_value=test_text):
            
            start_time = time.time()
            voice_processor._on_realtime_update(test_text)
            end_time = time.time()
            
            processing_time = end_time - start_time
            
            # Should process in less than 50ms for real-time performance
            assert processing_time < 0.05
    
    @pytest.mark.asyncio
    async def test_final_processing_speed(self, voice_processor):
        """Test that final processing meets latency requirements"""
        test_text = "This is a performance test of final processing"
        
        with patch.object(voice_processor.emotion_detector, 'detect_emotion', return_value={'primary_emotion': 'neutral', 'confidence': 0.7}), \
             patch.object(voice_processor.text_formatter, 'format_final_text', return_value=test_text), \
             patch.object(voice_processor, '_calculate_pause_info', return_value={}):
            
            start_time = time.time()
            await voice_processor._process_final_transcription(test_text)
            end_time = time.time()
            
            processing_time = end_time - start_time
            
            # Should process in less than 200ms for acceptable latency
            assert processing_time < 0.2

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
