"""
Unit Tests for API Layer
Tests REST endpoints and WebSocket functionality
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, patch, MagicMock
from fastapi.testclient import TestClient
import websockets
from datetime import datetime

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from api.rest_api import app, initialize_api_components
from api.websocket_api import WebSocketManager, ConnectionManager
from api.models import (
    StartTranscriptionRequest, TranscriptionSession, SystemStatus,
    TranscriptionResult, EmotionResult
)

class TestRESTAPI:
    """Test suite for REST API endpoints"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)
    
    @pytest.fixture
    def mock_components(self):
        """Create mock system components"""
        voice_processor = Mock()
        model_manager = Mock()
        status_monitor = Mock()
        performance_monitor = Mock()
        
        # Initialize API with mocked components
        initialize_api_components(voice_processor, model_manager, status_monitor, performance_monitor)
        
        return {
            'voice_processor': voice_processor,
            'model_manager': model_manager,
            'status_monitor': status_monitor,
            'performance_monitor': performance_monitor
        }
    
    def test_root_endpoint(self, client):
        """Test root endpoint"""
        response = client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        assert data['success'] is True
        assert 'version' in data['data']
        assert 'features' in data['data']
    
    def test_health_check_success(self, client, mock_components):
        """Test health check endpoint with healthy system"""
        # Mock healthy system status
        mock_status = Mock()
        mock_status.uptime_seconds = 3600
        mock_status.cpu_usage = 45.0
        mock_status.memory_usage_mb = 2048
        mock_status.gpu_memory_mb = 1024
        mock_status.gpu_utilization = 60.0
        mock_status.current_latency_ms = 150
        mock_status.avg_latency_ms = 145
        mock_status.transcription_count = 100
        
        mock_components['status_monitor'].get_current_status.return_value = mock_status
        mock_components['performance_monitor'].get_statistics.return_value = Mock()
        mock_components['status_monitor'].get_health_status.return_value = {'health_score': 85}
        
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data['status'] == 'healthy'
        assert data['uptime_seconds'] == 3600
        assert data['health_score'] == 85
    
    def test_create_session_success(self, client, mock_components):
        """Test successful session creation"""
        request_data = {
            "model_type": "medium.en",
            "language": "en",
            "enable_emotion_detection": True,
            "enable_pause_detection": True,
            "enable_filler_detection": True,
            "sensitivity": 0.7
        }
        
        response = client.post("/api/v1/sessions", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data['session_id'].startswith('sess_')
        assert data['status'] == 'idle'
        assert data['model_type'] == 'medium.en'
        assert data['language'] == 'en'
    
    def test_create_session_invalid_data(self, client):
        """Test session creation with invalid data"""
        request_data = {
            "sensitivity": 1.5  # Invalid: should be <= 1.0
        }
        
        response = client.post("/api/v1/sessions", json=request_data)
        
        assert response.status_code == 422  # Validation error
    
    def test_list_sessions(self, client, mock_components):
        """Test listing sessions"""
        # Create a session first
        request_data = {
            "model_type": "medium.en",
            "language": "en"
        }
        create_response = client.post("/api/v1/sessions", json=request_data)
        assert create_response.status_code == 200
        
        # List sessions
        response = client.get("/api/v1/sessions")
        
        assert response.status_code == 200
        data = response.json()
        assert 'items' in data
        assert 'total' in data
        assert data['total'] >= 1
    
    def test_get_session_info(self, client, mock_components):
        """Test getting session information"""
        # Create a session first
        request_data = {"model_type": "medium.en", "language": "en"}
        create_response = client.post("/api/v1/sessions", json=request_data)
        session_id = create_response.json()['session_id']
        
        # Get session info
        response = client.get(f"/api/v1/sessions/{session_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data['session_id'] == session_id
        assert data['model_type'] == 'medium.en'
    
    def test_get_nonexistent_session(self, client):
        """Test getting info for nonexistent session"""
        response = client.get("/api/v1/sessions/sess_nonexistent")
        
        assert response.status_code == 404
    
    def test_start_transcription(self, client, mock_components):
        """Test starting transcription"""
        # Create a session first
        request_data = {"model_type": "medium.en", "language": "en"}
        create_response = client.post("/api/v1/sessions", json=request_data)
        session_id = create_response.json()['session_id']
        
        # Start transcription
        response = client.post(f"/api/v1/sessions/{session_id}/start")
        
        assert response.status_code == 200
        data = response.json()
        assert data['success'] is True
        assert session_id in data['message']
    
    def test_stop_transcription(self, client, mock_components):
        """Test stopping transcription"""
        # Create and start a session
        request_data = {"model_type": "medium.en", "language": "en"}
        create_response = client.post("/api/v1/sessions", json=request_data)
        session_id = create_response.json()['session_id']
        
        client.post(f"/api/v1/sessions/{session_id}/start")
        
        # Stop transcription
        response = client.post(f"/api/v1/sessions/{session_id}/stop")
        
        assert response.status_code == 200
        data = response.json()
        assert data['success'] is True
    
    def test_delete_session(self, client, mock_components):
        """Test deleting a session"""
        # Create a session first
        request_data = {"model_type": "medium.en", "language": "en"}
        create_response = client.post("/api/v1/sessions", json=request_data)
        session_id = create_response.json()['session_id']
        
        # Delete session
        response = client.delete(f"/api/v1/sessions/{session_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data['success'] is True
        
        # Verify session is deleted
        get_response = client.get(f"/api/v1/sessions/{session_id}")
        assert get_response.status_code == 404
    
    def test_update_configuration(self, client, mock_components):
        """Test updating system configuration"""
        config_data = {
            "vad_sensitivity": 0.8,
            "emotion_detection": True,
            "model_type": "small.en"
        }
        
        response = client.post("/api/v1/configure", json=config_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data['success'] is True
        assert 'vad_sensitivity' in data['data']
    
    def test_get_statistics(self, client, mock_components):
        """Test getting system statistics"""
        # Mock statistics data
        mock_components['performance_monitor'].get_statistics.return_value = Mock()
        mock_components['status_monitor'].get_status_summary.return_value = {}
        mock_components['status_monitor'].get_health_status.return_value = {}
        
        response = client.get("/api/v1/statistics")
        
        assert response.status_code == 200
        data = response.json()
        assert 'performance' in data
        assert 'status' in data
        assert 'health' in data
        assert 'sessions' in data

class TestWebSocketAPI:
    """Test suite for WebSocket API"""
    
    @pytest.fixture
    def connection_manager(self):
        """Create ConnectionManager instance"""
        return ConnectionManager()
    
    @pytest.fixture
    def websocket_manager(self):
        """Create WebSocketManager instance"""
        return WebSocketManager()
    
    @pytest.mark.asyncio
    async def test_connection_manager_connect(self, connection_manager):
        """Test WebSocket connection management"""
        mock_websocket = Mock()
        mock_websocket.accept = Mock()
        
        client_id = await connection_manager.connect(mock_websocket)
        
        assert client_id.startswith('client_')
        assert client_id in connection_manager.active_connections
        assert client_id in connection_manager.connection_metadata
        mock_websocket.accept.assert_called_once()
    
    def test_connection_manager_disconnect(self, connection_manager):
        """Test WebSocket disconnection"""
        # Add a mock connection
        client_id = "test_client"
        connection_manager.active_connections[client_id] = Mock()
        connection_manager.connection_metadata[client_id] = {}
        
        connection_manager.disconnect(client_id)
        
        assert client_id not in connection_manager.active_connections
        assert client_id not in connection_manager.connection_metadata
    
    @pytest.mark.asyncio
    async def test_send_personal_message(self, connection_manager):
        """Test sending personal message"""
        # Add a mock connection
        client_id = "test_client"
        mock_websocket = Mock()
        mock_websocket.send_text = Mock()
        
        connection_manager.active_connections[client_id] = mock_websocket
        connection_manager.connection_metadata[client_id] = {
            'messages_sent': 0,
            'last_activity': datetime.now()
        }
        
        test_message = {"type": "test", "data": "hello"}
        result = await connection_manager.send_personal_message(client_id, test_message)
        
        assert result is True
        mock_websocket.send_text.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_broadcast_message(self, connection_manager):
        """Test broadcasting message to all clients"""
        # Add multiple mock connections
        clients = ["client1", "client2", "client3"]
        for client_id in clients:
            mock_websocket = Mock()
            mock_websocket.send_text = Mock()
            connection_manager.active_connections[client_id] = mock_websocket
            connection_manager.connection_metadata[client_id] = {
                'messages_sent': 0,
                'last_activity': datetime.now()
            }
        
        test_message = {"type": "broadcast", "data": "hello all"}
        await connection_manager.broadcast(test_message)
        
        # All clients should receive the message
        for client_id in clients:
            connection_manager.active_connections[client_id].send_text.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_send_transcription_update(self, connection_manager):
        """Test sending transcription update"""
        client_id = "test_client"
        mock_websocket = Mock()
        mock_websocket.send_text = Mock()
        
        connection_manager.active_connections[client_id] = mock_websocket
        connection_manager.connection_metadata[client_id] = {
            'messages_sent': 0,
            'last_activity': datetime.now()
        }
        
        # Create mock transcription result
        transcription_result = TranscriptionResult(
            text="Hello world",
            confidence=0.95,
            is_final=True,
            timestamp=datetime.now(),
            processing_time_ms=125.5
        )
        
        await connection_manager.send_transcription_update(client_id, transcription_result)
        
        mock_websocket.send_text.assert_called_once()
        # Verify the message structure
        call_args = mock_websocket.send_text.call_args[0][0]
        message_data = json.loads(call_args)
        assert message_data['type'] == 'transcription_update'
        assert 'data' in message_data
    
    def test_connection_statistics(self, connection_manager):
        """Test connection statistics"""
        # Add some mock connections
        for i in range(3):
            client_id = f"client_{i}"
            connection_manager.active_connections[client_id] = Mock()
            connection_manager.connection_metadata[client_id] = {}
        
        connection_manager.total_connections = 10
        connection_manager.messages_sent = 50
        connection_manager.messages_received = 30
        
        stats = connection_manager.get_statistics()
        
        assert stats['active_connections'] == 3
        assert stats['total_connections'] == 10
        assert stats['messages_sent'] == 50
        assert stats['messages_received'] == 30
    
    @pytest.mark.asyncio
    async def test_websocket_manager_message_handling(self, websocket_manager):
        """Test WebSocket message handling"""
        client_id = "test_client"
        
        # Test ping message
        ping_message = json.dumps({"type": "ping", "timestamp": 1234567890})
        
        with patch.object(websocket_manager.connection_manager, 'send_system_message') as mock_send:
            await websocket_manager._handle_message(client_id, ping_message)
            mock_send.assert_called_once_with(client_id, "pong", {"timestamp": mock_send.call_args[0][2]["timestamp"]})
    
    def test_websocket_manager_set_components(self, websocket_manager):
        """Test setting system components"""
        mock_voice_processor = Mock()
        mock_status_monitor = Mock()
        
        websocket_manager.set_components(mock_voice_processor, mock_status_monitor)
        
        assert websocket_manager.voice_processor == mock_voice_processor
        assert websocket_manager.status_monitor == mock_status_monitor

class TestAPIModels:
    """Test suite for API data models"""
    
    def test_start_transcription_request_validation(self):
        """Test StartTranscriptionRequest validation"""
        # Valid request
        valid_request = StartTranscriptionRequest(
            model_type="medium.en",
            language="en",
            enable_emotion_detection=True,
            sensitivity=0.7
        )
        
        assert valid_request.model_type == "medium.en"
        assert valid_request.sensitivity == 0.7
        
        # Test validation bounds
        with pytest.raises(ValueError):
            StartTranscriptionRequest(sensitivity=1.5)  # > 1.0
        
        with pytest.raises(ValueError):
            StartTranscriptionRequest(sensitivity=-0.1)  # < 0.0
    
    def test_transcription_result_model(self):
        """Test TranscriptionResult model"""
        result = TranscriptionResult(
            text="Hello world",
            confidence=0.95,
            is_final=True,
            timestamp=datetime.now(),
            processing_time_ms=125.5,
            emotion=EmotionResult(
                primary_emotion="happy",
                confidence=0.85,
                all_emotions={"happy": 0.85, "neutral": 0.15},
                processing_time_ms=15.2
            )
        )
        
        assert result.text == "Hello world"
        assert result.confidence == 0.95
        assert result.is_final is True
        assert result.emotion.primary_emotion == "happy"
    
    def test_system_status_model(self):
        """Test SystemStatus model"""
        status = SystemStatus(
            status="healthy",
            uptime_seconds=3600.5,
            cpu_usage=45.2,
            memory_usage_mb=2048.5,
            gpu_memory_mb=1024.0,
            gpu_utilization=65.8,
            current_latency_ms=125.5,
            average_latency_ms=145.2,
            total_transcriptions=150,
            health_score=92.5,
            active_sessions=2
        )
        
        assert status.status == "healthy"
        assert status.health_score == 92.5
        assert 0 <= status.health_score <= 100

class TestAPIIntegration:
    """Integration tests for API components"""
    
    @pytest.fixture
    def integrated_client(self):
        """Create client with integrated components"""
        # Mock components but with more realistic behavior
        voice_processor = Mock()
        model_manager = Mock()
        status_monitor = Mock()
        performance_monitor = Mock()
        
        # Set up realistic mock responses
        status_monitor.get_current_status.return_value = Mock(
            uptime_seconds=3600,
            cpu_usage=45.0,
            memory_usage_mb=2048,
            gpu_memory_mb=1024,
            gpu_utilization=60.0,
            current_latency_ms=150,
            avg_latency_ms=145,
            transcription_count=100
        )
        
        status_monitor.get_health_status.return_value = {'health_score': 85}
        performance_monitor.get_statistics.return_value = Mock()
        status_monitor.get_status_summary.return_value = {}
        
        initialize_api_components(voice_processor, model_manager, status_monitor, performance_monitor)
        
        return TestClient(app)
    
    def test_full_session_lifecycle(self, integrated_client):
        """Test complete session lifecycle"""
        # 1. Create session
        create_response = integrated_client.post("/api/v1/sessions", json={
            "model_type": "medium.en",
            "language": "en",
            "enable_emotion_detection": True
        })
        assert create_response.status_code == 200
        session_id = create_response.json()['session_id']
        
        # 2. Start transcription
        start_response = integrated_client.post(f"/api/v1/sessions/{session_id}/start")
        assert start_response.status_code == 200
        
        # 3. Get session info
        info_response = integrated_client.get(f"/api/v1/sessions/{session_id}")
        assert info_response.status_code == 200
        assert info_response.json()['status'] == 'recording'
        
        # 4. Stop transcription
        stop_response = integrated_client.post(f"/api/v1/sessions/{session_id}/stop")
        assert stop_response.status_code == 200
        
        # 5. Delete session
        delete_response = integrated_client.delete(f"/api/v1/sessions/{session_id}")
        assert delete_response.status_code == 200
        
        # 6. Verify deletion
        final_info_response = integrated_client.get(f"/api/v1/sessions/{session_id}")
        assert final_info_response.status_code == 404

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
