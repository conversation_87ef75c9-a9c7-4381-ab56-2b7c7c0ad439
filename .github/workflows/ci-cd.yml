# CI/CD Pipeline for Real-time Voice-to-Text System
# Automated testing, building, and deployment

name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Code Quality and Testing
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y portaudio19-dev python3-pyaudio ffmpeg
    
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov flake8 black mypy
    
    - name: Run code formatting check
      run: |
        black --check .
        flake8 . --max-line-length=100 --exclude=venv,__pycache__,.git
    
    - name: Run type checking
      run: |
        mypy . --ignore-missing-imports
    
    - name: Run unit tests
      run: |
        python scripts/run_tests.py --unit-only
    
    - name: Run integration tests
      run: |
        python scripts/run_tests.py --performance-only
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  # Security Scanning
  security:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # Build Docker Image
  build:
    runs-on: ubuntu-latest
    needs: [test, security]
    permissions:
      contents: read
      packages: write
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Deploy to Staging
  deploy-staging:
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-west-2
    
    - name: Deploy to EKS Staging
      run: |
        aws eks update-kubeconfig --region us-west-2 --name realstt-staging
        kubectl set image deployment/realstt-app realstt=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }} -n realstt-staging
        kubectl rollout status deployment/realstt-app -n realstt-staging --timeout=300s
    
    - name: Run smoke tests
      run: |
        python scripts/smoke_tests.py --endpoint https://staging.realstt.com

  # Deploy to Production
  deploy-production:
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'release'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-west-2
    
    - name: Deploy to EKS Production
      run: |
        aws eks update-kubeconfig --region us-west-2 --name realstt-production
        kubectl set image deployment/realstt-app realstt=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.ref_name }} -n realstt-production
        kubectl rollout status deployment/realstt-app -n realstt-production --timeout=600s
    
    - name: Run production health checks
      run: |
        python scripts/health_check.py --endpoint https://api.realstt.com
    
    - name: Notify deployment success
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: "🚀 Real-time Voice-to-Text System deployed to production successfully!"
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Performance Benchmarking
  benchmark:
    runs-on: self-hosted
    needs: build
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
    
    - name: Run performance benchmarks
      run: |
        python scripts/benchmark.py --output benchmark-results.json
    
    - name: Upload benchmark results
      uses: actions/upload-artifact@v3
      with:
        name: benchmark-results
        path: benchmark-results.json
    
    - name: Comment benchmark results
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const results = JSON.parse(fs.readFileSync('benchmark-results.json', 'utf8'));
          
          const comment = `## 📊 Performance Benchmark Results
          
          | Metric | Value |
          |--------|-------|
          | Average Latency | ${results.latency_tests['2.0s_audio'].avg_latency_seconds * 1000:.1f}ms |
          | Real-time Factor | ${results.latency_tests['2.0s_audio'].real_time_factor:.2f}x |
          | GPU Memory Usage | ${results.memory_usage.gpu?.total_usage_mb || 'N/A'}MB |
          | Overall Coverage | ${results.coverage_report?.overall_coverage || 'N/A'}% |
          
          🎯 Target: <200ms latency, >1.0x real-time factor
          `;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });

  # Cleanup
  cleanup:
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    
    steps:
    - name: Clean up old images
      run: |
        # Keep only the last 10 images
        echo "Cleaning up old container images..."
        # This would typically involve calling container registry APIs
        # to remove old images based on tags and creation dates
