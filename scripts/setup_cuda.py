"""
CUDA Setup Helper
Checks CUDA installation, verifies GPU compatibility, and optimizes settings
"""

import sys
import subprocess
import platform
from pathlib import Path
import json

def check_python_version():
    """Check Python version compatibility"""
    print("🐍 Checking Python version...")
    version = sys.version_info
    
    if version.major != 3 or version.minor < 9:
        print(f"❌ Python {version.major}.{version.minor} detected. Python 3.9+ required.")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
    return True

def check_cuda_installation():
    """Check CUDA installation"""
    print("\n🎮 Checking CUDA installation...")
    
    try:
        # Check nvidia-smi
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ nvidia-smi not found. NVIDIA drivers may not be installed.")
            return False
        
        print("✅ NVIDIA drivers detected")
        
        # Parse nvidia-smi output for GPU info
        lines = result.stdout.split('\n')
        for line in lines:
            if 'GTX 1660 Ti' in line or 'GeForce GTX 1660 Ti' in line:
                print("✅ GTX 1660 Ti detected")
                break
        else:
            print("⚠️  GTX 1660 Ti not detected. System may work with other GPUs.")
        
        # Check CUDA version
        for line in lines:
            if 'CUDA Version:' in line:
                cuda_version = line.split('CUDA Version:')[1].strip().split()[0]
                print(f"✅ CUDA Version: {cuda_version}")
                break
        
        return True
        
    except FileNotFoundError:
        print("❌ nvidia-smi not found. NVIDIA drivers not installed.")
        return False
    except Exception as e:
        print(f"❌ Error checking CUDA: {str(e)}")
        return False

def check_pytorch_cuda():
    """Check PyTorch CUDA support"""
    print("\n🔥 Checking PyTorch CUDA support...")
    
    try:
        import torch
        
        print(f"✅ PyTorch version: {torch.__version__}")
        
        if torch.cuda.is_available():
            print("✅ CUDA is available in PyTorch")
            
            device_count = torch.cuda.device_count()
            print(f"✅ CUDA devices available: {device_count}")
            
            for i in range(device_count):
                props = torch.cuda.get_device_properties(i)
                memory_gb = props.total_memory / (1024**3)
                print(f"   Device {i}: {props.name} ({memory_gb:.1f}GB VRAM)")
                
                if 'GTX 1660 Ti' in props.name:
                    print(f"   ✅ GTX 1660 Ti confirmed with {memory_gb:.1f}GB VRAM")
            
            # Test CUDA functionality
            try:
                x = torch.randn(100, 100).cuda()
                y = torch.randn(100, 100).cuda()
                z = torch.mm(x, y)
                print("✅ CUDA tensor operations working")
                
                # Memory test
                allocated = torch.cuda.memory_allocated(0) / (1024**2)  # MB
                print(f"✅ GPU memory test passed ({allocated:.1f}MB allocated)")
                
            except Exception as e:
                print(f"❌ CUDA tensor operations failed: {str(e)}")
                return False
            
        else:
            print("❌ CUDA not available in PyTorch")
            return False
        
        return True
        
    except ImportError:
        print("❌ PyTorch not installed")
        return False
    except Exception as e:
        print(f"❌ Error checking PyTorch CUDA: {str(e)}")
        return False

def check_audio_dependencies():
    """Check audio processing dependencies"""
    print("\n🎵 Checking audio dependencies...")
    
    dependencies = [
        ('pyaudio', 'PyAudio'),
        ('sounddevice', 'SoundDevice'),
        ('librosa', 'Librosa'),
        ('numpy', 'NumPy'),
        ('scipy', 'SciPy')
    ]
    
    all_good = True
    
    for module_name, display_name in dependencies:
        try:
            __import__(module_name)
            print(f"✅ {display_name} installed")
        except ImportError:
            print(f"❌ {display_name} not installed")
            all_good = False
    
    return all_good

def check_whisper_dependencies():
    """Check Whisper and RealtimeSTT dependencies"""
    print("\n🎤 Checking speech recognition dependencies...")
    
    dependencies = [
        ('whisper', 'OpenAI Whisper'),
        ('RealtimeSTT', 'RealtimeSTT'),
        ('transformers', 'Transformers'),
        ('datasets', 'Datasets')
    ]
    
    all_good = True
    
    for module_name, display_name in dependencies:
        try:
            module = __import__(module_name)
            version = getattr(module, '__version__', 'unknown')
            print(f"✅ {display_name} installed (version: {version})")
        except ImportError:
            print(f"❌ {display_name} not installed")
            all_good = False
    
    return all_good

def check_api_dependencies():
    """Check API dependencies"""
    print("\n🌐 Checking API dependencies...")
    
    dependencies = [
        ('fastapi', 'FastAPI'),
        ('uvicorn', 'Uvicorn'),
        ('websockets', 'WebSockets'),
        ('pydantic', 'Pydantic')
    ]
    
    all_good = True
    
    for module_name, display_name in dependencies:
        try:
            module = __import__(module_name)
            version = getattr(module, '__version__', 'unknown')
            print(f"✅ {display_name} installed (version: {version})")
        except ImportError:
            print(f"❌ {display_name} not installed")
            all_good = False
    
    return all_good

def optimize_cuda_settings():
    """Generate optimized CUDA settings"""
    print("\n⚙️ Generating optimized CUDA settings...")
    
    try:
        import torch
        
        if not torch.cuda.is_available():
            print("❌ CUDA not available, skipping optimization")
            return {}
        
        # Get GPU properties
        props = torch.cuda.get_device_properties(0)
        memory_gb = props.total_memory / (1024**3)
        
        # Generate optimized settings based on GTX 1660 Ti specs
        settings = {
            "model_config": {
                "primary_model": "medium.en" if memory_gb >= 6 else "small.en",
                "realtime_model": "tiny.en",
                "compute_type": "float16",
                "batch_size": 4 if memory_gb >= 6 else 2,
                "beam_size": 5 if memory_gb >= 6 else 3
            },
            "memory_config": {
                "max_vram_usage_gb": min(4.5, memory_gb * 0.75),
                "enable_memory_monitoring": True,
                "aggressive_cleanup": memory_gb < 8
            },
            "performance_config": {
                "target_latency_ms": 200,
                "max_acceptable_latency_ms": 500,
                "vad_sensitivity": 0.7,
                "processing_threads": min(4, torch.get_num_threads())
            }
        }
        
        print("✅ Optimized settings generated:")
        print(f"   Primary model: {settings['model_config']['primary_model']}")
        print(f"   Batch size: {settings['model_config']['batch_size']}")
        print(f"   Max VRAM usage: {settings['memory_config']['max_vram_usage_gb']:.1f}GB")
        
        return settings
        
    except Exception as e:
        print(f"❌ Error generating optimized settings: {str(e)}")
        return {}

def save_optimization_config(settings):
    """Save optimization configuration to file"""
    if not settings:
        return
    
    try:
        config_path = Path("cuda_optimization.json")
        
        with open(config_path, 'w') as f:
            json.dump(settings, f, indent=2)
        
        print(f"✅ Optimization config saved to: {config_path}")
        
    except Exception as e:
        print(f"❌ Error saving config: {str(e)}")

def install_missing_dependencies():
    """Provide installation commands for missing dependencies"""
    print("\n📦 Installation commands for missing dependencies:")
    print("\n# Install PyTorch with CUDA support:")
    print("pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
    
    print("\n# Install audio dependencies:")
    print("pip install pyaudio sounddevice librosa numpy scipy")
    
    print("\n# Install speech recognition:")
    print("pip install openai-whisper RealtimeSTT transformers datasets")
    
    print("\n# Install API dependencies:")
    print("pip install fastapi uvicorn[standard] websockets pydantic")
    
    print("\n# Install all at once:")
    print("pip install -r requirements.txt")

def main():
    """Main setup check function"""
    print("🚀 Real-time Voice-to-Text System - CUDA Setup Check")
    print("=" * 60)
    
    checks = [
        ("Python Version", check_python_version),
        ("CUDA Installation", check_cuda_installation),
        ("PyTorch CUDA", check_pytorch_cuda),
        ("Audio Dependencies", check_audio_dependencies),
        ("Speech Recognition", check_whisper_dependencies),
        ("API Dependencies", check_api_dependencies)
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        results[check_name] = check_func()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Setup Check Summary:")
    
    all_passed = True
    for check_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {check_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All checks passed! System is ready.")
        
        # Generate optimization settings
        settings = optimize_cuda_settings()
        if settings:
            save_optimization_config(settings)
        
        print("\n🚀 You can now run the voice-to-text system:")
        print("   python main.py")
        
    else:
        print("\n⚠️  Some checks failed. Please install missing dependencies.")
        install_missing_dependencies()
        
        print("\n🔄 Run this script again after installing dependencies:")
        print("   python scripts/setup_cuda.py")

if __name__ == "__main__":
    main()
