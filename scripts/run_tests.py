"""
Comprehensive Test Runner and Quality Assurance
Runs all tests, generates coverage reports, and performs quality checks
"""

import subprocess
import sys
import time
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
import argparse

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from utils import get_logger

logger = get_logger(__name__)

class TestRunner:
    """Comprehensive test runner with quality assurance"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.tests_dir = self.project_root / "tests"
        self.results = {
            'timestamp': time.time(),
            'test_results': {},
            'coverage_report': {},
            'quality_checks': {},
            'performance_tests': {},
            'summary': {}
        }
        
        logger.info("TestRunner initialized")
    
    def check_dependencies(self) -> bool:
        """Check if required testing dependencies are installed"""
        print("🔍 Checking test dependencies...")
        
        required_packages = [
            'pytest',
            'pytest-asyncio',
            'pytest-cov',
            'coverage',
            'flake8',
            'black',
            'mypy'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                print(f"✅ {package}")
            except ImportError:
                print(f"❌ {package} - not installed")
                missing_packages.append(package)
        
        if missing_packages:
            print(f"\n📦 Install missing packages:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
        
        print("✅ All test dependencies available")
        return True
    
    def run_unit_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """Run unit tests with pytest"""
        print("\n🧪 Running unit tests...")
        
        cmd = [
            sys.executable, "-m", "pytest",
            str(self.tests_dir),
            "--tb=short",
            "-v" if verbose else "-q",
            "--junit-xml=test_results.xml",
            "--cov=core",
            "--cov=api",
            "--cov=ui",
            "--cov=utils",
            "--cov=models",
            "--cov-report=html:htmlcov",
            "--cov-report=term-missing",
            "--cov-report=json:coverage.json"
        ]
        
        try:
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
            end_time = time.time()
            
            test_results = {
                'exit_code': result.returncode,
                'duration_seconds': end_time - start_time,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'passed': result.returncode == 0
            }
            
            # Parse test results
            if "failed" in result.stdout.lower():
                print("❌ Some tests failed")
            elif result.returncode == 0:
                print("✅ All tests passed")
            else:
                print("⚠️ Tests completed with warnings")
            
            print(f"Duration: {test_results['duration_seconds']:.2f}s")
            
            self.results['test_results'] = test_results
            return test_results
            
        except Exception as e:
            print(f"❌ Error running tests: {str(e)}")
            return {'passed': False, 'error': str(e)}
    
    def run_coverage_analysis(self) -> Dict[str, Any]:
        """Analyze test coverage"""
        print("\n📊 Analyzing test coverage...")
        
        coverage_file = self.project_root / "coverage.json"
        
        if not coverage_file.exists():
            print("❌ Coverage report not found. Run tests first.")
            return {}
        
        try:
            with open(coverage_file, 'r') as f:
                coverage_data = json.load(f)
            
            # Extract summary
            summary = coverage_data.get('totals', {})
            coverage_percent = summary.get('percent_covered', 0)
            
            coverage_results = {
                'overall_coverage': coverage_percent,
                'lines_covered': summary.get('covered_lines', 0),
                'lines_total': summary.get('num_statements', 0),
                'missing_lines': summary.get('missing_lines', 0),
                'files': {}
            }
            
            # Analyze per-file coverage
            files = coverage_data.get('files', {})
            for filepath, file_data in files.items():
                file_summary = file_data.get('summary', {})
                coverage_results['files'][filepath] = {
                    'coverage': file_summary.get('percent_covered', 0),
                    'lines_covered': file_summary.get('covered_lines', 0),
                    'lines_total': file_summary.get('num_statements', 0)
                }
            
            print(f"📈 Overall coverage: {coverage_percent:.1f}%")
            
            if coverage_percent >= 80:
                print("✅ Good coverage (≥80%)")
            elif coverage_percent >= 60:
                print("⚠️ Moderate coverage (60-79%)")
            else:
                print("❌ Low coverage (<60%)")
            
            self.results['coverage_report'] = coverage_results
            return coverage_results
            
        except Exception as e:
            print(f"❌ Error analyzing coverage: {str(e)}")
            return {}
    
    def run_code_quality_checks(self) -> Dict[str, Any]:
        """Run code quality checks"""
        print("\n🔍 Running code quality checks...")
        
        quality_results = {}
        
        # Flake8 - Style and error checking
        print("Running flake8...")
        try:
            flake8_cmd = [sys.executable, "-m", "flake8", ".", "--max-line-length=100", "--exclude=venv,__pycache__,.git"]
            flake8_result = subprocess.run(flake8_cmd, capture_output=True, text=True, cwd=self.project_root)
            
            quality_results['flake8'] = {
                'passed': flake8_result.returncode == 0,
                'issues': flake8_result.stdout.count('\n') if flake8_result.stdout else 0,
                'output': flake8_result.stdout
            }
            
            if flake8_result.returncode == 0:
                print("✅ Flake8: No style issues")
            else:
                print(f"⚠️ Flake8: {quality_results['flake8']['issues']} issues found")
                
        except Exception as e:
            quality_results['flake8'] = {'error': str(e)}
            print(f"❌ Flake8 error: {str(e)}")
        
        # Black - Code formatting check
        print("Checking code formatting...")
        try:
            black_cmd = [sys.executable, "-m", "black", "--check", "--diff", "."]
            black_result = subprocess.run(black_cmd, capture_output=True, text=True, cwd=self.project_root)
            
            quality_results['black'] = {
                'passed': black_result.returncode == 0,
                'output': black_result.stdout
            }
            
            if black_result.returncode == 0:
                print("✅ Black: Code formatting is correct")
            else:
                print("⚠️ Black: Code formatting issues found")
                
        except Exception as e:
            quality_results['black'] = {'error': str(e)}
            print(f"❌ Black error: {str(e)}")
        
        # MyPy - Type checking (optional)
        print("Running type checking...")
        try:
            mypy_cmd = [sys.executable, "-m", "mypy", ".", "--ignore-missing-imports"]
            mypy_result = subprocess.run(mypy_cmd, capture_output=True, text=True, cwd=self.project_root)
            
            quality_results['mypy'] = {
                'passed': mypy_result.returncode == 0,
                'issues': mypy_result.stdout.count('error:') if mypy_result.stdout else 0,
                'output': mypy_result.stdout
            }
            
            if mypy_result.returncode == 0:
                print("✅ MyPy: No type issues")
            else:
                print(f"⚠️ MyPy: {quality_results['mypy']['issues']} type issues found")
                
        except Exception as e:
            quality_results['mypy'] = {'error': str(e)}
            print(f"❌ MyPy error: {str(e)}")
        
        self.results['quality_checks'] = quality_results
        return quality_results
    
    def run_performance_tests(self) -> Dict[str, Any]:
        """Run performance-specific tests"""
        print("\n⚡ Running performance tests...")
        
        performance_cmd = [
            sys.executable, "-m", "pytest",
            str(self.tests_dir),
            "-k", "performance",
            "-v",
            "--tb=short"
        ]
        
        try:
            start_time = time.time()
            result = subprocess.run(performance_cmd, capture_output=True, text=True, cwd=self.project_root)
            end_time = time.time()
            
            performance_results = {
                'exit_code': result.returncode,
                'duration_seconds': end_time - start_time,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'passed': result.returncode == 0
            }
            
            if result.returncode == 0:
                print("✅ Performance tests passed")
            else:
                print("❌ Performance tests failed")
            
            self.results['performance_tests'] = performance_results
            return performance_results
            
        except Exception as e:
            print(f"❌ Error running performance tests: {str(e)}")
            return {'passed': False, 'error': str(e)}
    
    def run_integration_tests(self) -> Dict[str, Any]:
        """Run integration tests"""
        print("\n🔗 Running integration tests...")
        
        integration_cmd = [
            sys.executable, "-m", "pytest",
            str(self.tests_dir),
            "-k", "integration",
            "-v",
            "--tb=short"
        ]
        
        try:
            result = subprocess.run(integration_cmd, capture_output=True, text=True, cwd=self.project_root)
            
            integration_results = {
                'exit_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'passed': result.returncode == 0
            }
            
            if result.returncode == 0:
                print("✅ Integration tests passed")
            else:
                print("❌ Integration tests failed")
            
            return integration_results
            
        except Exception as e:
            print(f"❌ Error running integration tests: {str(e)}")
            return {'passed': False, 'error': str(e)}
    
    def generate_summary(self) -> Dict[str, Any]:
        """Generate test summary"""
        print("\n📋 Generating test summary...")
        
        summary = {
            'overall_status': 'PASS',
            'test_categories': {},
            'recommendations': []
        }
        
        # Analyze test results
        test_results = self.results.get('test_results', {})
        if not test_results.get('passed', False):
            summary['overall_status'] = 'FAIL'
            summary['recommendations'].append("Fix failing unit tests")
        
        summary['test_categories']['unit_tests'] = test_results.get('passed', False)
        
        # Analyze coverage
        coverage = self.results.get('coverage_report', {})
        coverage_percent = coverage.get('overall_coverage', 0)
        summary['test_categories']['coverage'] = coverage_percent >= 60
        
        if coverage_percent < 80:
            summary['recommendations'].append(f"Improve test coverage (current: {coverage_percent:.1f}%)")
        
        # Analyze code quality
        quality = self.results.get('quality_checks', {})
        flake8_passed = quality.get('flake8', {}).get('passed', False)
        black_passed = quality.get('black', {}).get('passed', False)
        
        summary['test_categories']['code_style'] = flake8_passed and black_passed
        
        if not flake8_passed:
            summary['recommendations'].append("Fix code style issues (flake8)")
        if not black_passed:
            summary['recommendations'].append("Fix code formatting (black)")
        
        # Performance tests
        performance = self.results.get('performance_tests', {})
        summary['test_categories']['performance'] = performance.get('passed', False)
        
        if not performance.get('passed', False):
            summary['recommendations'].append("Fix performance test failures")
        
        # Overall status
        if not all(summary['test_categories'].values()):
            summary['overall_status'] = 'FAIL'
        
        self.results['summary'] = summary
        return summary
    
    def save_results(self, filepath: Optional[str] = None) -> str:
        """Save test results to file"""
        if not filepath:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filepath = f"test_results_{timestamp}.json"
        
        try:
            with open(filepath, 'w') as f:
                json.dump(self.results, f, indent=2, default=str)
            
            print(f"📄 Test results saved to: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"❌ Error saving results: {str(e)}")
            return ""
    
    def print_summary(self):
        """Print test summary"""
        summary = self.results.get('summary', {})
        
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        overall_status = summary.get('overall_status', 'UNKNOWN')
        status_emoji = "✅" if overall_status == 'PASS' else "❌"
        print(f"{status_emoji} Overall Status: {overall_status}")
        
        print("\n📋 Test Categories:")
        categories = summary.get('test_categories', {})
        for category, passed in categories.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"   {category.replace('_', ' ').title()}: {status}")
        
        # Coverage info
        coverage = self.results.get('coverage_report', {})
        if coverage:
            print(f"\n📈 Test Coverage: {coverage.get('overall_coverage', 0):.1f}%")
        
        # Recommendations
        recommendations = summary.get('recommendations', [])
        if recommendations:
            print("\n💡 Recommendations:")
            for rec in recommendations:
                print(f"   • {rec}")
        
        print("\n" + "=" * 60)
    
    def run_all_tests(self, verbose: bool = False) -> bool:
        """Run complete test suite"""
        print("🚀 Running Complete Test Suite")
        print("=" * 60)
        
        # Check dependencies
        if not self.check_dependencies():
            return False
        
        # Run all test categories
        self.run_unit_tests(verbose)
        self.run_coverage_analysis()
        self.run_code_quality_checks()
        self.run_performance_tests()
        
        # Generate summary
        self.generate_summary()
        
        # Print results
        self.print_summary()
        
        # Save results
        self.save_results()
        
        # Return overall success
        return self.results['summary']['overall_status'] == 'PASS'

def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(description="Run comprehensive test suite")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--unit-only", action="store_true", help="Run only unit tests")
    parser.add_argument("--coverage-only", action="store_true", help="Run only coverage analysis")
    parser.add_argument("--quality-only", action="store_true", help="Run only quality checks")
    parser.add_argument("--performance-only", action="store_true", help="Run only performance tests")
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    try:
        if args.unit_only:
            runner.run_unit_tests(args.verbose)
        elif args.coverage_only:
            runner.run_coverage_analysis()
        elif args.quality_only:
            runner.run_code_quality_checks()
        elif args.performance_only:
            runner.run_performance_tests()
        else:
            # Run all tests
            success = runner.run_all_tests(args.verbose)
            
            if success:
                print("\n🎉 All tests passed! System is ready for deployment.")
                sys.exit(0)
            else:
                print("\n⚠️ Some tests failed. Please review and fix issues.")
                sys.exit(1)
                
    except KeyboardInterrupt:
        print("\n\n⏹️ Tests cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test runner error: {str(e)}")
        logger.error(f"Test runner error: {str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
