"""
Audio Input Calibration Tool
Tests microphone input, calibrates audio levels, detects background noise
"""

import time
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Any, Optional, Tuple
import sys
from pathlib import Path
import json

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from config import AudioConfig
from utils import get_logger

logger = get_logger(__name__)

class AudioCalibrator:
    """Audio input calibration and testing tool"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Audio settings
        self.sample_rate = AudioConfig.SAMPLE_RATE
        self.channels = AudioConfig.CHANNELS
        self.chunk_size = AudioConfig.CHUNK_SIZE
        
        # Calibration results
        self.calibration_data = {
            'timestamp': time.time(),
            'sample_rate': self.sample_rate,
            'channels': self.channels,
            'devices': [],
            'background_noise': {},
            'speech_levels': {},
            'recommendations': {}
        }
        
        self.logger.info("AudioCalibrator initialized")
    
    def list_audio_devices(self) -> List[Dict[str, Any]]:
        """List available audio input devices"""
        print("🎤 Scanning audio devices...")
        
        devices = []
        
        try:
            import sounddevice as sd
            
            device_list = sd.query_devices()
            
            print("\nAvailable Audio Input Devices:")
            print("-" * 50)
            
            for i, device in enumerate(device_list):
                if device['max_input_channels'] > 0:
                    device_info = {
                        'index': i,
                        'name': device['name'],
                        'channels': device['max_input_channels'],
                        'sample_rate': device['default_samplerate'],
                        'hostapi': device['hostapi']
                    }
                    devices.append(device_info)
                    
                    print(f"{i:2d}: {device['name']}")
                    print(f"     Channels: {device['max_input_channels']}")
                    print(f"     Sample Rate: {device['default_samplerate']:.0f} Hz")
                    print(f"     Host API: {sd.query_hostapis()[device['hostapi']]['name']}")
                    print()
            
            # Get default device
            default_device = sd.default.device[0]
            if default_device is not None:
                print(f"Default Input Device: {default_device} - {device_list[default_device]['name']}")
            
            self.calibration_data['devices'] = devices
            return devices
            
        except ImportError:
            print("❌ sounddevice not installed. Cannot list audio devices.")
            return []
        except Exception as e:
            print(f"❌ Error listing audio devices: {str(e)}")
            return []
    
    def test_device_functionality(self, device_index: Optional[int] = None) -> Dict[str, Any]:
        """Test basic functionality of an audio device"""
        print(f"🔧 Testing device functionality...")
        
        try:
            import sounddevice as sd
            
            device_name = "default"
            if device_index is not None:
                device_info = sd.query_devices(device_index)
                device_name = device_info['name']
            
            print(f"Testing device: {device_name}")
            
            # Test recording
            duration = 2.0
            print(f"Recording {duration} seconds of audio...")
            
            audio_data = sd.rec(
                int(duration * self.sample_rate),
                samplerate=self.sample_rate,
                channels=self.channels,
                device=device_index,
                dtype=np.float32
            )
            sd.wait()
            
            # Analyze recorded audio
            audio_flat = audio_data.flatten()
            
            results = {
                'device_index': device_index,
                'device_name': device_name,
                'duration': duration,
                'samples_recorded': len(audio_flat),
                'rms_level': float(np.sqrt(np.mean(audio_flat ** 2))),
                'peak_level': float(np.max(np.abs(audio_flat))),
                'dynamic_range_db': float(20 * np.log10(np.max(np.abs(audio_flat)) / (np.sqrt(np.mean(audio_flat ** 2)) + 1e-10))),
                'clipping_detected': bool(np.any(np.abs(audio_flat) > 0.95)),
                'silence_detected': bool(np.sqrt(np.mean(audio_flat ** 2)) < 0.001),
                'status': 'success'
            }
            
            print(f"✅ Recording successful")
            print(f"   RMS Level: {results['rms_level']:.4f}")
            print(f"   Peak Level: {results['peak_level']:.4f}")
            print(f"   Dynamic Range: {results['dynamic_range_db']:.1f} dB")
            
            if results['clipping_detected']:
                print("⚠️  Clipping detected - reduce input gain")
            
            if results['silence_detected']:
                print("⚠️  Very low signal - check microphone connection")
            
            return results
            
        except Exception as e:
            error_result = {
                'device_index': device_index,
                'error': str(e),
                'status': 'failed'
            }
            print(f"❌ Device test failed: {str(e)}")
            return error_result
    
    def measure_background_noise(self, device_index: Optional[int] = None, duration: float = 10.0) -> Dict[str, Any]:
        """Measure background noise levels"""
        print(f"🔇 Measuring background noise for {duration} seconds...")
        print("Please remain quiet during this measurement.")
        
        try:
            import sounddevice as sd
            import librosa
            
            # Record background noise
            print("Recording background noise...")
            audio_data = sd.rec(
                int(duration * self.sample_rate),
                samplerate=self.sample_rate,
                channels=self.channels,
                device=device_index,
                dtype=np.float32
            )
            sd.wait()
            
            audio_flat = audio_data.flatten()
            
            # Analyze noise characteristics
            rms_level = np.sqrt(np.mean(audio_flat ** 2))
            peak_level = np.max(np.abs(audio_flat))
            
            # Spectral analysis
            fft = np.fft.rfft(audio_flat)
            magnitude_spectrum = np.abs(fft)
            frequencies = np.fft.rfftfreq(len(audio_flat), 1/self.sample_rate)
            
            # Find dominant frequencies
            peak_freq_idx = np.argmax(magnitude_spectrum)
            dominant_frequency = frequencies[peak_freq_idx]
            
            # Calculate noise floor in different frequency bands
            low_freq_mask = frequencies < 500
            mid_freq_mask = (frequencies >= 500) & (frequencies < 2000)
            high_freq_mask = frequencies >= 2000
            
            noise_results = {
                'duration': duration,
                'rms_level': float(rms_level),
                'peak_level': float(peak_level),
                'rms_db': float(20 * np.log10(rms_level + 1e-10)),
                'peak_db': float(20 * np.log10(peak_level + 1e-10)),
                'dominant_frequency_hz': float(dominant_frequency),
                'noise_floor': {
                    'low_freq_db': float(20 * np.log10(np.mean(magnitude_spectrum[low_freq_mask]) + 1e-10)),
                    'mid_freq_db': float(20 * np.log10(np.mean(magnitude_spectrum[mid_freq_mask]) + 1e-10)),
                    'high_freq_db': float(20 * np.log10(np.mean(magnitude_spectrum[high_freq_mask]) + 1e-10))
                },
                'quality_assessment': self._assess_noise_quality(rms_level, peak_level)
            }
            
            print(f"✅ Background noise measurement complete")
            print(f"   RMS Level: {noise_results['rms_db']:.1f} dB")
            print(f"   Peak Level: {noise_results['peak_db']:.1f} dB")
            print(f"   Quality: {noise_results['quality_assessment']}")
            
            self.calibration_data['background_noise'] = noise_results
            return noise_results
            
        except Exception as e:
            print(f"❌ Background noise measurement failed: {str(e)}")
            return {'error': str(e)}
    
    def measure_speech_levels(self, device_index: Optional[int] = None, duration: float = 15.0) -> Dict[str, Any]:
        """Measure speech levels for calibration"""
        print(f"🗣️ Measuring speech levels for {duration} seconds...")
        print("Please speak normally into the microphone.")
        print("Try saying: 'Hello, this is a test of my microphone levels.'")
        
        try:
            import sounddevice as sd
            from core.audio_utils import AudioUtils
            
            audio_utils = AudioUtils()
            
            # Record speech
            print("Recording speech...")
            audio_data = sd.rec(
                int(duration * self.sample_rate),
                samplerate=self.sample_rate,
                channels=self.channels,
                device=device_index,
                dtype=np.float32
            )
            sd.wait()
            
            audio_flat = audio_data.flatten()
            
            # Voice activity detection
            vad_result = audio_utils.detect_voice_activity(audio_flat)
            speech_segments = audio_flat[vad_result] if np.any(vad_result) else audio_flat
            
            # Analyze speech characteristics
            if len(speech_segments) > 0:
                speech_rms = np.sqrt(np.mean(speech_segments ** 2))
                speech_peak = np.max(np.abs(speech_segments))
                speech_ratio = len(speech_segments) / len(audio_flat)
            else:
                speech_rms = np.sqrt(np.mean(audio_flat ** 2))
                speech_peak = np.max(np.abs(audio_flat))
                speech_ratio = 0.0
            
            # Calculate SNR (Signal-to-Noise Ratio)
            background_noise = self.calibration_data.get('background_noise', {})
            noise_rms = background_noise.get('rms_level', 0.001)
            snr_db = 20 * np.log10(speech_rms / (noise_rms + 1e-10))
            
            speech_results = {
                'duration': duration,
                'speech_rms': float(speech_rms),
                'speech_peak': float(speech_peak),
                'speech_rms_db': float(20 * np.log10(speech_rms + 1e-10)),
                'speech_peak_db': float(20 * np.log10(speech_peak + 1e-10)),
                'speech_activity_ratio': float(speech_ratio),
                'snr_db': float(snr_db),
                'quality_assessment': self._assess_speech_quality(speech_rms, speech_peak, snr_db, speech_ratio)
            }
            
            print(f"✅ Speech level measurement complete")
            print(f"   Speech RMS: {speech_results['speech_rms_db']:.1f} dB")
            print(f"   Speech Peak: {speech_results['speech_peak_db']:.1f} dB")
            print(f"   SNR: {speech_results['snr_db']:.1f} dB")
            print(f"   Speech Activity: {speech_results['speech_activity_ratio']:.1%}")
            print(f"   Quality: {speech_results['quality_assessment']}")
            
            self.calibration_data['speech_levels'] = speech_results
            return speech_results
            
        except Exception as e:
            print(f"❌ Speech level measurement failed: {str(e)}")
            return {'error': str(e)}
    
    def _assess_noise_quality(self, rms_level: float, peak_level: float) -> str:
        """Assess background noise quality"""
        rms_db = 20 * np.log10(rms_level + 1e-10)
        
        if rms_db < -60:
            return "Excellent (Very quiet environment)"
        elif rms_db < -50:
            return "Good (Quiet environment)"
        elif rms_db < -40:
            return "Fair (Some background noise)"
        elif rms_db < -30:
            return "Poor (Noisy environment)"
        else:
            return "Very Poor (Very noisy environment)"
    
    def _assess_speech_quality(self, rms_level: float, peak_level: float, snr_db: float, speech_ratio: float) -> str:
        """Assess speech quality"""
        issues = []
        
        if snr_db < 10:
            issues.append("Low SNR")
        if peak_level > 0.9:
            issues.append("Clipping risk")
        if rms_level < 0.01:
            issues.append("Too quiet")
        if speech_ratio < 0.3:
            issues.append("Low speech activity")
        
        if not issues:
            if snr_db > 20:
                return "Excellent"
            elif snr_db > 15:
                return "Good"
            else:
                return "Fair"
        else:
            return f"Issues: {', '.join(issues)}"
    
    def generate_recommendations(self) -> Dict[str, List[str]]:
        """Generate calibration recommendations"""
        print("💡 Generating recommendations...")
        
        recommendations = {
            'microphone_setup': [],
            'environment': [],
            'software_settings': []
        }
        
        # Analyze background noise
        noise_data = self.calibration_data.get('background_noise', {})
        if noise_data:
            noise_rms_db = noise_data.get('rms_db', 0)
            
            if noise_rms_db > -40:
                recommendations['environment'].append("Reduce background noise - find a quieter location")
                recommendations['environment'].append("Consider acoustic treatment (curtains, carpets)")
            
            if noise_data.get('dominant_frequency_hz', 0) < 100:
                recommendations['environment'].append("Low-frequency noise detected - check for fans, AC, traffic")
        
        # Analyze speech levels
        speech_data = self.calibration_data.get('speech_levels', {})
        if speech_data:
            speech_rms_db = speech_data.get('speech_rms_db', 0)
            snr_db = speech_data.get('snr_db', 0)
            
            if speech_rms_db < -30:
                recommendations['microphone_setup'].append("Increase microphone gain or move closer to microphone")
            elif speech_rms_db > -10:
                recommendations['microphone_setup'].append("Decrease microphone gain or move away from microphone")
            
            if snr_db < 15:
                recommendations['microphone_setup'].append("Improve signal-to-noise ratio")
                recommendations['environment'].append("Reduce background noise")
            
            if speech_data.get('speech_activity_ratio', 0) < 0.3:
                recommendations['microphone_setup'].append("Speak more consistently during recording")
        
        # Software recommendations
        if speech_data.get('snr_db', 0) < 20:
            recommendations['software_settings'].append("Consider enabling noise reduction")
            recommendations['software_settings'].append("Adjust VAD sensitivity to 0.6-0.8")
        
        if not any(recommendations.values()):
            recommendations['microphone_setup'].append("Audio setup looks good!")
        
        self.calibration_data['recommendations'] = recommendations
        return recommendations
    
    def save_calibration_data(self, filepath: Optional[str] = None) -> str:
        """Save calibration data to file"""
        if not filepath:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filepath = f"audio_calibration_{timestamp}.json"
        
        try:
            with open(filepath, 'w') as f:
                json.dump(self.calibration_data, f, indent=2, default=str)
            
            print(f"📄 Calibration data saved to: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"❌ Error saving calibration data: {str(e)}")
            return ""
    
    def run_full_calibration(self, device_index: Optional[int] = None) -> Dict[str, Any]:
        """Run complete audio calibration"""
        print("🎤 Starting Audio Calibration")
        print("=" * 50)
        
        # List devices
        devices = self.list_audio_devices()
        
        if device_index is None and devices:
            try:
                device_choice = input(f"\nSelect device index (0-{len(devices)-1}, or press Enter for default): ").strip()
                if device_choice:
                    device_index = int(device_choice)
            except (ValueError, KeyboardInterrupt):
                print("Using default device")
        
        # Test device functionality
        device_test = self.test_device_functionality(device_index)
        
        if device_test.get('status') != 'success':
            print("❌ Device test failed. Cannot continue calibration.")
            return self.calibration_data
        
        # Measure background noise
        input("\nPress Enter when ready to measure background noise (stay quiet)...")
        self.measure_background_noise(device_index)
        
        # Measure speech levels
        input("\nPress Enter when ready to measure speech levels (speak normally)...")
        self.measure_speech_levels(device_index)
        
        # Generate recommendations
        self.generate_recommendations()
        
        # Print summary
        self.print_calibration_summary()
        
        return self.calibration_data
    
    def print_calibration_summary(self):
        """Print calibration summary"""
        print("\n" + "=" * 50)
        print("📊 AUDIO CALIBRATION SUMMARY")
        print("=" * 50)
        
        # Background noise
        noise_data = self.calibration_data.get('background_noise', {})
        if noise_data:
            print(f"🔇 Background Noise: {noise_data.get('rms_db', 0):.1f} dB - {noise_data.get('quality_assessment', 'Unknown')}")
        
        # Speech levels
        speech_data = self.calibration_data.get('speech_levels', {})
        if speech_data:
            print(f"🗣️ Speech Level: {speech_data.get('speech_rms_db', 0):.1f} dB")
            print(f"📊 SNR: {speech_data.get('snr_db', 0):.1f} dB - {speech_data.get('quality_assessment', 'Unknown')}")
        
        # Recommendations
        recommendations = self.calibration_data.get('recommendations', {})
        if recommendations:
            print("\n💡 Recommendations:")
            for category, recs in recommendations.items():
                if recs:
                    print(f"\n{category.replace('_', ' ').title()}:")
                    for rec in recs:
                        print(f"   • {rec}")
        
        print("\n" + "=" * 50)

def main():
    """Main calibration function"""
    calibrator = AudioCalibrator()
    
    try:
        # Run calibration
        results = calibrator.run_full_calibration()
        
        # Save results
        filepath = calibrator.save_calibration_data()
        
        print(f"\n🎉 Audio calibration complete! Results saved to {filepath}")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Calibration cancelled by user")
    except Exception as e:
        print(f"\n❌ Calibration failed: {str(e)}")
        logger.error(f"Calibration error: {str(e)}", exc_info=True)

if __name__ == "__main__":
    main()
