"""
Model Downloader
Downloads and verifies required AI models for the voice-to-text system
"""

import os
import sys
import hashlib
import requests
from pathlib import Path
from typing import Dict, List, Optional
import json
import time

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from config import MODELS_DIR
from utils import get_logger

logger = get_logger(__name__)

class ModelDownloader:
    """Downloads and manages AI models"""
    
    def __init__(self):
        self.models_dir = MODELS_DIR
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        # Model configurations
        self.whisper_models = {
            'tiny.en': {
                'size_mb': 39,
                'description': 'Fastest, lowest accuracy',
                'vram_mb': 200
            },
            'base.en': {
                'size_mb': 74,
                'description': 'Fast, good accuracy',
                'vram_mb': 300
            },
            'small.en': {
                'size_mb': 244,
                'description': 'Balanced speed/accuracy',
                'vram_mb': 500
            },
            'medium.en': {
                'size_mb': 769,
                'description': 'High accuracy, slower',
                'vram_mb': 1500
            },
            'large': {
                'size_mb': 1550,
                'description': 'Highest accuracy, slowest',
                'vram_mb': 3000
            }
        }
        
        self.emotion_models = {
            'distilroberta-emotion': {
                'url': 'https://huggingface.co/j-hartmann/emotion-english-distilroberta-base',
                'size_mb': 82,
                'description': 'DistilRoBERTa emotion classifier',
                'local_path': 'emotion_models/distilroberta'
            }
        }
        
        logger.info("ModelDownloader initialized")
    
    def check_whisper_models(self) -> Dict[str, bool]:
        """Check which Whisper models are available locally"""
        try:
            import whisper
            available_models = {}
            
            for model_name in self.whisper_models.keys():
                try:
                    # Check if model is downloaded
                    model_path = whisper._download._MODELS[model_name]
                    cache_dir = whisper._download.download_root()
                    full_path = os.path.join(cache_dir, os.path.basename(model_path))
                    
                    available_models[model_name] = os.path.exists(full_path)
                except:
                    available_models[model_name] = False
            
            return available_models
            
        except ImportError:
            logger.error("Whisper not installed")
            return {}
    
    def download_whisper_model(self, model_name: str) -> bool:
        """Download a specific Whisper model"""
        if model_name not in self.whisper_models:
            logger.error(f"Unknown Whisper model: {model_name}")
            return False
        
        try:
            import whisper
            
            model_info = self.whisper_models[model_name]
            print(f"📥 Downloading Whisper model: {model_name}")
            print(f"   Size: {model_info['size_mb']}MB")
            print(f"   Description: {model_info['description']}")
            print(f"   VRAM usage: ~{model_info['vram_mb']}MB")
            
            start_time = time.time()
            
            # Download model (this will cache it automatically)
            model = whisper.load_model(model_name)
            
            download_time = time.time() - start_time
            print(f"✅ Downloaded {model_name} in {download_time:.1f} seconds")
            
            # Clean up model from memory
            del model
            
            return True
            
        except Exception as e:
            logger.error(f"Error downloading {model_name}: {str(e)}")
            return False
    
    def download_recommended_models(self, gpu_memory_gb: float = 6.0) -> bool:
        """Download recommended models based on GPU memory"""
        print(f"🎯 Downloading recommended models for {gpu_memory_gb}GB GPU...")
        
        # Determine recommended models based on GPU memory
        if gpu_memory_gb >= 8:
            recommended = ['tiny.en', 'small.en', 'medium.en']
        elif gpu_memory_gb >= 6:
            recommended = ['tiny.en', 'small.en', 'medium.en']
        elif gpu_memory_gb >= 4:
            recommended = ['tiny.en', 'small.en']
        else:
            recommended = ['tiny.en', 'base.en']
        
        print(f"📋 Recommended models: {', '.join(recommended)}")
        
        success_count = 0
        for model_name in recommended:
            if self.download_whisper_model(model_name):
                success_count += 1
            else:
                print(f"❌ Failed to download {model_name}")
        
        print(f"\n✅ Successfully downloaded {success_count}/{len(recommended)} models")
        return success_count == len(recommended)
    
    def download_emotion_models(self) -> bool:
        """Download emotion detection models"""
        print("😊 Downloading emotion detection models...")
        
        try:
            from transformers import pipeline
            
            # Download the emotion classification model
            print("📥 Downloading DistilRoBERTa emotion classifier...")
            
            emotion_classifier = pipeline(
                "text-classification",
                model="j-hartmann/emotion-english-distilroberta-base",
                return_all_scores=True
            )
            
            print("✅ Emotion model downloaded and cached")
            
            # Test the model
            test_result = emotion_classifier("I am happy today!")
            print(f"✅ Model test successful: {test_result[0]['label']}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error downloading emotion models: {str(e)}")
            return False
    
    def verify_model_integrity(self, model_name: str) -> bool:
        """Verify model file integrity"""
        try:
            if model_name in self.whisper_models:
                import whisper
                
                # Try to load the model
                model = whisper.load_model(model_name)
                
                # Basic functionality test
                audio = whisper.pad_or_trim(whisper.log_mel_spectrogram(
                    whisper.audio.load_audio(whisper.audio._SAMPLE_RATE * [0.0])
                ))
                
                # This should not raise an exception
                _ = model.encode(audio.unsqueeze(0))
                
                del model
                return True
                
        except Exception as e:
            logger.error(f"Model verification failed for {model_name}: {str(e)}")
            return False
    
    def get_model_info(self) -> Dict[str, Dict]:
        """Get information about all available models"""
        info = {
            'whisper_models': {},
            'emotion_models': {},
            'total_size_mb': 0
        }
        
        # Check Whisper models
        available_whisper = self.check_whisper_models()
        for model_name, model_config in self.whisper_models.items():
            info['whisper_models'][model_name] = {
                **model_config,
                'downloaded': available_whisper.get(model_name, False),
                'verified': False
            }
            
            if available_whisper.get(model_name, False):
                info['whisper_models'][model_name]['verified'] = self.verify_model_integrity(model_name)
                info['total_size_mb'] += model_config['size_mb']
        
        # Check emotion models
        try:
            from transformers import pipeline
            # Try to load emotion model to check if it's available
            pipeline("text-classification", model="j-hartmann/emotion-english-distilroberta-base")
            info['emotion_models']['distilroberta'] = {
                'downloaded': True,
                'verified': True,
                'size_mb': 82
            }
            info['total_size_mb'] += 82
        except:
            info['emotion_models']['distilroberta'] = {
                'downloaded': False,
                'verified': False,
                'size_mb': 82
            }
        
        return info
    
    def cleanup_unused_models(self) -> int:
        """Remove unused or corrupted models"""
        print("🧹 Cleaning up unused models...")
        
        cleaned_count = 0
        
        # Check for corrupted Whisper models
        available_models = self.check_whisper_models()
        for model_name, is_available in available_models.items():
            if is_available and not self.verify_model_integrity(model_name):
                print(f"🗑️  Removing corrupted model: {model_name}")
                try:
                    import whisper
                    cache_dir = whisper._download.download_root()
                    model_path = whisper._download._MODELS[model_name]
                    full_path = os.path.join(cache_dir, os.path.basename(model_path))
                    
                    if os.path.exists(full_path):
                        os.remove(full_path)
                        cleaned_count += 1
                        print(f"✅ Removed {model_name}")
                        
                except Exception as e:
                    print(f"❌ Failed to remove {model_name}: {str(e)}")
        
        return cleaned_count
    
    def estimate_download_time(self, models: List[str]) -> float:
        """Estimate download time for models"""
        total_mb = sum(self.whisper_models[model]['size_mb'] for model in models if model in self.whisper_models)
        
        # Assume 10 MB/s download speed (conservative estimate)
        estimated_seconds = total_mb / 10
        return estimated_seconds
    
    def show_model_recommendations(self, gpu_memory_gb: float):
        """Show model recommendations based on GPU memory"""
        print(f"\n🎯 Model Recommendations for {gpu_memory_gb}GB GPU:")
        print("=" * 50)
        
        if gpu_memory_gb >= 8:
            print("🚀 High-end setup:")
            print("   • Primary: medium.en (best accuracy)")
            print("   • Realtime: tiny.en (fastest response)")
            print("   • Fallback: small.en (balanced)")
            
        elif gpu_memory_gb >= 6:
            print("⚡ GTX 1660 Ti optimized setup:")
            print("   • Primary: medium.en (high accuracy)")
            print("   • Realtime: tiny.en (fast response)")
            print("   • Memory usage: ~2-3GB VRAM")
            
        elif gpu_memory_gb >= 4:
            print("💡 Mid-range setup:")
            print("   • Primary: small.en (good accuracy)")
            print("   • Realtime: tiny.en (fast response)")
            print("   • Memory usage: ~1-2GB VRAM")
            
        else:
            print("🔧 Conservative setup:")
            print("   • Primary: base.en (basic accuracy)")
            print("   • Realtime: tiny.en (fast response)")
            print("   • Memory usage: <1GB VRAM")

def main():
    """Main model download function"""
    print("📦 Real-time Voice-to-Text System - Model Downloader")
    print("=" * 60)
    
    downloader = ModelDownloader()
    
    # Get GPU memory info
    gpu_memory_gb = 6.0  # Default for GTX 1660 Ti
    try:
        import torch
        if torch.cuda.is_available():
            props = torch.cuda.get_device_properties(0)
            gpu_memory_gb = props.total_memory / (1024**3)
            print(f"🎮 Detected GPU: {props.name} ({gpu_memory_gb:.1f}GB)")
        else:
            print("⚠️  No CUDA GPU detected, using CPU mode")
            gpu_memory_gb = 0
    except ImportError:
        print("⚠️  PyTorch not installed, assuming GTX 1660 Ti")
    
    # Show recommendations
    downloader.show_model_recommendations(gpu_memory_gb)
    
    # Check current models
    print("\n📋 Checking current models...")
    model_info = downloader.get_model_info()
    
    print("\nWhisper Models:")
    for model_name, info in model_info['whisper_models'].items():
        status = "✅" if info['downloaded'] else "❌"
        verified = "✓" if info.get('verified', False) else "✗"
        print(f"   {status} {model_name} ({info['size_mb']}MB) - {info['description']} [Verified: {verified}]")
    
    print("\nEmotion Models:")
    for model_name, info in model_info['emotion_models'].items():
        status = "✅" if info['downloaded'] else "❌"
        print(f"   {status} {model_name} ({info['size_mb']}MB)")
    
    print(f"\nTotal downloaded size: {model_info['total_size_mb']:.0f}MB")
    
    # Ask user what to download
    print("\n" + "=" * 60)
    print("Download Options:")
    print("1. Download recommended models for your GPU")
    print("2. Download specific Whisper model")
    print("3. Download emotion detection models")
    print("4. Download all models")
    print("5. Cleanup corrupted models")
    print("6. Exit")
    
    try:
        choice = input("\nEnter your choice (1-6): ").strip()
        
        if choice == "1":
            success = downloader.download_recommended_models(gpu_memory_gb)
            if success:
                print("\n🎉 All recommended models downloaded successfully!")
            else:
                print("\n⚠️  Some models failed to download.")
        
        elif choice == "2":
            print("\nAvailable Whisper models:")
            for i, (model_name, info) in enumerate(downloader.whisper_models.items(), 1):
                print(f"{i}. {model_name} ({info['size_mb']}MB) - {info['description']}")
            
            model_choice = input("Enter model number: ").strip()
            try:
                model_idx = int(model_choice) - 1
                model_name = list(downloader.whisper_models.keys())[model_idx]
                downloader.download_whisper_model(model_name)
            except (ValueError, IndexError):
                print("Invalid choice")
        
        elif choice == "3":
            downloader.download_emotion_models()
        
        elif choice == "4":
            print("📥 Downloading all models...")
            for model_name in downloader.whisper_models.keys():
                downloader.download_whisper_model(model_name)
            downloader.download_emotion_models()
        
        elif choice == "5":
            cleaned = downloader.cleanup_unused_models()
            print(f"🧹 Cleaned up {cleaned} corrupted models")
        
        elif choice == "6":
            print("👋 Goodbye!")
            return
        
        else:
            print("Invalid choice")
    
    except KeyboardInterrupt:
        print("\n\n👋 Download cancelled by user")
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
