"""
Performance Benchmarking Tool
Tests system performance, measures latency and accuracy, generates performance reports
"""

import time
import asyncio
import statistics
from pathlib import Path
from typing import Dict, List, Any, Optional
import json
import sys
from datetime import datetime
import threading

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from config import ModelConfig, AudioConfig, PerformanceConfig
from utils import get_logger

logger = get_logger(__name__)

class PerformanceBenchmark:
    """Comprehensive performance benchmarking suite"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Benchmark results
        self.results = {
            'system_info': {},
            'model_performance': {},
            'audio_processing': {},
            'memory_usage': {},
            'latency_tests': {},
            'accuracy_tests': {},
            'stress_tests': {},
            'recommendations': []
        }
        
        # Test configurations
        self.test_audio_samples = [
            "Hello, this is a test of the speech recognition system.",
            "The quick brown fox jumps over the lazy dog.",
            "In a hole in the ground there lived a hobbit.",
            "To be or not to be, that is the question.",
            "Four score and seven years ago our fathers brought forth."
        ]
        
        self.logger.info("PerformanceBenchmark initialized")
    
    def collect_system_info(self) -> Dict[str, Any]:
        """Collect system information"""
        print("🔍 Collecting system information...")
        
        system_info = {
            'timestamp': datetime.now().isoformat(),
            'python_version': sys.version,
            'platform': sys.platform
        }
        
        # CPU information
        try:
            import psutil
            system_info['cpu'] = {
                'cores': psutil.cpu_count(logical=False),
                'threads': psutil.cpu_count(logical=True),
                'frequency_mhz': psutil.cpu_freq().current if psutil.cpu_freq() else 'unknown'
            }
            
            # Memory information
            memory = psutil.virtual_memory()
            system_info['memory'] = {
                'total_gb': memory.total / (1024**3),
                'available_gb': memory.available / (1024**3)
            }
        except ImportError:
            system_info['cpu'] = 'psutil not available'
            system_info['memory'] = 'psutil not available'
        
        # GPU information
        try:
            import torch
            if torch.cuda.is_available():
                props = torch.cuda.get_device_properties(0)
                system_info['gpu'] = {
                    'name': props.name,
                    'memory_gb': props.total_memory / (1024**3),
                    'compute_capability': f"{props.major}.{props.minor}",
                    'cuda_version': torch.version.cuda
                }
            else:
                system_info['gpu'] = 'CUDA not available'
        except ImportError:
            system_info['gpu'] = 'PyTorch not available'
        
        self.results['system_info'] = system_info
        return system_info
    
    def benchmark_model_loading(self) -> Dict[str, Any]:
        """Benchmark model loading times"""
        print("🤖 Benchmarking model loading...")
        
        model_results = {}
        
        try:
            import torch
            from models import ModelManager
            
            # Test different model configurations
            test_models = ['tiny.en', 'base.en', 'small.en']
            if torch.cuda.is_available():
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                if gpu_memory >= 6:
                    test_models.append('medium.en')
            
            for model_name in test_models:
                print(f"   Testing {model_name}...")
                
                start_time = time.time()
                start_memory = torch.cuda.memory_allocated(0) if torch.cuda.is_available() else 0
                
                try:
                    import whisper
                    model = whisper.load_model(model_name)
                    
                    load_time = time.time() - start_time
                    end_memory = torch.cuda.memory_allocated(0) if torch.cuda.is_available() else 0
                    memory_used = (end_memory - start_memory) / (1024**2)  # MB
                    
                    model_results[model_name] = {
                        'load_time_seconds': load_time,
                        'memory_usage_mb': memory_used,
                        'status': 'success'
                    }
                    
                    # Clean up
                    del model
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                    
                    print(f"     ✅ Loaded in {load_time:.2f}s, {memory_used:.0f}MB VRAM")
                    
                except Exception as e:
                    model_results[model_name] = {
                        'load_time_seconds': 0,
                        'memory_usage_mb': 0,
                        'status': f'failed: {str(e)}'
                    }
                    print(f"     ❌ Failed: {str(e)}")
        
        except ImportError as e:
            model_results['error'] = f"Required modules not available: {str(e)}"
        
        self.results['model_performance'] = model_results
        return model_results
    
    def benchmark_audio_processing(self) -> Dict[str, Any]:
        """Benchmark audio processing performance"""
        print("🎵 Benchmarking audio processing...")
        
        audio_results = {}
        
        try:
            import numpy as np
            import librosa
            from core.audio_utils import AudioUtils
            
            audio_utils = AudioUtils()
            
            # Generate test audio
            sample_rate = AudioConfig.SAMPLE_RATE
            duration = 5.0  # 5 seconds
            test_audio = np.random.randn(int(sample_rate * duration)).astype(np.float32)
            
            # Test different audio processing operations
            operations = {
                'normalize': lambda x: audio_utils.normalize_audio(x),
                'preprocess': lambda x: audio_utils.preprocess_audio(x),
                'noise_reduction': lambda x: audio_utils.reduce_noise(x),
                'feature_extraction': lambda x: audio_utils.analyze_audio_features(x)
            }
            
            for op_name, op_func in operations.items():
                print(f"   Testing {op_name}...")
                
                times = []
                for _ in range(5):  # Run 5 times for average
                    start_time = time.time()
                    try:
                        result = op_func(test_audio.copy())
                        end_time = time.time()
                        times.append(end_time - start_time)
                    except Exception as e:
                        print(f"     ❌ {op_name} failed: {str(e)}")
                        break
                
                if times:
                    avg_time = statistics.mean(times)
                    audio_results[op_name] = {
                        'avg_time_seconds': avg_time,
                        'min_time_seconds': min(times),
                        'max_time_seconds': max(times),
                        'throughput_ratio': duration / avg_time  # Real-time factor
                    }
                    print(f"     ✅ {avg_time:.3f}s avg ({duration/avg_time:.1f}x real-time)")
        
        except ImportError as e:
            audio_results['error'] = f"Audio processing modules not available: {str(e)}"
        
        self.results['audio_processing'] = audio_results
        return audio_results
    
    def benchmark_memory_usage(self) -> Dict[str, Any]:
        """Benchmark memory usage patterns"""
        print("💾 Benchmarking memory usage...")
        
        memory_results = {}
        
        try:
            import torch
            import psutil
            
            if torch.cuda.is_available():
                # GPU memory benchmark
                initial_gpu = torch.cuda.memory_allocated(0)
                
                # Simulate model loading
                import whisper
                model = whisper.load_model('tiny.en')
                
                after_model = torch.cuda.memory_allocated(0)
                model_memory = (after_model - initial_gpu) / (1024**2)
                
                # Simulate processing
                dummy_audio = torch.randn(1, 80, 3000).cuda()  # Mel spectrogram shape
                _ = model.encode(dummy_audio)
                
                after_processing = torch.cuda.memory_allocated(0)
                processing_memory = (after_processing - after_model) / (1024**2)
                
                memory_results['gpu'] = {
                    'model_memory_mb': model_memory,
                    'processing_memory_mb': processing_memory,
                    'total_usage_mb': (after_processing - initial_gpu) / (1024**2),
                    'gpu_total_mb': torch.cuda.get_device_properties(0).total_memory / (1024**2)
                }
                
                # Cleanup
                del model, dummy_audio
                torch.cuda.empty_cache()
            
            # CPU memory benchmark
            process = psutil.Process()
            memory_results['cpu'] = {
                'rss_mb': process.memory_info().rss / (1024**2),
                'vms_mb': process.memory_info().vms / (1024**2),
                'percent': process.memory_percent()
            }
        
        except ImportError as e:
            memory_results['error'] = f"Memory monitoring modules not available: {str(e)}"
        
        self.results['memory_usage'] = memory_results
        return memory_results
    
    def benchmark_latency(self) -> Dict[str, Any]:
        """Benchmark processing latency"""
        print("⏱️ Benchmarking processing latency...")
        
        latency_results = {}
        
        try:
            import whisper
            import torch
            
            # Load model for testing
            model = whisper.load_model('tiny.en')  # Use fastest model for latency test
            
            # Test different audio lengths
            sample_rate = 16000
            test_durations = [1.0, 2.0, 5.0, 10.0]  # seconds
            
            for duration in test_durations:
                print(f"   Testing {duration}s audio...")
                
                # Generate test audio
                audio_length = int(sample_rate * duration)
                test_audio = np.random.randn(audio_length).astype(np.float32)
                
                # Measure transcription latency
                latencies = []
                for _ in range(3):  # Run 3 times
                    start_time = time.time()
                    
                    # Preprocess audio
                    audio = whisper.pad_or_trim(test_audio)
                    mel = whisper.log_mel_spectrogram(audio).to(model.device)
                    
                    # Transcribe
                    with torch.no_grad():
                        _ = model.decode(mel, whisper.DecodingOptions())
                    
                    end_time = time.time()
                    latency = end_time - start_time
                    latencies.append(latency)
                
                avg_latency = statistics.mean(latencies)
                real_time_factor = duration / avg_latency
                
                latency_results[f'{duration}s_audio'] = {
                    'avg_latency_seconds': avg_latency,
                    'min_latency_seconds': min(latencies),
                    'max_latency_seconds': max(latencies),
                    'real_time_factor': real_time_factor,
                    'meets_target': avg_latency < (PerformanceConfig.TARGET_LATENCY / 1000)
                }
                
                status = "✅" if real_time_factor > 1.0 else "⚠️"
                print(f"     {status} {avg_latency:.3f}s ({real_time_factor:.1f}x real-time)")
            
            # Cleanup
            del model
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        
        except ImportError as e:
            latency_results['error'] = f"Latency testing modules not available: {str(e)}"
        
        self.results['latency_tests'] = latency_results
        return latency_results
    
    def benchmark_accuracy(self) -> Dict[str, Any]:
        """Benchmark transcription accuracy (basic test)"""
        print("🎯 Benchmarking transcription accuracy...")
        
        accuracy_results = {}
        
        try:
            import whisper
            
            # Load model for testing
            model = whisper.load_model('base.en')
            
            # Test with known phrases
            test_cases = [
                "hello world",
                "the quick brown fox",
                "testing one two three",
                "artificial intelligence",
                "speech recognition system"
            ]
            
            correct_transcriptions = 0
            total_tests = len(test_cases)
            
            for i, expected_text in enumerate(test_cases):
                print(f"   Testing phrase {i+1}/{total_tests}...")
                
                # Generate synthetic audio (this is a simplified test)
                # In a real benchmark, you'd use actual audio files
                try:
                    # Create dummy mel spectrogram
                    mel = torch.randn(1, 80, 3000)
                    
                    # Transcribe
                    result = model.decode(mel, whisper.DecodingOptions())
                    transcribed_text = result.text.lower().strip()
                    
                    # Simple accuracy check (word overlap)
                    expected_words = set(expected_text.split())
                    transcribed_words = set(transcribed_text.split())
                    
                    if expected_words.intersection(transcribed_words):
                        correct_transcriptions += 1
                        print(f"     ✅ Match found")
                    else:
                        print(f"     ❌ No match")
                
                except Exception as e:
                    print(f"     ❌ Error: {str(e)}")
            
            accuracy_percentage = (correct_transcriptions / total_tests) * 100
            
            accuracy_results = {
                'correct_transcriptions': correct_transcriptions,
                'total_tests': total_tests,
                'accuracy_percentage': accuracy_percentage,
                'note': 'Simplified accuracy test with synthetic data'
            }
            
            print(f"   📊 Accuracy: {accuracy_percentage:.1f}% ({correct_transcriptions}/{total_tests})")
            
            # Cleanup
            del model
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        
        except ImportError as e:
            accuracy_results['error'] = f"Accuracy testing modules not available: {str(e)}"
        
        self.results['accuracy_tests'] = accuracy_results
        return accuracy_results
    
    def generate_recommendations(self) -> List[str]:
        """Generate performance recommendations"""
        print("💡 Generating recommendations...")
        
        recommendations = []
        
        # Check GPU memory
        if 'gpu' in self.results['system_info'] and isinstance(self.results['system_info']['gpu'], dict):
            gpu_memory = self.results['system_info']['gpu'].get('memory_gb', 0)
            
            if gpu_memory < 4:
                recommendations.append("Consider upgrading GPU memory for better model performance")
            elif gpu_memory >= 8:
                recommendations.append("GPU memory is sufficient for large models")
        
        # Check model performance
        if 'model_performance' in self.results:
            for model_name, results in self.results['model_performance'].items():
                if isinstance(results, dict) and results.get('load_time_seconds', 0) > 30:
                    recommendations.append(f"Model {model_name} loads slowly - consider using smaller model")
        
        # Check latency
        if 'latency_tests' in self.results:
            for test_name, results in self.results['latency_tests'].items():
                if isinstance(results, dict) and results.get('real_time_factor', 0) < 1.0:
                    recommendations.append(f"Latency too high for {test_name} - consider optimization")
        
        # Check memory usage
        if 'memory_usage' in self.results and 'gpu' in self.results['memory_usage']:
            gpu_usage = self.results['memory_usage']['gpu']
            if gpu_usage.get('total_usage_mb', 0) > gpu_usage.get('gpu_total_mb', 1) * 0.8:
                recommendations.append("GPU memory usage is high - consider smaller batch sizes")
        
        # Default recommendations
        if not recommendations:
            recommendations.append("System performance looks good!")
            recommendations.append("Consider running stress tests for extended usage scenarios")
        
        self.results['recommendations'] = recommendations
        return recommendations
    
    def run_full_benchmark(self) -> Dict[str, Any]:
        """Run complete benchmark suite"""
        print("🚀 Running Full Performance Benchmark")
        print("=" * 60)
        
        start_time = time.time()
        
        # Run all benchmark tests
        self.collect_system_info()
        self.benchmark_model_loading()
        self.benchmark_audio_processing()
        self.benchmark_memory_usage()
        self.benchmark_latency()
        self.benchmark_accuracy()
        self.generate_recommendations()
        
        total_time = time.time() - start_time
        self.results['benchmark_duration_seconds'] = total_time
        
        print(f"\n✅ Benchmark completed in {total_time:.1f} seconds")
        return self.results
    
    def save_results(self, filepath: Optional[str] = None) -> str:
        """Save benchmark results to file"""
        if not filepath:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = f"benchmark_results_{timestamp}.json"
        
        try:
            with open(filepath, 'w') as f:
                json.dump(self.results, f, indent=2, default=str)
            
            print(f"📄 Results saved to: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"❌ Error saving results: {str(e)}")
            return ""
    
    def print_summary(self):
        """Print benchmark summary"""
        print("\n" + "=" * 60)
        print("📊 BENCHMARK SUMMARY")
        print("=" * 60)
        
        # System info
        if 'system_info' in self.results:
            sys_info = self.results['system_info']
            if 'gpu' in sys_info and isinstance(sys_info['gpu'], dict):
                print(f"🎮 GPU: {sys_info['gpu'].get('name', 'Unknown')} ({sys_info['gpu'].get('memory_gb', 0):.1f}GB)")
            if 'cpu' in sys_info and isinstance(sys_info['cpu'], dict):
                print(f"💻 CPU: {sys_info['cpu'].get('cores', 'Unknown')} cores, {sys_info['cpu'].get('threads', 'Unknown')} threads")
        
        # Performance highlights
        if 'latency_tests' in self.results:
            print("\n⏱️ Latency Performance:")
            for test_name, results in self.results['latency_tests'].items():
                if isinstance(results, dict):
                    rtf = results.get('real_time_factor', 0)
                    status = "✅" if rtf > 1.0 else "⚠️"
                    print(f"   {status} {test_name}: {rtf:.1f}x real-time")
        
        # Recommendations
        if 'recommendations' in self.results:
            print("\n💡 Recommendations:")
            for rec in self.results['recommendations']:
                print(f"   • {rec}")
        
        print("\n" + "=" * 60)

def main():
    """Main benchmark function"""
    benchmark = PerformanceBenchmark()
    
    try:
        # Run benchmark
        results = benchmark.run_full_benchmark()
        
        # Print summary
        benchmark.print_summary()
        
        # Save results
        filepath = benchmark.save_results()
        
        print(f"\n🎉 Benchmark complete! Results saved to {filepath}")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Benchmark cancelled by user")
    except Exception as e:
        print(f"\n❌ Benchmark failed: {str(e)}")
        logger.error(f"Benchmark error: {str(e)}", exc_info=True)

if __name__ == "__main__":
    main()
