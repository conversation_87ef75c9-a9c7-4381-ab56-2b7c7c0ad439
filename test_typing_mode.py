#!/usr/bin/env python3
"""
Test script for the clean typing mode interface
"""

import asyncio
import time
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ui.console_display import ConsoleDisplay
from dataclasses import dataclass

@dataclass
class MockResult:
    text: str
    confidence: float
    timestamp: float
    is_final: bool = False
    emotion: dict = None

async def test_typing_effect():
    """Test the typing effect with mock data"""
    
    # Create clean mode display
    display = ConsoleDisplay(clean_mode=True)
    display.start_display()
    
    # Hide cursor for clean display
    print("\033[?25l", end='')
    
    try:
        # Simulate typing effect
        test_phrases = [
            "Hello there",
            "Hello there, how",
            "Hello there, how are",
            "Hello there, how are you",
            "Hello there, how are you doing",
            "Hello there, how are you doing today?",
        ]
        
        print("Testing typing effect...")
        await asyncio.sleep(2)
        
        # Show progressive typing
        for i, phrase in enumerate(test_phrases):
            result = MockResult(
                text=phrase,
                confidence=0.8,
                timestamp=time.time(),
                is_final=(i == len(test_phrases) - 1)
            )
            display.display_realtime_text(result)
            await asyncio.sleep(0.5)
        
        # Final result
        final_result = MockResult(
            text="Hello there, how are you doing today?",
            confidence=0.95,
            timestamp=time.time(),
            is_final=True
        )
        display.display_final_text(final_result)
        
        await asyncio.sleep(2)
        
        # Add more text to test paragraph flow
        more_phrases = [
            "I'm testing the",
            "I'm testing the typing",
            "I'm testing the typing mode",
            "I'm testing the typing mode interface",
            "I'm testing the typing mode interface to see",
            "I'm testing the typing mode interface to see how",
            "I'm testing the typing mode interface to see how well",
            "I'm testing the typing mode interface to see how well it works",
            "I'm testing the typing mode interface to see how well it works with continuous speech.",
        ]
        
        for i, phrase in enumerate(more_phrases):
            result = MockResult(
                text=phrase,
                confidence=0.8,
                timestamp=time.time(),
                is_final=(i == len(more_phrases) - 1)
            )
            display.display_realtime_text(result)
            await asyncio.sleep(0.3)
        
        # Final result for second sentence
        final_result2 = MockResult(
            text="I'm testing the typing mode interface to see how well it works with continuous speech.",
            confidence=0.95,
            timestamp=time.time(),
            is_final=True
        )
        display.display_final_text(final_result2)
        
        # Keep running for a bit to see the cursor blink
        await asyncio.sleep(5)
        
    finally:
        display.stop_display()
        # Show cursor on exit
        print("\033[?25h", end='')
        print("\nTest completed!")

if __name__ == "__main__":
    asyncio.run(test_typing_effect())
