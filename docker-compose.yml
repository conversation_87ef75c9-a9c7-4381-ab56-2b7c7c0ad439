# Docker Compose for Real-time Voice-to-Text System
# Includes main application, database, monitoring, and optional services

version: '3.8'

services:
  # Main voice-to-text application
  voice-to-text:
    build: .
    container_name: realstt-app
    ports:
      - "8000:8000"
      - "8001:8001"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./models:/app/models
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - PYTHONUNBUFFERED=1
      - LOG_LEVEL=INFO
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    depends_on:
      - redis
      - postgres
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: realstt-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL for persistent data storage
  postgres:
    image: postgres:15-alpine
    container_name: realstt-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=realstt
      - POSTGRES_USER=realstt
      - POSTGRES_PASSWORD=realstt_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U realstt"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy and load balancer
  nginx:
    image: nginx:alpine
    container_name: realstt-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - voice-to-text
    restart: unless-stopped

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: realstt-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: realstt-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    restart: unless-stopped

  # Optional: Elasticsearch for log aggregation
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: realstt-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    restart: unless-stopped
    profiles:
      - logging

  # Optional: Kibana for log visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: realstt-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    restart: unless-stopped
    profiles:
      - logging

  # Optional: Filebeat for log shipping
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.8.0
    container_name: realstt-filebeat
    user: root
    volumes:
      - ./logs:/app/logs:ro
      - ./monitoring/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    depends_on:
      - elasticsearch
    restart: unless-stopped
    profiles:
      - logging

volumes:
  redis_data:
  postgres_data:
  prometheus_data:
  grafana_data:
  elasticsearch_data:

networks:
  default:
    name: realstt-network
