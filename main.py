"""
Real-time Voice-to-Text System - Main Application
Entry point for the comprehensive voice-to-text system with emotion detection
"""

import asyncio
import argparse
import signal
import sys
import time
from pathlib import Path
from typing import Optional

# Core components
from models.model_manager import ModelManager
from core.voice_processor import VoiceProcessor
from core.emotion_detector import EmotionDetector
from core.text_formatter import TextFormatter
from ui.console_display import ConsoleDisplay
from ui.status_monitor import StatusMonitor
from utils.performance import PerformanceMonitor
from utils.file_handler import FileHandler
from api.rest_api import app, initialize_api_components
from config import UIConfig, APIConfig, LoggingConfig
from utils import get_logger, setup_system_logging

# Initialize logging
setup_system_logging()
logger = get_logger(__name__)

class VoiceToTextSystem:
    """Main application class for the voice-to-text system"""

    def __init__(self):
        self.logger = get_logger(__name__)

        # System components
        self.model_manager = None
        self.voice_processor = None
        self.console_display = None
        self.status_monitor = None
        self.performance_monitor = None
        self.file_handler = None

        # System state
        self.is_running = False
        self.shutdown_requested = False

        # Statistics
        self.start_time = None
        self.session_count = 0

        self.logger.info("VoiceToTextSystem initialized")

    async def initialize(self) -> bool:
        """Initialize all system components"""
        try:
            self.logger.info("Initializing Real-time Voice-to-Text System...")
            print("🚀 Initializing Real-time Voice-to-Text System")
            print("=" * 60)

            # Initialize performance monitor first
            print("📊 Starting performance monitoring...")
            self.performance_monitor = PerformanceMonitor()
            self.performance_monitor.start()

            # Initialize status monitor
            print("📡 Starting status monitoring...")
            self.status_monitor = StatusMonitor()
            self.status_monitor.start_monitoring()

            # Initialize file handler
            print("📁 Initializing file handler...")
            self.file_handler = FileHandler()

            # Initialize console display
            print("🖥️ Initializing console display...")
            self.console_display = ConsoleDisplay()

            # Initialize model manager
            print("🤖 Loading AI models...")
            self.model_manager = ModelManager()
            model_init_success = await self.model_manager.initialize()

            if not model_init_success:
                print("❌ Failed to initialize AI models")
                return False

            # Initialize voice processor
            print("🎤 Initializing voice processor...")
            self.voice_processor = VoiceProcessor(
                self.model_manager,
                self.console_display,
                self.status_monitor,
                self.performance_monitor
            )

            voice_init_success = await self.voice_processor.initialize()

            if not voice_init_success:
                print("❌ Failed to initialize voice processor")
                return False

            # Set up callbacks
            self._setup_callbacks()

            print("✅ System initialization completed successfully!")
            self.logger.info("System initialization completed")

            return True

        except Exception as e:
            print(f"❌ System initialization failed: {str(e)}")
            self.logger.error(f"System initialization failed: {str(e)}", exc_info=True)
            return False

    def _setup_callbacks(self):
        """Set up inter-component callbacks"""
        # Voice processor callbacks
        self.voice_processor.set_callbacks(
            transcription_callback=self._on_transcription,
            emotion_callback=self._on_emotion_detected,
            status_callback=self._on_status_update
        )

        # Status monitor callbacks
        self.status_monitor.add_status_callback(self._on_system_status_update)

        self.logger.debug("System callbacks configured")

    def _on_transcription(self, result):
        """Handle transcription results"""
        try:
            # Update session statistics
            if result.is_final:
                self.session_count += 1

            # Log transcription (optional)
            if result.is_final and result.confidence > 0.8:
                self.logger.info(f"High-confidence transcription: {result.text[:50]}...")

        except Exception as e:
            self.logger.error(f"Error handling transcription: {str(e)}")

    def _on_emotion_detected(self, emotion_result):
        """Handle emotion detection results"""
        try:
            # Log significant emotion changes
            if hasattr(emotion_result, 'confidence') and emotion_result.confidence > 0.8:
                self.logger.debug(f"Strong emotion detected: {emotion_result.primary_emotion}")

        except Exception as e:
            self.logger.error(f"Error handling emotion: {str(e)}")

    def _on_status_update(self, status_data):
        """Handle status updates"""
        try:
            # Update console display
            if self.console_display:
                self.console_display.update_status(status_data)

        except Exception as e:
            self.logger.error(f"Error handling status update: {str(e)}")

    def _on_system_status_update(self, status):
        """Handle system status updates"""
        try:
            # Check for critical issues
            if hasattr(status, 'errors_count') and status.errors_count > 10:
                self.logger.warning("High error count detected")

        except Exception as e:
            self.logger.error(f"Error handling system status: {str(e)}")

    async def shutdown(self):
        """Gracefully shutdown the system"""
        if self.shutdown_requested:
            return

        self.shutdown_requested = True

        print("\n🛑 Shutting down system...")
        self.logger.info("System shutdown initiated")

        try:
            # Stop voice processing
            if self.voice_processor:
                self.voice_processor.stop()

            # Stop console display
            if self.console_display:
                self.console_display.stop_display()

            # Stop monitoring
            if self.status_monitor:
                self.status_monitor.stop_monitoring()

            if self.performance_monitor:
                self.performance_monitor.stop()

            # Shutdown model manager
            if self.model_manager:
                self.model_manager.shutdown()

            # Calculate uptime
            if self.start_time:
                uptime = time.time() - self.start_time
                print(f"📊 System uptime: {uptime:.1f} seconds")
                print(f"📝 Sessions processed: {self.session_count}")

            print("✅ System shutdown completed")
            self.logger.info("System shutdown completed")

        except Exception as e:
            print(f"❌ Error during shutdown: {str(e)}")
            self.logger.error(f"Error during shutdown: {str(e)}", exc_info=True)

        self.is_running = False

    def get_system_info(self) -> dict:
        """Get system information"""
        info = {
            'is_running': self.is_running,
            'uptime_seconds': time.time() - self.start_time if self.start_time else 0,
            'session_count': self.session_count,
            'components': {
                'model_manager': self.model_manager is not None,
                'voice_processor': self.voice_processor is not None,
                'console_display': self.console_display is not None,
                'status_monitor': self.status_monitor is not None,
                'performance_monitor': self.performance_monitor is not None
            }
        }

        # Add component-specific info
        if self.model_manager:
            info['model_info'] = self.model_manager.get_model_info()

        if self.performance_monitor:
            info['performance'] = self.performance_monitor.get_statistics()

        return info

    async def start_console_mode(self):
        """Start the system in console mode"""
        try:
            print("\n🎤 Starting Real-time Voice-to-Text Console Mode")
            print("=" * 60)
            print("🎯 Ready for voice input!")
            print("📝 Speak into your microphone...")
            print("⏹️  Press Ctrl+C to stop")
            print("=" * 60)

            self.is_running = True
            self.start_time = time.time()

            # Start voice processing
            if self.voice_processor:
                await self.voice_processor.start()

            # Start console display
            if self.console_display:
                await self.console_display.start_display()

            # Keep running until shutdown
            while self.is_running and not self.shutdown_requested:
                await asyncio.sleep(0.1)

        except KeyboardInterrupt:
            print("\n⏹️ Stopping voice processing...")
        except Exception as e:
            print(f"❌ Console mode error: {str(e)}")
            self.logger.error(f"Console mode error: {str(e)}", exc_info=True)
        finally:
            self.is_running = False

    async def start_api_mode(self, host=None, port=None):
        """Start the system in API mode"""
        try:
            from api.rest_api import create_app
            from api.websocket_api import WebSocketManager

            print("\n🌐 Starting Real-time Voice-to-Text API Mode")
            print("=" * 60)

            # Use config defaults if not specified
            api_host = host or "localhost"
            api_port = port or 8000

            print(f"🚀 Starting API server on {api_host}:{api_port}")
            print(f"📡 WebSocket endpoint: ws://{api_host}:{api_port}/ws")
            print(f"🔗 REST API: http://{api_host}:{api_port}/docs")
            print("⏹️  Press Ctrl+C to stop")
            print("=" * 60)

            self.is_running = True
            self.start_time = time.time()

            # Create and configure the API app
            app = create_app(self)

            # Start the server
            import uvicorn
            config = uvicorn.Config(
                app=app,
                host=api_host,
                port=api_port,
                log_level="info"
            )
            server = uvicorn.Server(config)
            await server.serve()

        except KeyboardInterrupt:
            print("\n⏹️ Stopping API server...")
        except Exception as e:
            print(f"❌ API mode error: {str(e)}")
            self.logger.error(f"API mode error: {str(e)}", exc_info=True)
        finally:
            self.is_running = False

# Global system instance
system = None

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    print(f"\n🛑 Received signal {signum}")
    if system:
        asyncio.create_task(system.shutdown())

async def main():
    """Main application entry point"""
    global system

    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Real-time Voice-to-Text System")
    parser.add_argument("--mode", choices=["console", "api"], default="console",
                       help="Run mode: console or api")
    parser.add_argument("--host", default=None, help="API host (default: from config)")
    parser.add_argument("--port", type=int, default=None, help="API port (default: from config)")
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       default="INFO", help="Logging level")

    args = parser.parse_args()

    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # Create and initialize system
        system = VoiceToTextSystem()

        init_success = await system.initialize()
        if not init_success:
            print("❌ System initialization failed")
            return 1

        # Start in requested mode
        if args.mode == "console":
            await system.start_console_mode()
        elif args.mode == "api":
            await system.start_api_mode(args.host, args.port)

        return 0

    except KeyboardInterrupt:
        print("\n⏹️ Interrupted by user")
        return 0
    except Exception as e:
        print(f"\n❌ System error: {str(e)}")
        logger.error(f"System error: {str(e)}", exc_info=True)
        return 1
    finally:
        if system:
            await system.shutdown()

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Fatal error: {str(e)}")
        sys.exit(1)
