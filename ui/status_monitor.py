"""
System Status Monitor
Tracks and displays real-time system status and performance metrics
"""

import time
import threading
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from collections import deque
import psutil
import torch

from utils import get_logger

@dataclass
class SystemStatus:
    """System status data class"""
    timestamp: float = field(default_factory=time.time)
    recording: bool = False
    processing: bool = False
    speech_detected: bool = False
    
    # Performance metrics
    cpu_usage: float = 0.0
    memory_usage_mb: float = 0.0
    gpu_memory_mb: float = 0.0
    gpu_utilization: float = 0.0
    
    # Processing metrics
    current_latency_ms: float = 0.0
    avg_latency_ms: float = 0.0
    transcription_count: int = 0
    
    # Emotion detection
    current_emotion: Optional[str] = None
    emotion_confidence: float = 0.0
    
    # Audio metrics
    audio_level: float = 0.0
    background_noise: float = 0.0
    
    # System health
    errors_count: int = 0
    warnings_count: int = 0
    uptime_seconds: float = 0.0

class StatusMonitor:
    """
    Real-time system status monitoring
    Tracks performance, health, and processing metrics
    """
    
    def __init__(self, update_interval: float = 1.0):
        self.logger = get_logger(__name__)
        self.update_interval = update_interval
        
        # Current status
        self.current_status = SystemStatus()
        self.status_lock = threading.Lock()
        
        # Status history
        self.status_history = deque(maxlen=100)
        
        # Monitoring state
        self.is_monitoring = False
        self.monitor_thread = None
        self.start_time = time.time()
        
        # Callbacks for status updates
        self.status_callbacks = []
        
        # Metrics tracking
        self.latency_history = deque(maxlen=50)
        self.error_count = 0
        self.warning_count = 0
        
        self.logger.info("StatusMonitor initialized")
    
    def start_monitoring(self):
        """Start status monitoring"""
        if self.is_monitoring:
            self.logger.warning("Status monitoring already running")
            return
        
        self.logger.info("Starting status monitoring...")
        self.is_monitoring = True
        self.start_time = time.time()
        
        # Start monitoring thread
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info("Status monitoring started")
    
    def stop_monitoring(self):
        """Stop status monitoring"""
        if not self.is_monitoring:
            return
        
        self.logger.info("Stopping status monitoring...")
        self.is_monitoring = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("Status monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                # Update system metrics
                self._update_system_metrics()
                
                # Update status history
                with self.status_lock:
                    self.current_status.timestamp = time.time()
                    self.current_status.uptime_seconds = time.time() - self.start_time
                    self.status_history.append(SystemStatus(**self.current_status.__dict__))
                
                # Notify callbacks
                self._notify_callbacks()
                
                # Sleep until next update
                time.sleep(self.update_interval)
                
            except Exception as e:
                self.logger.error(f"Error in status monitoring loop: {str(e)}", exc_info=True)
                time.sleep(1)  # Prevent tight error loop
    
    def _update_system_metrics(self):
        """Update system performance metrics"""
        try:
            with self.status_lock:
                # CPU and memory
                self.current_status.cpu_usage = psutil.cpu_percent(interval=0.1)
                memory = psutil.virtual_memory()
                self.current_status.memory_usage_mb = memory.used / (1024 * 1024)
                
                # GPU metrics
                if torch.cuda.is_available():
                    try:
                        self.current_status.gpu_memory_mb = torch.cuda.memory_allocated(0) / (1024 * 1024)
                        
                        # Try to get GPU utilization
                        try:
                            import pynvml
                            pynvml.nvmlInit()
                            handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                            utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)
                            self.current_status.gpu_utilization = utilization.gpu
                        except ImportError:
                            pass  # pynvml not available
                        except Exception:
                            pass  # Other GPU monitoring errors
                            
                    except Exception as e:
                        self.logger.debug(f"Error getting GPU metrics: {str(e)}")
                
                # Calculate average latency
                if self.latency_history:
                    self.current_status.avg_latency_ms = sum(self.latency_history) / len(self.latency_history)
                
                # Update error counts
                self.current_status.errors_count = self.error_count
                self.current_status.warnings_count = self.warning_count
                
        except Exception as e:
            self.logger.error(f"Error updating system metrics: {str(e)}")
    
    def _notify_callbacks(self):
        """Notify all registered callbacks"""
        for callback in self.status_callbacks:
            try:
                callback(self.current_status)
            except Exception as e:
                self.logger.error(f"Error in status callback: {str(e)}")
    
    def update_status(self, key: str, value: Any):
        """Update a specific status value"""
        with self.status_lock:
            if hasattr(self.current_status, key):
                setattr(self.current_status, key, value)
                self.logger.debug(f"Status updated: {key} = {value}")
            else:
                self.logger.warning(f"Unknown status key: {key}")
    
    def update_latency(self, latency_ms: float):
        """Update latency metrics"""
        with self.status_lock:
            self.current_status.current_latency_ms = latency_ms
            self.latency_history.append(latency_ms)
    
    def update_emotion(self, emotion: str, confidence: float):
        """Update current emotion status"""
        with self.status_lock:
            self.current_status.current_emotion = emotion
            self.current_status.emotion_confidence = confidence
    
    def increment_transcription_count(self):
        """Increment transcription counter"""
        with self.status_lock:
            self.current_status.transcription_count += 1
    
    def record_error(self):
        """Record an error occurrence"""
        self.error_count += 1
        self.logger.debug(f"Error count: {self.error_count}")
    
    def record_warning(self):
        """Record a warning occurrence"""
        self.warning_count += 1
        self.logger.debug(f"Warning count: {self.warning_count}")
    
    def get_current_status(self) -> SystemStatus:
        """Get current system status"""
        with self.status_lock:
            return SystemStatus(**self.current_status.__dict__)
    
    def get_status_summary(self) -> Dict[str, Any]:
        """Get status summary for display"""
        status = self.get_current_status()
        
        return {
            'recording': status.recording,
            'processing': status.processing,
            'speech_detected': status.speech_detected,
            'emotion': {
                'emotion': status.current_emotion,
                'confidence': status.emotion_confidence
            } if status.current_emotion else None,
            'latency': status.current_latency_ms,
            'avg_latency': status.avg_latency_ms,
            'cpu_usage': status.cpu_usage,
            'memory_mb': status.memory_usage_mb,
            'gpu_memory': status.gpu_memory_mb,
            'gpu_utilization': status.gpu_utilization,
            'transcriptions': status.transcription_count,
            'uptime': status.uptime_seconds,
            'errors': status.errors_count,
            'warnings': status.warnings_count
        }
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        with self.status_lock:
            recent_history = list(self.status_history)[-10:]  # Last 10 status updates
            
            if not recent_history:
                return {}
            
            # Calculate averages
            avg_cpu = sum(s.cpu_usage for s in recent_history) / len(recent_history)
            avg_memory = sum(s.memory_usage_mb for s in recent_history) / len(recent_history)
            avg_gpu_memory = sum(s.gpu_memory_mb for s in recent_history) / len(recent_history)
            avg_gpu_util = sum(s.gpu_utilization for s in recent_history) / len(recent_history)
            
            return {
                'avg_cpu_usage': avg_cpu,
                'avg_memory_mb': avg_memory,
                'avg_gpu_memory_mb': avg_gpu_memory,
                'avg_gpu_utilization': avg_gpu_util,
                'current_latency_ms': self.current_status.current_latency_ms,
                'avg_latency_ms': self.current_status.avg_latency_ms,
                'total_transcriptions': self.current_status.transcription_count,
                'uptime_hours': self.current_status.uptime_seconds / 3600
            }
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get system health status"""
        status = self.get_current_status()
        
        # Determine health level
        health_score = 100
        health_issues = []
        
        # Check CPU usage
        if status.cpu_usage > 80:
            health_score -= 20
            health_issues.append("High CPU usage")
        
        # Check memory usage
        if status.memory_usage_mb > 8000:  # 8GB
            health_score -= 15
            health_issues.append("High memory usage")
        
        # Check GPU memory
        if status.gpu_memory_mb > 5000:  # 5GB
            health_score -= 15
            health_issues.append("High GPU memory usage")
        
        # Check latency
        if status.current_latency_ms > 500:
            health_score -= 25
            health_issues.append("High latency")
        
        # Check error rate
        error_rate = status.errors_count / max(1, status.transcription_count)
        if error_rate > 0.1:  # 10% error rate
            health_score -= 20
            health_issues.append("High error rate")
        
        # Determine health level
        if health_score >= 90:
            health_level = "Excellent"
        elif health_score >= 75:
            health_level = "Good"
        elif health_score >= 60:
            health_level = "Fair"
        elif health_score >= 40:
            health_level = "Poor"
        else:
            health_level = "Critical"
        
        return {
            'health_score': max(0, health_score),
            'health_level': health_level,
            'issues': health_issues,
            'recommendations': self._get_health_recommendations(health_issues)
        }
    
    def _get_health_recommendations(self, issues: List[str]) -> List[str]:
        """Get recommendations based on health issues"""
        recommendations = []
        
        if "High CPU usage" in issues:
            recommendations.append("Consider reducing processing load or upgrading CPU")
        
        if "High memory usage" in issues:
            recommendations.append("Close unnecessary applications or add more RAM")
        
        if "High GPU memory usage" in issues:
            recommendations.append("Use smaller models or reduce batch size")
        
        if "High latency" in issues:
            recommendations.append("Check system resources and optimize settings")
        
        if "High error rate" in issues:
            recommendations.append("Check audio input quality and system stability")
        
        return recommendations
    
    def add_status_callback(self, callback):
        """Add callback for status updates"""
        self.status_callbacks.append(callback)
        self.logger.debug("Status callback added")
    
    def remove_status_callback(self, callback):
        """Remove status callback"""
        if callback in self.status_callbacks:
            self.status_callbacks.remove(callback)
            self.logger.debug("Status callback removed")
    
    def reset_counters(self):
        """Reset error and warning counters"""
        with self.status_lock:
            self.error_count = 0
            self.warning_count = 0
            self.current_status.errors_count = 0
            self.current_status.warnings_count = 0
            self.current_status.transcription_count = 0
        
        self.logger.info("Status counters reset")
    
    def export_status_history(self, filepath: str):
        """Export status history to file"""
        try:
            import json
            
            with self.status_lock:
                history_data = []
                for status in self.status_history:
                    history_data.append({
                        'timestamp': status.timestamp,
                        'recording': status.recording,
                        'processing': status.processing,
                        'cpu_usage': status.cpu_usage,
                        'memory_usage_mb': status.memory_usage_mb,
                        'gpu_memory_mb': status.gpu_memory_mb,
                        'current_latency_ms': status.current_latency_ms,
                        'transcription_count': status.transcription_count,
                        'current_emotion': status.current_emotion,
                        'emotion_confidence': status.emotion_confidence
                    })
            
            with open(filepath, 'w') as f:
                json.dump(history_data, f, indent=2)
            
            self.logger.info(f"Status history exported to {filepath}")
            
        except Exception as e:
            self.logger.error(f"Error exporting status history: {str(e)}")

if __name__ == "__main__":
    # Test status monitor
    monitor = StatusMonitor(update_interval=0.5)
    monitor.start_monitoring()
    
    # Simulate some activity
    import time
    for i in range(10):
        monitor.update_latency(100 + i * 10)
        monitor.update_emotion("happy", 0.8)
        monitor.increment_transcription_count()
        time.sleep(1)
    
    # Print status
    status = monitor.get_status_summary()
    print(f"Status: {status}")
    
    health = monitor.get_health_status()
    print(f"Health: {health}")
    
    monitor.stop_monitoring()
