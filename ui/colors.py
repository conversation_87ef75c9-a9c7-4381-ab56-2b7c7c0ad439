"""
Color Management System
Provides color schemes and formatting for console output
"""

from typing import Dict, Any, Optional
from enum import Enum
import colorama
from colorama import Fore, Back, Style

from config import UIConfig
from utils import get_logger

# Initialize colorama for Windows compatibility
colorama.init(autoreset=True)

class ColorTheme(Enum):
    """Available color themes"""
    DEFAULT = "default"
    DARK = "dark"
    LIGHT = "light"
    HIGH_CONTRAST = "high_contrast"

class ColorManager:
    """
    Manages color schemes and console formatting
    Provides emotion-based coloring and theme management
    """
    
    def __init__(self, theme: ColorTheme = ColorTheme.DEFAULT):
        self.logger = get_logger(__name__)
        self.current_theme = theme
        
        # Color schemes for different themes
        self.themes = {
            ColorTheme.DEFAULT: {
                'neutral': Fore.WHITE,
                'happy': Fore.GREEN,
                'sad': Fore.BLUE,
                'angry': Fore.RED,
                'excited': Fore.YELLOW,
                'frustrated': Fore.MAGENTA,
                'surprised': For<PERSON>.CYAN,
                'pause': Style.DIM + Fore.WHITE,
                'filler': Fore.RED + Style.DIM,
                'system': Fore.BLUE + Style.BRIGHT,
                'error': Fore.RED + Style.BRIGHT,
                'warning': Fore.YELLOW + Style.BRIGHT,
                'success': Fore.GREEN + Style.BRIGHT,
                'info': Fore.CYAN,
                'timestamp': Style.DIM + Fore.WHITE,
                'confidence_high': Fore.GREEN,
                'confidence_medium': Fore.YELLOW,
                'confidence_low': Fore.RED,
                'recording': Fore.RED + Style.BRIGHT,
                'processing': Fore.YELLOW,
                'realtime': Fore.CYAN + Style.DIM,
                'final': Fore.WHITE + Style.BRIGHT
            },
            
            ColorTheme.DARK: {
                'neutral': Fore.LIGHTWHITE_EX,
                'happy': Fore.LIGHTGREEN_EX,
                'sad': Fore.LIGHTBLUE_EX,
                'angry': Fore.LIGHTRED_EX,
                'excited': Fore.LIGHTYELLOW_EX,
                'frustrated': Fore.LIGHTMAGENTA_EX,
                'surprised': Fore.LIGHTCYAN_EX,
                'pause': Style.DIM + Fore.LIGHTWHITE_EX,
                'filler': Fore.LIGHTRED_EX + Style.DIM,
                'system': Fore.LIGHTBLUE_EX + Style.BRIGHT,
                'error': Fore.LIGHTRED_EX + Style.BRIGHT,
                'warning': Fore.LIGHTYELLOW_EX + Style.BRIGHT,
                'success': Fore.LIGHTGREEN_EX + Style.BRIGHT,
                'info': Fore.LIGHTCYAN_EX,
                'timestamp': Style.DIM + Fore.LIGHTWHITE_EX,
                'confidence_high': Fore.LIGHTGREEN_EX,
                'confidence_medium': Fore.LIGHTYELLOW_EX,
                'confidence_low': Fore.LIGHTRED_EX,
                'recording': Fore.LIGHTRED_EX + Style.BRIGHT,
                'processing': Fore.LIGHTYELLOW_EX,
                'realtime': Fore.LIGHTCYAN_EX + Style.DIM,
                'final': Fore.LIGHTWHITE_EX + Style.BRIGHT
            },
            
            ColorTheme.HIGH_CONTRAST: {
                'neutral': Fore.WHITE + Back.BLACK,
                'happy': Fore.BLACK + Back.GREEN,
                'sad': Fore.WHITE + Back.BLUE,
                'angry': Fore.WHITE + Back.RED,
                'excited': Fore.BLACK + Back.YELLOW,
                'frustrated': Fore.WHITE + Back.MAGENTA,
                'surprised': Fore.BLACK + Back.CYAN,
                'pause': Fore.WHITE + Back.BLACK + Style.DIM,
                'filler': Fore.WHITE + Back.RED,
                'system': Fore.WHITE + Back.BLUE,
                'error': Fore.WHITE + Back.RED + Style.BRIGHT,
                'warning': Fore.BLACK + Back.YELLOW + Style.BRIGHT,
                'success': Fore.BLACK + Back.GREEN + Style.BRIGHT,
                'info': Fore.BLACK + Back.CYAN,
                'timestamp': Fore.WHITE + Back.BLACK + Style.DIM,
                'confidence_high': Fore.BLACK + Back.GREEN,
                'confidence_medium': Fore.BLACK + Back.YELLOW,
                'confidence_low': Fore.WHITE + Back.RED,
                'recording': Fore.WHITE + Back.RED + Style.BRIGHT,
                'processing': Fore.BLACK + Back.YELLOW,
                'realtime': Fore.BLACK + Back.CYAN + Style.DIM,
                'final': Fore.BLACK + Back.WHITE + Style.BRIGHT
            }
        }
        
        # Current color scheme
        self.colors = self.themes[self.current_theme]
        
        # Special formatting
        self.reset = Style.RESET_ALL
        self.bold = Style.BRIGHT
        self.dim = Style.DIM
        
        self.logger.info(f"ColorManager initialized with theme: {theme.value}")
    
    def get_emotion_color(self, emotion: str) -> str:
        """Get color code for an emotion"""
        return self.colors.get(emotion, self.colors['neutral'])
    
    def get_confidence_color(self, confidence: float) -> str:
        """Get color based on confidence level"""
        if confidence >= 0.8:
            return self.colors['confidence_high']
        elif confidence >= 0.6:
            return self.colors['confidence_medium']
        else:
            return self.colors['confidence_low']
    
    def get_system_color(self, message_type: str) -> str:
        """Get color for system messages"""
        return self.colors.get(message_type, self.colors['system'])
    
    def colorize_text(self, text: str, color_type: str) -> str:
        """Apply color to text"""
        color = self.colors.get(color_type, self.colors['neutral'])
        return f"{color}{text}{self.reset}"
    
    def colorize_emotion_text(self, text: str, emotion: str, confidence: float = 1.0) -> str:
        """Colorize text based on emotion and confidence"""
        emotion_color = self.get_emotion_color(emotion)
        
        # Adjust brightness based on confidence
        if confidence < 0.6:
            emotion_color += Style.DIM
        elif confidence > 0.8:
            emotion_color += Style.BRIGHT
        
        return f"{emotion_color}{text}{self.reset}"
    
    def format_transcription(self, text: str, emotion: Optional[str] = None, 
                           confidence: Optional[float] = None, 
                           is_final: bool = False) -> str:
        """Format transcription text with appropriate colors"""
        
        # Choose base color
        if is_final:
            base_color = self.colors['final']
        else:
            base_color = self.colors['realtime']
        
        # Apply emotion color if available
        if emotion and confidence:
            emotion_color = self.get_emotion_color(emotion)
            if confidence < 0.6:
                emotion_color += Style.DIM
            base_color = emotion_color
        
        return f"{base_color}{text}{self.reset}"
    
    def format_metadata(self, metadata_type: str, value: Any) -> str:
        """Format metadata with appropriate colors"""
        
        if metadata_type == 'pause':
            return f"{self.colors['pause']}(pause:{value:.1f}s){self.reset}"
        
        elif metadata_type == 'emotion':
            emotion, confidence = value if isinstance(value, tuple) else (value, 1.0)
            confidence_color = self.get_confidence_color(confidence)
            return f"{confidence_color}({emotion}:{confidence*100:.0f}%){self.reset}"
        
        elif metadata_type == 'filler':
            return f"{self.colors['filler']}({value}:filler){self.reset}"
        
        elif metadata_type == 'timestamp':
            return f"{self.colors['timestamp']}[{value}]{self.reset}"
        
        elif metadata_type == 'confidence':
            confidence_color = self.get_confidence_color(value)
            return f"{confidence_color}{value*100:.0f}%{self.reset}"
        
        else:
            return f"{self.colors['info']}{value}{self.reset}"
    
    def format_system_message(self, message: str, message_type: str = 'info') -> str:
        """Format system messages"""
        color = self.get_system_color(message_type)
        
        # Add prefix based on message type
        prefixes = {
            'info': 'ℹ️ ',
            'warning': '⚠️ ',
            'error': '❌ ',
            'success': '✅ ',
            'recording': '🎤 ',
            'processing': '⚙️ ',
            'system': '🔧 '
        }
        
        prefix = prefixes.get(message_type, '')
        return f"{color}{prefix}{message}{self.reset}"
    
    def format_status_bar(self, status_items: Dict[str, Any]) -> str:
        """Format status bar with multiple items"""
        formatted_items = []
        
        for key, value in status_items.items():
            if key == 'recording':
                color = self.colors['recording'] if value else self.colors['system']
                status = "REC" if value else "IDLE"
                formatted_items.append(f"{color}{status}{self.reset}")
            
            elif key == 'emotion':
                if value and isinstance(value, dict):
                    emotion = value.get('emotion', 'neutral')
                    confidence = value.get('confidence', 0)
                    emotion_color = self.get_emotion_color(emotion)
                    formatted_items.append(f"{emotion_color}{emotion.upper()}{self.reset}")
            
            elif key == 'latency':
                if value is not None:
                    if value < 200:
                        color = self.colors['success']
                    elif value < 500:
                        color = self.colors['warning']
                    else:
                        color = self.colors['error']
                    formatted_items.append(f"{color}{value:.0f}ms{self.reset}")
            
            elif key == 'gpu_memory':
                if value is not None:
                    if value < 3000:  # MB
                        color = self.colors['success']
                    elif value < 4500:
                        color = self.colors['warning']
                    else:
                        color = self.colors['error']
                    formatted_items.append(f"{color}{value:.0f}MB{self.reset}")
            
            else:
                formatted_items.append(f"{self.colors['info']}{key}:{value}{self.reset}")
        
        return " | ".join(formatted_items)
    
    def create_progress_bar(self, progress: float, width: int = 20, 
                          color: str = 'info') -> str:
        """Create a colored progress bar"""
        filled = int(progress * width)
        bar_color = self.colors.get(color, self.colors['info'])
        
        bar = "█" * filled + "░" * (width - filled)
        percentage = f"{progress*100:.0f}%"
        
        return f"{bar_color}[{bar}] {percentage}{self.reset}"
    
    def set_theme(self, theme: ColorTheme):
        """Change color theme"""
        if theme in self.themes:
            self.current_theme = theme
            self.colors = self.themes[theme]
            self.logger.info(f"Color theme changed to: {theme.value}")
        else:
            self.logger.warning(f"Unknown theme: {theme}")
    
    def get_available_themes(self) -> list:
        """Get list of available themes"""
        return [theme.value for theme in ColorTheme]
    
    def disable_colors(self):
        """Disable all colors (for file output)"""
        self.colors = {key: '' for key in self.colors.keys()}
        self.reset = ''
        self.bold = ''
        self.dim = ''
    
    def enable_colors(self):
        """Re-enable colors"""
        self.colors = self.themes[self.current_theme]
        self.reset = Style.RESET_ALL
        self.bold = Style.BRIGHT
        self.dim = Style.DIM
    
    def test_colors(self):
        """Test all colors and formatting"""
        print("🎨 Color Test - Emotions:")
        emotions = ['neutral', 'happy', 'sad', 'angry', 'excited', 'frustrated', 'surprised']
        for emotion in emotions:
            colored_text = self.colorize_emotion_text(f"This is {emotion} text", emotion, 0.8)
            print(f"  {colored_text}")
        
        print("\n🎨 Color Test - System Messages:")
        message_types = ['info', 'warning', 'error', 'success', 'recording', 'processing']
        for msg_type in message_types:
            formatted_msg = self.format_system_message(f"This is a {msg_type} message", msg_type)
            print(f"  {formatted_msg}")
        
        print("\n🎨 Color Test - Metadata:")
        print(f"  {self.format_metadata('pause', 1.5)}")
        print(f"  {self.format_metadata('emotion', ('happy', 0.85))}")
        print(f"  {self.format_metadata('filler', 'um')}")
        print(f"  {self.format_metadata('confidence', 0.92)}")
        
        print("\n🎨 Color Test - Progress Bar:")
        for i in range(0, 101, 25):
            progress = i / 100
            bar = self.create_progress_bar(progress, 20, 'success')
            print(f"  {bar}")

if __name__ == "__main__":
    # Test color manager
    color_manager = ColorManager()
    color_manager.test_colors()
    
    # Test different themes
    print("\n" + "="*50)
    print("Testing Dark Theme:")
    color_manager.set_theme(ColorTheme.DARK)
    color_manager.test_colors()
