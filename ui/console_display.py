"""
Real-time Console Display Interface
Provides color-coded console interface with live text streaming and status monitoring
"""

import os
import sys
import time
import threading
from typing import Dict, Any, Optional, List
from collections import deque
from datetime import datetime
import shutil

from .colors import ColorManager, ColorTheme
from .status_monitor import StatusMonitor
from config import UIConfig
from utils import get_logger

class ConsoleDisplay:
    """
    Real-time console display with color coding and live updates
    Features live text streaming, emotion highlighting, and system status
    """
    
    def __init__(self, color_theme: ColorTheme = ColorTheme.DEFAULT):
        self.logger = get_logger(__name__)
        
        # Display components
        self.color_manager = ColorManager(color_theme)
        
        # Display configuration
        self.max_display_lines = UIConfig.MAX_DISPLAY_LINES
        self.scroll_buffer_size = UIConfig.SCROLL_BUFFER_SIZE
        self.update_interval = UIConfig.UPDATE_INTERVAL
        self.show_timestamps = UIConfig.SHOW_TIMESTAMPS
        self.show_confidence = UIConfig.SHOW_CONFIDENCE
        
        # Text buffer
        self.text_buffer = deque(maxlen=self.scroll_buffer_size)
        self.buffer_lock = threading.Lock()
        
        # Display state
        self.is_displaying = False
        self.display_thread = None
        self.last_update_time = 0
        
        # Console dimensions
        self.console_width = 80
        self.console_height = 24
        self._update_console_size()
        
        # Status display
        self.show_status_bar = True
        self.status_bar_height = 3
        self.text_display_height = self.console_height - self.status_bar_height - 2
        
        # Current status
        self.current_status = {}
        self.recording_status = False
        self.processing_status = False
        
        # Statistics
        self.lines_displayed = 0
        self.updates_count = 0
        
        self.logger.info("ConsoleDisplay initialized")
    
    def _update_console_size(self):
        """Update console dimensions"""
        try:
            size = shutil.get_terminal_size()
            self.console_width = size.columns
            self.console_height = size.lines
        except Exception:
            # Fallback to default size
            self.console_width = 80
            self.console_height = 24
    
    def start_display(self):
        """Start the console display"""
        if self.is_displaying:
            self.logger.warning("Console display already running")
            return
        
        self.logger.info("Starting console display...")
        self.is_displaying = True
        
        # Clear screen and show header
        self._clear_screen()
        self._show_header()
        
        # Start display update thread
        self.display_thread = threading.Thread(target=self._display_loop, daemon=True)
        self.display_thread.start()
        
        self.logger.info("Console display started")
    
    def stop_display(self):
        """Stop the console display"""
        if not self.is_displaying:
            return
        
        self.logger.info("Stopping console display...")
        self.is_displaying = False
        
        if self.display_thread and self.display_thread.is_alive():
            self.display_thread.join(timeout=2)
        
        self.logger.info("Console display stopped")
    
    def _display_loop(self):
        """Main display update loop"""
        while self.is_displaying:
            try:
                current_time = time.time()
                
                # Update display if enough time has passed
                if current_time - self.last_update_time >= self.update_interval:
                    self._update_display()
                    self.last_update_time = current_time
                    self.updates_count += 1
                
                # Small sleep to prevent excessive CPU usage
                time.sleep(0.01)
                
            except Exception as e:
                self.logger.error(f"Error in display loop: {str(e)}", exc_info=True)
                time.sleep(0.1)
    
    def _update_display(self):
        """Update the console display"""
        try:
            # Update console size
            self._update_console_size()
            
            # Move cursor to top
            self._move_cursor(1, 1)
            
            # Display header
            self._show_header()
            
            # Display text content
            self._display_text_content()
            
            # Display status bar
            if self.show_status_bar:
                self._display_status_bar()
            
            # Flush output
            sys.stdout.flush()
            
        except Exception as e:
            self.logger.error(f"Error updating display: {str(e)}")
    
    def _clear_screen(self):
        """Clear the console screen"""
        if os.name == 'nt':  # Windows
            os.system('cls')
        else:  # Unix/Linux/Mac
            os.system('clear')
    
    def _move_cursor(self, row: int, col: int):
        """Move cursor to specific position"""
        print(f"\033[{row};{col}H", end='')
    
    def _show_header(self):
        """Display the application header"""
        header_lines = [
            "🎤 Real-time Voice-to-Text System",
            "=" * self.console_width,
            ""
        ]
        
        for i, line in enumerate(header_lines):
            self._move_cursor(i + 1, 1)
            if i == 0:
                # Colorize main title
                colored_line = self.color_manager.colorize_text(line, 'system')
                print(colored_line.ljust(self.console_width))
            else:
                print(line.ljust(self.console_width))
    
    def _display_text_content(self):
        """Display the main text content"""
        start_row = 4  # After header
        max_rows = self.text_display_height
        
        with self.buffer_lock:
            # Get recent text lines
            recent_lines = list(self.text_buffer)[-max_rows:]
        
        # Clear text area
        for i in range(max_rows):
            self._move_cursor(start_row + i, 1)
            print(" " * self.console_width)
        
        # Display text lines
        for i, line_data in enumerate(recent_lines):
            if i >= max_rows:
                break
            
            self._move_cursor(start_row + i, 1)
            formatted_line = self._format_text_line(line_data)
            
            # Truncate if too long
            if len(formatted_line) > self.console_width:
                formatted_line = formatted_line[:self.console_width - 3] + "..."
            
            print(formatted_line)
    
    def _format_text_line(self, line_data: Dict[str, Any]) -> str:
        """Format a text line for display"""
        try:
            text = line_data.get('text', '')
            emotion = line_data.get('emotion')
            confidence = line_data.get('confidence', 0)
            is_final = line_data.get('is_final', False)
            timestamp = line_data.get('timestamp')
            
            # Format timestamp
            timestamp_str = ""
            if self.show_timestamps and timestamp:
                dt = datetime.fromtimestamp(timestamp)
                timestamp_str = self.color_manager.format_metadata('timestamp', dt.strftime('%H:%M:%S'))
                timestamp_str += " "
            
            # Format main text with emotion coloring
            if emotion and confidence > 0.6:
                formatted_text = self.color_manager.format_transcription(
                    text, emotion, confidence, is_final
                )
            else:
                color_type = 'final' if is_final else 'realtime'
                formatted_text = self.color_manager.colorize_text(text, color_type)
            
            # Add confidence indicator
            confidence_str = ""
            if self.show_confidence and confidence > 0:
                confidence_str = " " + self.color_manager.format_metadata('confidence', confidence)
            
            return f"{timestamp_str}{formatted_text}{confidence_str}"
            
        except Exception as e:
            self.logger.error(f"Error formatting text line: {str(e)}")
            return str(line_data.get('text', ''))
    
    def _display_status_bar(self):
        """Display the status bar at the bottom"""
        status_start_row = self.console_height - self.status_bar_height
        
        # Clear status area
        for i in range(self.status_bar_height):
            self._move_cursor(status_start_row + i, 1)
            print(" " * self.console_width)
        
        # Status line 1: Recording and processing status
        self._move_cursor(status_start_row, 1)
        status_line1 = self._format_status_line1()
        print(status_line1)
        
        # Status line 2: Performance metrics
        self._move_cursor(status_start_row + 1, 1)
        status_line2 = self._format_status_line2()
        print(status_line2)
        
        # Status line 3: Separator
        self._move_cursor(status_start_row + 2, 1)
        separator = "─" * self.console_width
        print(self.color_manager.colorize_text(separator, 'system'))
    
    def _format_status_line1(self) -> str:
        """Format the first status line"""
        items = []
        
        # Recording status
        if self.recording_status:
            items.append(self.color_manager.colorize_text("● REC", 'recording'))
        else:
            items.append(self.color_manager.colorize_text("○ IDLE", 'system'))
        
        # Processing status
        if self.processing_status:
            items.append(self.color_manager.colorize_text("⚙ PROCESSING", 'processing'))
        
        # Current emotion
        current_emotion = self.current_status.get('emotion')
        if current_emotion:
            emotion_text = f"😊 {current_emotion['emotion'].upper()}"
            items.append(self.color_manager.colorize_emotion_text(
                emotion_text, current_emotion['emotion'], current_emotion.get('confidence', 0)
            ))
        
        return " | ".join(items)
    
    def _format_status_line2(self) -> str:
        """Format the second status line"""
        items = []
        
        # Latency
        latency = self.current_status.get('latency')
        if latency is not None:
            if latency < 200:
                color = 'success'
            elif latency < 500:
                color = 'warning'
            else:
                color = 'error'
            items.append(self.color_manager.colorize_text(f"⏱ {latency:.0f}ms", color))
        
        # GPU Memory
        gpu_memory = self.current_status.get('gpu_memory')
        if gpu_memory is not None:
            if gpu_memory < 3000:
                color = 'success'
            elif gpu_memory < 4500:
                color = 'warning'
            else:
                color = 'error'
            items.append(self.color_manager.colorize_text(f"🎮 {gpu_memory:.0f}MB", color))
        
        # CPU Usage
        cpu_usage = self.current_status.get('cpu_usage')
        if cpu_usage is not None:
            if cpu_usage < 50:
                color = 'success'
            elif cpu_usage < 80:
                color = 'warning'
            else:
                color = 'error'
            items.append(self.color_manager.colorize_text(f"💻 {cpu_usage:.0f}%", color))
        
        # Transcription count
        transcriptions = self.current_status.get('transcriptions', 0)
        items.append(self.color_manager.colorize_text(f"📝 {transcriptions}", 'info'))
        
        return " | ".join(items)
    
    def display_realtime_text(self, result):
        """Display real-time transcription text"""
        try:
            # Handle emotion result properly
            emotion_text = None
            if hasattr(result, 'emotion') and result.emotion:
                if hasattr(result.emotion, 'primary_emotion'):
                    emotion_text = result.emotion.primary_emotion
                elif isinstance(result.emotion, dict) and 'primary_emotion' in result.emotion:
                    emotion_text = result.emotion['primary_emotion']

            line_data = {
                'text': result.text,
                'emotion': emotion_text,
                'confidence': result.confidence,
                'is_final': False,
                'timestamp': result.timestamp
            }
            
            with self.buffer_lock:
                self.text_buffer.append(line_data)
                self.lines_displayed += 1
            
        except Exception as e:
            self.logger.error(f"Error displaying real-time text: {str(e)}")
    
    def display_final_text(self, result):
        """Display final transcription text"""
        try:
            # Handle emotion result properly
            emotion_text = None
            if result.emotion:
                if hasattr(result.emotion, 'primary_emotion'):
                    emotion_text = result.emotion.primary_emotion
                elif isinstance(result.emotion, dict) and 'primary_emotion' in result.emotion:
                    emotion_text = result.emotion['primary_emotion']

            line_data = {
                'text': result.text,
                'emotion': emotion_text,
                'confidence': result.confidence,
                'is_final': True,
                'timestamp': result.timestamp
            }
            
            with self.buffer_lock:
                self.text_buffer.append(line_data)
                self.lines_displayed += 1
            
        except Exception as e:
            self.logger.error(f"Error displaying final text: {str(e)}")
    
    def show_system_message(self, message: str, message_type: str = 'info'):
        """Display a system message"""
        try:
            formatted_message = self.color_manager.format_system_message(message, message_type)
            
            line_data = {
                'text': formatted_message,
                'is_final': True,
                'timestamp': time.time()
            }
            
            with self.buffer_lock:
                self.text_buffer.append(line_data)
            
        except Exception as e:
            self.logger.error(f"Error showing system message: {str(e)}")
    
    def show_recording_status(self, is_recording: bool):
        """Update recording status"""
        self.recording_status = is_recording
    
    def show_processing_status(self, is_processing: bool):
        """Update processing status"""
        self.processing_status = is_processing
    
    def update_status(self, status_data: Dict[str, Any]):
        """Update status information"""
        self.current_status.update(status_data)
    
    def clear_display(self):
        """Clear the text display"""
        with self.buffer_lock:
            self.text_buffer.clear()
        self._clear_screen()
        self._show_header()
    
    def set_color_theme(self, theme: ColorTheme):
        """Change color theme"""
        self.color_manager.set_theme(theme)
        self.logger.info(f"Color theme changed to: {theme.value}")
    
    def toggle_timestamps(self):
        """Toggle timestamp display"""
        self.show_timestamps = not self.show_timestamps
        self.logger.info(f"Timestamps {'enabled' if self.show_timestamps else 'disabled'}")
    
    def toggle_confidence(self):
        """Toggle confidence display"""
        self.show_confidence = not self.show_confidence
        self.logger.info(f"Confidence display {'enabled' if self.show_confidence else 'disabled'}")
    
    def get_display_statistics(self) -> Dict[str, Any]:
        """Get display statistics"""
        return {
            'lines_displayed': self.lines_displayed,
            'updates_count': self.updates_count,
            'buffer_size': len(self.text_buffer),
            'console_size': f"{self.console_width}x{self.console_height}",
            'is_displaying': self.is_displaying
        }
    
    def export_text_buffer(self, filepath: str):
        """Export text buffer to file"""
        try:
            with self.buffer_lock:
                lines = []
                for line_data in self.text_buffer:
                    timestamp = datetime.fromtimestamp(line_data.get('timestamp', time.time()))
                    text = line_data.get('text', '')
                    is_final = line_data.get('is_final', False)
                    
                    prefix = "[FINAL]" if is_final else "[REALTIME]"
                    lines.append(f"{timestamp.strftime('%Y-%m-%d %H:%M:%S')} {prefix} {text}")
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))
            
            self.logger.info(f"Text buffer exported to {filepath}")
            
        except Exception as e:
            self.logger.error(f"Error exporting text buffer: {str(e)}")

if __name__ == "__main__":
    # Test console display
    display = ConsoleDisplay()
    display.start_display()
    
    # Simulate some transcription results
    import time
    from dataclasses import dataclass
    
    @dataclass
    class MockResult:
        text: str
        confidence: float
        timestamp: float
        emotion: dict = None
    
    # Show some test messages
    display.show_system_message("System initialized", "success")
    display.show_recording_status(True)
    
    # Simulate real-time transcription
    test_texts = [
        "Hello there",
        "Hello there how",
        "Hello there how are",
        "Hello there how are you",
        "Hello there how are you doing"
    ]
    
    for text in test_texts:
        result = MockResult(text, 0.8, time.time())
        display.display_realtime_text(result)
        time.sleep(1)
    
    # Final result
    final_result = MockResult(
        "Hello there, how are you doing today?", 
        0.95, 
        time.time(),
        {'primary_emotion': 'happy', 'confidence': 0.85}
    )
    display.display_final_text(final_result)
    
    # Keep running for a bit
    time.sleep(10)
    display.stop_display()
