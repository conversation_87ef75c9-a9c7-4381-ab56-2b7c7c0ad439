# Real-time Voice-to-Text System Dependencies
# Optimized for GTX 1660 Ti (6GB VRAM)

# Core Speech-to-Text
RealtimeSTT>=0.1.19
openai-whisper>=20231117

# PyTorch with CUDA support (for GTX 1660 Ti)
torch>=2.0.0
torchaudio>=2.0.0
torchvision>=0.15.0

# Audio Processing
pyaudio>=0.2.11
sounddevice>=0.4.6
librosa>=0.10.1
numpy>=1.24.0
scipy>=1.10.0

# Voice Activity Detection
silero-vad>=4.0.0
webrtcvad>=2.0.10

# Emotion Detection
transformers>=4.30.0
datasets>=2.12.0
scikit-learn>=1.3.0
joblib>=1.3.0

# API Framework
fastapi>=0.100.0
uvicorn[standard]>=0.22.0
websockets>=11.0.0
pydantic>=2.0.0
python-multipart>=0.0.6

# Console Interface & Display
colorama>=0.4.6
rich>=13.4.0
click>=8.1.0
prompt-toolkit>=3.0.38

# Performance & Monitoring
psutil>=5.9.0
GPUtil>=1.4.0
memory-profiler>=0.60.0

# Utilities
python-dotenv>=1.0.0
pyyaml>=6.0
requests>=2.31.0
aiofiles>=23.1.0

# Development & Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0

# Optional: For advanced emotion detection
speechbrain>=0.5.15
