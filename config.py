"""
Real-time Voice-to-Text System Configuration
Optimized for GTX 1660 Ti (6GB VRAM)
"""

import os
import torch
from pathlib import Path
from typing import Dict, List, Optional

# Base paths
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "data"
MODELS_DIR = BASE_DIR / "models"
LOGS_DIR = DATA_DIR / "logs"

class AudioConfig:
    """Audio processing configuration"""
    SAMPLE_RATE = 16000  # Optimal for speech recognition
    CHANNELS = 1  # Mono
    CHUNK_SIZE = 1024  # Buffer size
    FORMAT = "int16"  # 16-bit audio
    
    # Voice Activity Detection
    VAD_SENSITIVITY = 0.7  # Higher = more sensitive
    SILENCE_THRESHOLD = 0.3  # Seconds of silence before stopping
    PRE_RECORDING_BUFFER = 0.4  # Capture speech start
    MIN_RECORDING_LENGTH = 0.8  # Minimum recording duration
    
    # Pause detection thresholds
    SHORT_PAUSE_MIN = 0.1  # seconds
    SHORT_PAUSE_MAX = 0.5
    MEDIUM_PAUSE_MIN = 0.5
    MEDIUM_PAUSE_MAX = 2.0
    LONG_PAUSE_MIN = 2.0

class ModelConfig:
    """AI Model configuration optimized for GTX 1660 Ti"""
    
    # GPU Configuration
    DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
    GPU_DEVICE_INDEX = 0
    COMPUTE_TYPE = "float16"  # Memory optimization
    
    # Whisper Model Settings
    PRIMARY_MODEL = "medium.en"  # Best balance for GTX 1660 Ti
    REALTIME_MODEL = "tiny.en"   # Fast real-time updates
    BATCH_SIZE = 4  # Conservative for 6GB VRAM
    BEAM_SIZE = 5   # Accuracy vs speed balance
    REALTIME_BEAM_SIZE = 3
    
    # Memory Management
    MAX_VRAM_USAGE = 4.5  # GB - leave headroom
    ENABLE_MEMORY_MONITORING = True
    
    # Language Settings
    LANGUAGE = "en"
    TASK = "transcribe"

class EmotionConfig:
    """Emotion detection configuration"""
    EMOTIONS = [
        "neutral", "happy", "sad", "angry", 
        "excited", "frustrated", "surprised"
    ]
    
    # Processing settings
    EMOTION_WINDOW_SIZE = 2.0  # seconds
    EMOTION_UPDATE_RATE = 0.5  # seconds
    MIN_CONFIDENCE_THRESHOLD = 0.6
    
    # Model paths
    EMOTION_MODEL_PATH = MODELS_DIR / "emotion_models" / "emotion_classifier.pkl"
    EMOTION_CONFIG_PATH = MODELS_DIR / "emotion_models" / "emotion_config.json"

class TextProcessingConfig:
    """Text processing and formatting configuration"""
    
    # Filler words to detect
    FILLER_WORDS = [
        "um", "uh", "ah", "hmm", "er", "like", 
        "you know", "sort of", "kind of", "basically"
    ]
    
    # Formatting options
    ENSURE_SENTENCE_CASE = True
    ENSURE_PERIOD_ENDING = True
    ADD_PAUSE_INDICATORS = True
    ADD_EMOTION_TAGS = True
    ADD_FILLER_TAGS = True
    
    # Display format templates
    PAUSE_FORMAT = "(pause:{duration:.1f}s)"
    EMOTION_FORMAT = "({emotion}:{confidence:.0f}%)"
    FILLER_FORMAT = "({word}:filler)"

class APIConfig:
    """API server configuration"""
    HOST = "127.0.0.1"
    PORT = 8000
    RELOAD = False  # Set to True for development
    
    # WebSocket settings
    WS_HEARTBEAT_INTERVAL = 30  # seconds
    MAX_CONNECTIONS = 10
    
    # CORS settings
    ALLOW_ORIGINS = ["*"]
    ALLOW_METHODS = ["*"]
    ALLOW_HEADERS = ["*"]

class UIConfig:
    """User interface configuration"""
    
    # Console colors (using colorama)
    COLORS = {
        "neutral": "white",
        "happy": "green",
        "sad": "blue", 
        "angry": "red",
        "excited": "yellow",
        "frustrated": "magenta",
        "surprised": "cyan",
        "pause": "dim",
        "filler": "red",
        "system": "bright_blue"
    }
    
    # Display settings
    MAX_DISPLAY_LINES = 50
    SCROLL_BUFFER_SIZE = 500
    UPDATE_INTERVAL = 0.1  # seconds
    SHOW_TIMESTAMPS = True
    SHOW_CONFIDENCE = True

class PerformanceConfig:
    """Performance monitoring and optimization"""
    
    # Latency targets (milliseconds)
    TARGET_LATENCY = 200
    MAX_ACCEPTABLE_LATENCY = 500
    
    # Memory monitoring
    MEMORY_CHECK_INTERVAL = 5.0  # seconds
    MAX_CPU_USAGE = 80  # percent
    
    # Performance logging
    LOG_PERFORMANCE_METRICS = True
    PERFORMANCE_LOG_INTERVAL = 30  # seconds

class LoggingConfig:
    """Logging configuration"""
    
    # Log levels
    CONSOLE_LOG_LEVEL = "INFO"
    FILE_LOG_LEVEL = "DEBUG"
    
    # Log files
    MAIN_LOG_FILE = LOGS_DIR / "realstt.log"
    PERFORMANCE_LOG_FILE = LOGS_DIR / "performance.log"
    ERROR_LOG_FILE = LOGS_DIR / "errors.log"
    
    # Log rotation
    MAX_LOG_SIZE = 10 * 1024 * 1024  # 10MB
    BACKUP_COUNT = 5

# Environment-specific overrides
def load_env_config():
    """Load configuration from environment variables"""
    from dotenv import load_dotenv
    load_dotenv()
    
    # Override with environment variables if present
    if os.getenv("REALSTT_MODEL"):
        ModelConfig.PRIMARY_MODEL = os.getenv("REALSTT_MODEL")
    
    if os.getenv("REALSTT_DEVICE"):
        ModelConfig.DEVICE = os.getenv("REALSTT_DEVICE")
    
    if os.getenv("REALSTT_API_PORT"):
        APIConfig.PORT = int(os.getenv("REALSTT_API_PORT"))
    
    if os.getenv("REALSTT_LOG_LEVEL"):
        LoggingConfig.CONSOLE_LOG_LEVEL = os.getenv("REALSTT_LOG_LEVEL")

# GPU Memory Check
def check_gpu_compatibility():
    """Check if GPU is compatible and has enough memory"""
    if not torch.cuda.is_available():
        print("⚠️  CUDA not available. Falling back to CPU mode.")
        ModelConfig.DEVICE = "cpu"
        return False
    
    gpu_props = torch.cuda.get_device_properties(0)
    gpu_memory_gb = gpu_props.total_memory / (1024**3)
    
    print(f"🎮 GPU: {gpu_props.name}")
    print(f"💾 GPU Memory: {gpu_memory_gb:.1f}GB")
    
    if gpu_memory_gb < 4:
        print("⚠️  GPU has less than 4GB VRAM. Consider using smaller models.")
        ModelConfig.PRIMARY_MODEL = "small.en"
        ModelConfig.BATCH_SIZE = 2
    
    return True

# Initialize configuration
def initialize_config():
    """Initialize configuration and create necessary directories"""
    # Create directories
    DATA_DIR.mkdir(exist_ok=True)
    LOGS_DIR.mkdir(exist_ok=True)
    (DATA_DIR / "transcriptions").mkdir(exist_ok=True)
    (DATA_DIR / "audio_samples").mkdir(exist_ok=True)
    MODELS_DIR.mkdir(exist_ok=True)
    (MODELS_DIR / "emotion_models").mkdir(exist_ok=True)
    
    # Load environment config
    load_env_config()
    
    # Check GPU compatibility
    check_gpu_compatibility()
    
    print("✅ Configuration initialized successfully")

if __name__ == "__main__":
    initialize_config()
