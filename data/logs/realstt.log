2025-09-08 16:12:01 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 16:12:01 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:12:01 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:12:01 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 16:12:01 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 16:12:01 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:12:01 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:12:01 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:12:01 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:12:01 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 16:12:01 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 16:12:01 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:12:01 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:12:01 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:12:01 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:12:01 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 16:12:01 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 16:12:01 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:12:01 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:12:01 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:12:01 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:12:01 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 16:12:01 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 16:12:01 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:12:01 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:12:01 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:12:01 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:12:01 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 16:12:01 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 16:12:01 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:12:01 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:12:01 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:12:01 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:12:01 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 16:13:11 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 16:13:11 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:13:11 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:13:11 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 16:13:11 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 16:13:11 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:13:11 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:13:11 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:13:11 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:13:11 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 16:13:11 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 16:13:11 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:13:11 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:13:11 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:13:11 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:13:11 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 16:13:11 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 16:13:11 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:13:11 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:13:11 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:13:11 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:13:11 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 16:13:11 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 16:13:11 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:13:11 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:13:11 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:13:11 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:13:11 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 16:13:11 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 16:13:11 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:13:11 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:13:11 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:13:11 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:13:11 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 16:13:11 | [32mINFO[0m | __main__ | setup_logger:139 | Logger '__main__' initialized
2025-09-08 16:13:11 | [32mINFO[0m | __main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:13:11 | [32mINFO[0m | __main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:13:11 | [32mINFO[0m | __main__ | __init__:68 | ModelDownloader initialized
2025-09-08 16:20:24 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 16:20:24 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:20:24 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:20:24 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 16:20:24 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 16:20:24 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:20:24 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:20:24 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:20:24 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:20:24 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 16:20:24 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 16:20:24 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:20:24 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:20:24 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:20:24 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:20:24 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 16:20:24 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 16:20:24 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:20:24 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:20:24 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:20:24 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:20:24 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 16:20:24 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 16:20:24 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:20:24 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:20:24 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:20:24 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:20:24 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 16:20:24 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 16:20:24 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:20:24 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:20:24 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:20:24 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:20:24 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 16:20:25 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 16:20:25 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:20:25 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:20:25 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 16:20:25 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 16:20:25 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 16:20:25 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:20:25 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:25:43 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 16:25:43 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:25:43 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:25:43 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 16:25:43 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 16:25:43 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:25:43 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:25:43 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:25:43 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:25:43 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 16:25:43 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 16:25:43 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:25:43 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:25:43 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:25:43 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:25:43 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 16:25:43 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 16:25:43 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:25:43 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:25:43 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:25:43 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:25:43 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 16:25:43 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 16:25:43 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:25:43 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:25:43 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:25:43 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:25:43 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 16:25:43 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 16:25:43 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:25:43 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:25:43 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:25:43 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:25:43 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 16:25:44 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 16:25:44 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:25:44 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:25:44 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 16:25:44 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 16:25:44 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 16:25:44 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:25:44 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:25:44 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 16:25:44 | [32mINFO[0m | __main__ | setup_logger:139 | Logger '__main__' initialized
2025-09-08 16:25:44 | [32mINFO[0m | __main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:25:44 | [32mINFO[0m | __main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:25:44 | [32mINFO[0m | __main__ | __init__:53 | VoiceToTextSystem initialized
2025-09-08 16:25:44 | [32mINFO[0m | __main__ | initialize:58 | Initializing Real-time Voice-to-Text System...
2025-09-08 16:25:44 | [32mINFO[0m | utils.performance | setup_logger:139 | Logger 'utils.performance' initialized
2025-09-08 16:25:44 | [32mINFO[0m | utils.performance | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:25:44 | [32mINFO[0m | utils.performance | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:25:44 | [32mINFO[0m | utils.performance | __init__:90 | PerformanceMonitor initialized
2025-09-08 16:25:44 | [32mINFO[0m | utils.performance | start:98 | Starting performance monitoring...
2025-09-08 16:25:44 | [32mINFO[0m | utils.performance | start:106 | Performance monitoring started
2025-09-08 16:25:44 | [32mINFO[0m | ui.status_monitor | setup_logger:139 | Logger 'ui.status_monitor' initialized
2025-09-08 16:25:44 | [32mINFO[0m | ui.status_monitor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:25:44 | [32mINFO[0m | ui.status_monitor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:25:44 | [32mINFO[0m | ui.status_monitor | __init__:78 | StatusMonitor initialized
2025-09-08 16:25:44 | [32mINFO[0m | ui.status_monitor | start_monitoring:86 | Starting status monitoring...
2025-09-08 16:25:44 | [32mINFO[0m | ui.status_monitor | start_monitoring:94 | Status monitoring started
2025-09-08 16:25:44 | [32mINFO[0m | utils.file_handler | setup_logger:139 | Logger 'utils.file_handler' initialized
2025-09-08 16:25:44 | [32mINFO[0m | utils.file_handler | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:25:44 | [32mINFO[0m | utils.file_handler | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:25:44 | DEBUG    | utils.file_handler | _ensure_directories:58 | Data directories ensured
2025-09-08 16:25:44 | [32mINFO[0m | utils.file_handler | __init__:44 | FileHandler initialized
2025-09-08 16:25:44 | [32mINFO[0m | ui.console_display | setup_logger:139 | Logger 'ui.console_display' initialized
2025-09-08 16:25:44 | [32mINFO[0m | ui.console_display | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:25:44 | [32mINFO[0m | ui.console_display | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:25:44 | [32mINFO[0m | ui.colors | setup_logger:139 | Logger 'ui.colors' initialized
2025-09-08 16:25:44 | [32mINFO[0m | ui.colors | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:25:44 | [32mINFO[0m | ui.colors | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:25:44 | [32mINFO[0m | ui.colors | __init__:120 | ColorManager initialized with theme: default
2025-09-08 16:25:44 | [32mINFO[0m | ui.console_display | __init__:67 | ConsoleDisplay initialized
2025-09-08 16:25:44 | [32mINFO[0m | models.model_manager | setup_logger:139 | Logger 'models.model_manager' initialized
2025-09-08 16:25:44 | [32mINFO[0m | models.model_manager | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:25:44 | [32mINFO[0m | models.model_manager | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:25:44 | [32mINFO[0m | models.model_manager | __init__:46 | ModelManager initialized
2025-09-08 16:25:44 | [32mINFO[0m | models.model_manager | initialize:51 | Starting model initialization...
2025-09-08 16:25:44 | [32mINFO[0m | models.model_manager | _check_gpu_status:76 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 16:25:44 | [32mINFO[0m | models.model_manager | _check_gpu_status:77 | Total VRAM: 6.0GB
2025-09-08 16:25:44 | [32mINFO[0m | models.model_manager | _check_gpu_status:78 | CUDA Version: 11.8
2025-09-08 16:25:44 | [32mINFO[0m | models.model_manager | _check_gpu_status:84 | Current VRAM usage: 0.00GB allocated, 0.00GB reserved
2025-09-08 16:25:44 | [32mINFO[0m | models.model_manager | _load_whisper_models:94 | Loading Whisper models...
2025-09-08 16:25:44 | [32mINFO[0m | models.model_manager | _load_whisper_models:97 | Loading primary Whisper model: medium.en
2025-09-08 16:25:45 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12636MB
2025-09-08 16:25:50 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12518MB
2025-09-08 16:25:55 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12521MB
2025-09-08 16:26:00 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12564MB
2025-09-08 16:26:05 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12613MB
2025-09-08 16:26:10 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12579MB
2025-09-08 16:26:15 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12543MB
2025-09-08 16:26:20 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12507MB
2025-09-08 16:26:26 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12541MB
2025-09-08 16:26:31 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12562MB
2025-09-08 16:26:36 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12596MB
2025-09-08 16:26:41 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12555MB
2025-09-08 16:26:46 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12544MB
2025-09-08 16:26:51 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12551MB
2025-09-08 16:26:56 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12534MB
2025-09-08 16:27:01 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12527MB
2025-09-08 16:27:07 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12545MB
2025-09-08 16:27:12 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12550MB
2025-09-08 16:27:17 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12538MB
2025-09-08 16:27:22 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12525MB
2025-09-08 16:27:27 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12522MB
2025-09-08 16:27:32 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12502MB
2025-09-08 16:27:37 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12535MB
2025-09-08 16:27:42 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12545MB
2025-09-08 16:27:48 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12562MB
2025-09-08 16:27:53 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12548MB
2025-09-08 16:27:58 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12541MB
2025-09-08 16:28:03 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12544MB
2025-09-08 16:28:08 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12553MB
2025-09-08 16:28:13 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12554MB
2025-09-08 16:28:18 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12552MB
2025-09-08 16:28:24 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12539MB
2025-09-08 16:28:29 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12537MB
2025-09-08 16:28:34 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12558MB
2025-09-08 16:28:39 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12427MB
2025-09-08 16:28:44 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12399MB
2025-09-08 16:28:49 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12764MB
2025-09-08 16:28:54 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11350MB
2025-09-08 16:28:59 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11365MB
2025-09-08 16:29:05 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11379MB
2025-09-08 16:29:10 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11372MB
2025-09-08 16:29:15 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11388MB
2025-09-08 16:29:20 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11373MB
2025-09-08 16:29:25 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11363MB
2025-09-08 16:29:30 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11442MB
2025-09-08 16:29:35 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14394MB
2025-09-08 16:29:40 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15620MB
2025-09-08 16:29:41 | [32mINFO[0m | models.model_manager | _load_whisper_models:112 | Primary Whisper model loaded in 236.86s
2025-09-08 16:29:41 | [32mINFO[0m | models.model_manager | _load_whisper_models:120 | Primary model VRAM usage: 1.42GB
2025-09-08 16:29:41 | [32mINFO[0m | models.model_manager | _load_whisper_models:123 | Loading realtime Whisper model: tiny.en
2025-09-08 16:29:42 | [32mINFO[0m | models.model_manager | _load_whisper_models:134 | Realtime Whisper model loaded in 1.02s
2025-09-08 16:29:42 | [32mINFO[0m | models.model_manager | _load_whisper_models:142 | Realtime model VRAM usage: 0.07GB
2025-09-08 16:29:42 | [32mINFO[0m | models.model_manager | _load_whisper_models:143 | Total VRAM usage: 1.50GB
2025-09-08 16:29:42 | [32mINFO[0m | models.model_manager | _load_emotion_model:152 | Loading emotion detection model...
2025-09-08 16:29:42 | [32mINFO[0m | models.model_manager | _load_emotion_model:164 | Loading transformer-based emotion classifier...
2025-09-08 16:29:45 | [32mINFO[0m | models.model_manager | _load_emotion_model:178 | Emotion model loaded in 2.47s
2025-09-08 16:29:45 | [32mINFO[0m | models.model_manager | _load_emotion_model:186 | Emotion model VRAM usage: 0.15GB
2025-09-08 16:29:45 | [32mINFO[0m | models.model_manager | _load_emotion_model:187 | Total VRAM usage: 1.65GB
2025-09-08 16:29:45 | [32mINFO[0m | models.model_manager | _verify_models:207 | Optional model 'emotion_detector': ✅ Loaded
2025-09-08 16:29:45 | [32mINFO[0m | models.model_manager | _verify_models:211 | Final VRAM usage: 1.65GB / 4.5GB limit
2025-09-08 16:29:45 | [32mINFO[0m | models.model_manager | initialize:63 | All models initialized successfully
2025-09-08 16:29:45 | [32mINFO[0m | core.voice_processor | setup_logger:139 | Logger 'core.voice_processor' initialized
2025-09-08 16:29:45 | [32mINFO[0m | core.voice_processor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:45 | [32mINFO[0m | core.voice_processor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:45 | [32mINFO[0m | core.audio_utils | setup_logger:139 | Logger 'core.audio_utils' initialized
2025-09-08 16:29:45 | [32mINFO[0m | core.audio_utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:45 | [32mINFO[0m | core.audio_utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:45 | [32mINFO[0m | core.audio_utils | __init__:28 | AudioUtils initialized
2025-09-08 16:29:45 | [32mINFO[0m | core.emotion_detector | setup_logger:139 | Logger 'core.emotion_detector' initialized
2025-09-08 16:29:45 | [32mINFO[0m | core.emotion_detector | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:45 | [32mINFO[0m | core.emotion_detector | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:45 | [32mINFO[0m | core.emotion_detector | __init__:75 | EmotionDetector initialized
2025-09-08 16:29:45 | [32mINFO[0m | core.text_formatter | setup_logger:139 | Logger 'core.text_formatter' initialized
2025-09-08 16:29:45 | [32mINFO[0m | core.text_formatter | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:45 | [32mINFO[0m | core.text_formatter | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:45 | [32mINFO[0m | core.text_formatter | __init__:62 | TextFormatter initialized
2025-09-08 16:29:45 | [32mINFO[0m | core.voice_processor | __init__:77 | VoiceProcessor initialized
2025-09-08 16:29:45 | [32mINFO[0m | core.voice_processor | initialize:82 | Initializing voice processor...
2025-09-08 16:29:45 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:109 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 16:29:45 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:110 | Total VRAM: 6.0GB
2025-09-08 16:29:45 | [33mWARNING[0m | core.voice_processor | _check_gpu_resources:114 | Less than 6GB VRAM available. Using conservative settings.
2025-09-08 16:29:45 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:120 | Initial GPU memory usage: 1.65GB
2025-09-08 16:29:45 | [32mINFO[0m | core.voice_processor | _initialize_recorder:128 | Configuring RealtimeSTT recorder...
2025-09-08 16:29:46 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12901MB
2025-09-08 16:29:51 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 13232MB
2025-09-08 16:29:54 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 16:29:54 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:54 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:54 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 16:29:54 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 16:29:55 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 16:29:55 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:55 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 16:29:55 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:55 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:55 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 16:29:55 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:55 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 16:29:55 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 16:29:55 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 16:29:55 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 16:29:55 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 16:29:55 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:55 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:55 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:55 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:55 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 16:29:55 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 16:29:55 | [32mINFO[0m | __mp_main__ | setup_logger:139 | Logger '__mp_main__' initialized
2025-09-08 16:29:55 | [32mINFO[0m | __mp_main__ | setup_logger:139 | Logger '__mp_main__' initialized
2025-09-08 16:29:55 | [32mINFO[0m | __mp_main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:55 | [32mINFO[0m | __mp_main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:29:55 | [32mINFO[0m | __mp_main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:55 | [32mINFO[0m | __mp_main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:29:56 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 13585MB
2025-09-08 16:29:58 | [31mERROR[0m | core.voice_processor | _initialize_recorder:187 | Failed to initialize recorder: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 746, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 647, in __init__
    self.model = ctranslate2.models.Whisper(
RuntimeError: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
2025-09-08 16:29:58 | [31mERROR[0m | core.voice_processor | initialize:100 | Failed to initialize voice processor: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 88, in initialize
    await self._initialize_recorder()
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 746, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 647, in __init__
    self.model = ctranslate2.models.Whisper(
RuntimeError: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
2025-09-08 16:29:58 | [32mINFO[0m | __main__ | shutdown:183 | System shutdown initiated
2025-09-08 16:29:58 | [32mINFO[0m | ui.status_monitor | stop_monitoring:101 | Stopping status monitoring...
2025-09-08 16:29:58 | [32mINFO[0m | ui.status_monitor | stop_monitoring:107 | Status monitoring stopped
2025-09-08 16:29:58 | [32mINFO[0m | utils.performance | stop:113 | Stopping performance monitoring...
2025-09-08 16:30:01 | [32mINFO[0m | utils.performance | stop:119 | Performance monitoring stopped
2025-09-08 16:30:01 | [32mINFO[0m | models.model_manager | shutdown:295 | Shutting down ModelManager...
2025-09-08 16:30:01 | [32mINFO[0m | models.model_manager | optimize_memory:262 | GPU memory optimized
2025-09-08 16:30:01 | [32mINFO[0m | models.model_manager | optimize_memory:266 | Memory after optimization: 0.00GB
2025-09-08 16:30:01 | [32mINFO[0m | models.model_manager | shutdown:311 | ModelManager shutdown complete
2025-09-08 16:30:01 | [32mINFO[0m | __main__ | shutdown:212 | System shutdown completed
2025-09-08 16:46:48 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 16:46:48 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:46:48 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:46:48 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 16:46:48 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 16:46:48 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:46:48 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:46:48 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:46:48 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:46:48 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 16:46:48 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 16:46:48 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:46:48 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:46:48 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:46:48 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:46:48 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 16:46:48 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 16:46:48 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:46:48 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:46:48 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:46:48 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:46:48 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 16:46:48 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 16:46:48 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:46:48 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:46:48 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:46:48 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:46:48 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 16:46:48 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 16:46:48 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:46:48 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:46:48 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:46:48 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:46:48 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 16:46:49 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 16:46:49 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:46:49 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:46:49 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 16:46:49 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 16:46:49 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 16:46:49 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:46:49 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:46:49 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 16:46:49 | [32mINFO[0m | __main__ | setup_logger:139 | Logger '__main__' initialized
2025-09-08 16:46:49 | [32mINFO[0m | __main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:46:49 | [32mINFO[0m | __main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:46:49 | [32mINFO[0m | __main__ | __init__:53 | VoiceToTextSystem initialized
2025-09-08 16:46:49 | [32mINFO[0m | __main__ | initialize:58 | Initializing Real-time Voice-to-Text System...
2025-09-08 16:46:49 | [32mINFO[0m | utils.performance | setup_logger:139 | Logger 'utils.performance' initialized
2025-09-08 16:46:49 | [32mINFO[0m | utils.performance | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:46:49 | [32mINFO[0m | utils.performance | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:46:49 | [32mINFO[0m | utils.performance | __init__:90 | PerformanceMonitor initialized
2025-09-08 16:46:49 | [32mINFO[0m | utils.performance | start:98 | Starting performance monitoring...
2025-09-08 16:46:49 | [32mINFO[0m | utils.performance | start:106 | Performance monitoring started
2025-09-08 16:46:49 | [32mINFO[0m | ui.status_monitor | setup_logger:139 | Logger 'ui.status_monitor' initialized
2025-09-08 16:46:49 | [32mINFO[0m | ui.status_monitor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:46:49 | [32mINFO[0m | ui.status_monitor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:46:49 | [32mINFO[0m | ui.status_monitor | __init__:78 | StatusMonitor initialized
2025-09-08 16:46:49 | [32mINFO[0m | ui.status_monitor | start_monitoring:86 | Starting status monitoring...
2025-09-08 16:46:49 | [32mINFO[0m | ui.status_monitor | start_monitoring:94 | Status monitoring started
2025-09-08 16:46:49 | [32mINFO[0m | utils.file_handler | setup_logger:139 | Logger 'utils.file_handler' initialized
2025-09-08 16:46:49 | [32mINFO[0m | utils.file_handler | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:46:49 | [32mINFO[0m | utils.file_handler | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:46:49 | DEBUG    | utils.file_handler | _ensure_directories:58 | Data directories ensured
2025-09-08 16:46:49 | [32mINFO[0m | utils.file_handler | __init__:44 | FileHandler initialized
2025-09-08 16:46:49 | [32mINFO[0m | ui.console_display | setup_logger:139 | Logger 'ui.console_display' initialized
2025-09-08 16:46:49 | [32mINFO[0m | ui.console_display | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:46:49 | [32mINFO[0m | ui.console_display | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:46:49 | [32mINFO[0m | ui.colors | setup_logger:139 | Logger 'ui.colors' initialized
2025-09-08 16:46:49 | [32mINFO[0m | ui.colors | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:46:49 | [32mINFO[0m | ui.colors | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:46:49 | [32mINFO[0m | ui.colors | __init__:120 | ColorManager initialized with theme: default
2025-09-08 16:46:49 | [32mINFO[0m | ui.console_display | __init__:67 | ConsoleDisplay initialized
2025-09-08 16:46:49 | [32mINFO[0m | models.model_manager | setup_logger:139 | Logger 'models.model_manager' initialized
2025-09-08 16:46:49 | [32mINFO[0m | models.model_manager | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:46:49 | [32mINFO[0m | models.model_manager | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:46:49 | [32mINFO[0m | models.model_manager | __init__:46 | ModelManager initialized
2025-09-08 16:46:49 | [32mINFO[0m | models.model_manager | initialize:51 | Starting model initialization...
2025-09-08 16:46:49 | [32mINFO[0m | models.model_manager | _check_gpu_status:76 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 16:46:49 | [32mINFO[0m | models.model_manager | _check_gpu_status:77 | Total VRAM: 6.0GB
2025-09-08 16:46:49 | [32mINFO[0m | models.model_manager | _check_gpu_status:78 | CUDA Version: 11.6
2025-09-08 16:46:49 | [32mINFO[0m | models.model_manager | _check_gpu_status:84 | Current VRAM usage: 0.00GB allocated, 0.00GB reserved
2025-09-08 16:46:49 | [32mINFO[0m | models.model_manager | _load_whisper_models:94 | Loading Whisper models...
2025-09-08 16:46:49 | [32mINFO[0m | models.model_manager | _load_whisper_models:97 | Loading primary Whisper model: medium.en
2025-09-08 16:46:49 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 13672MB
2025-09-08 16:46:54 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12907MB
2025-09-08 16:46:59 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15418MB
2025-09-08 16:47:04 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15882MB
2025-09-08 16:47:07 | [32mINFO[0m | models.model_manager | _load_whisper_models:112 | Primary Whisper model loaded in 18.27s
2025-09-08 16:47:07 | [32mINFO[0m | models.model_manager | _load_whisper_models:120 | Primary model VRAM usage: 1.42GB
2025-09-08 16:47:07 | [32mINFO[0m | models.model_manager | _load_whisper_models:123 | Loading realtime Whisper model: tiny.en
2025-09-08 16:47:08 | [32mINFO[0m | models.model_manager | _load_whisper_models:134 | Realtime Whisper model loaded in 0.75s
2025-09-08 16:47:08 | [32mINFO[0m | models.model_manager | _load_whisper_models:142 | Realtime model VRAM usage: 0.07GB
2025-09-08 16:47:08 | [32mINFO[0m | models.model_manager | _load_whisper_models:143 | Total VRAM usage: 1.50GB
2025-09-08 16:47:08 | [32mINFO[0m | models.model_manager | _load_emotion_model:152 | Loading emotion detection model...
2025-09-08 16:47:08 | [32mINFO[0m | models.model_manager | _load_emotion_model:164 | Loading transformer-based emotion classifier...
2025-09-08 16:47:09 | [31mERROR[0m | models.model_manager | _load_emotion_model:190 | Failed to load emotion model: At least one of TensorFlow 2.0 or PyTorch should be installed. To install TensorFlow 2.0, read the instructions at https://www.tensorflow.org/install/ To install PyTorch, read the instructions at https://pytorch.org/.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\models\model_manager.py", line 168, in _load_emotion_model
    emotion_classifier = pipeline(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\transformers\pipelines\__init__.py", line 1028, in pipeline
    framework, model = infer_framework_load_model(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\transformers\pipelines\base.py", line 244, in infer_framework_load_model
    raise RuntimeError(
RuntimeError: At least one of TensorFlow 2.0 or PyTorch should be installed. To install TensorFlow 2.0, read the instructions at https://www.tensorflow.org/install/ To install PyTorch, read the instructions at https://pytorch.org/.
2025-09-08 16:47:09 | [33mWARNING[0m | models.model_manager | _load_emotion_model:192 | Continuing without emotion detection
2025-09-08 16:47:09 | [32mINFO[0m | models.model_manager | _verify_models:207 | Optional model 'emotion_detector': ❌ Not loaded
2025-09-08 16:47:09 | [32mINFO[0m | models.model_manager | _verify_models:211 | Final VRAM usage: 1.50GB / 4.5GB limit
2025-09-08 16:47:09 | [32mINFO[0m | models.model_manager | initialize:63 | All models initialized successfully
2025-09-08 16:47:09 | [32mINFO[0m | core.voice_processor | setup_logger:139 | Logger 'core.voice_processor' initialized
2025-09-08 16:47:09 | [32mINFO[0m | core.voice_processor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:09 | [32mINFO[0m | core.voice_processor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:09 | [32mINFO[0m | core.audio_utils | setup_logger:139 | Logger 'core.audio_utils' initialized
2025-09-08 16:47:09 | [32mINFO[0m | core.audio_utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:09 | [32mINFO[0m | core.audio_utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:09 | [32mINFO[0m | core.audio_utils | __init__:28 | AudioUtils initialized
2025-09-08 16:47:09 | [32mINFO[0m | core.emotion_detector | setup_logger:139 | Logger 'core.emotion_detector' initialized
2025-09-08 16:47:09 | [32mINFO[0m | core.emotion_detector | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:09 | [32mINFO[0m | core.emotion_detector | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:09 | [32mINFO[0m | core.emotion_detector | __init__:75 | EmotionDetector initialized
2025-09-08 16:47:09 | [32mINFO[0m | core.text_formatter | setup_logger:139 | Logger 'core.text_formatter' initialized
2025-09-08 16:47:09 | [32mINFO[0m | core.text_formatter | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:09 | [32mINFO[0m | core.text_formatter | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:09 | [32mINFO[0m | core.text_formatter | __init__:62 | TextFormatter initialized
2025-09-08 16:47:09 | [32mINFO[0m | core.voice_processor | __init__:77 | VoiceProcessor initialized
2025-09-08 16:47:09 | [32mINFO[0m | core.voice_processor | initialize:82 | Initializing voice processor...
2025-09-08 16:47:09 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:109 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 16:47:09 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:110 | Total VRAM: 6.0GB
2025-09-08 16:47:09 | [33mWARNING[0m | core.voice_processor | _check_gpu_resources:114 | Less than 6GB VRAM available. Using conservative settings.
2025-09-08 16:47:09 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:120 | Initial GPU memory usage: 1.50GB
2025-09-08 16:47:09 | [32mINFO[0m | core.voice_processor | _initialize_recorder:128 | Configuring RealtimeSTT recorder...
2025-09-08 16:47:09 | [31mERROR[0m | core.voice_processor | _initialize_recorder:187 | Failed to initialize recorder: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 746, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 647, in __init__
    self.model = ctranslate2.models.Whisper(
RuntimeError: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
2025-09-08 16:47:09 | [31mERROR[0m | core.voice_processor | initialize:100 | Failed to initialize voice processor: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 88, in initialize
    await self._initialize_recorder()
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 746, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 647, in __init__
    self.model = ctranslate2.models.Whisper(
RuntimeError: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
2025-09-08 16:47:09 | [32mINFO[0m | __main__ | shutdown:183 | System shutdown initiated
2025-09-08 16:47:09 | [32mINFO[0m | ui.status_monitor | stop_monitoring:101 | Stopping status monitoring...
2025-09-08 16:47:10 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12491MB
2025-09-08 16:47:10 | [32mINFO[0m | ui.status_monitor | stop_monitoring:107 | Status monitoring stopped
2025-09-08 16:47:10 | [32mINFO[0m | utils.performance | stop:113 | Stopping performance monitoring...
2025-09-08 16:47:15 | [32mINFO[0m | utils.performance | stop:119 | Performance monitoring stopped
2025-09-08 16:47:15 | [32mINFO[0m | models.model_manager | shutdown:295 | Shutting down ModelManager...
2025-09-08 16:47:15 | [32mINFO[0m | models.model_manager | optimize_memory:262 | GPU memory optimized
2025-09-08 16:47:15 | [32mINFO[0m | models.model_manager | optimize_memory:266 | Memory after optimization: 0.00GB
2025-09-08 16:47:15 | [32mINFO[0m | models.model_manager | shutdown:311 | ModelManager shutdown complete
2025-09-08 16:47:15 | [32mINFO[0m | __main__ | shutdown:212 | System shutdown completed
2025-09-08 16:47:15 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 16:47:15 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 16:47:15 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:15 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:15 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 16:47:16 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 16:47:16 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:16 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 16:47:16 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:16 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:16 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 16:47:16 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:16 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 16:47:16 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 16:47:16 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 16:47:16 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 16:47:16 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 16:47:16 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:16 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:16 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:16 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:16 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 16:47:16 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 16:47:16 | [32mINFO[0m | __mp_main__ | setup_logger:139 | Logger '__mp_main__' initialized
2025-09-08 16:47:16 | [32mINFO[0m | __mp_main__ | setup_logger:139 | Logger '__mp_main__' initialized
2025-09-08 16:47:16 | [32mINFO[0m | __mp_main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:16 | [32mINFO[0m | __mp_main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:47:16 | [32mINFO[0m | __mp_main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:47:16 | [32mINFO[0m | __mp_main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:20 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 16:57:20 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:20 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:20 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 16:57:20 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 16:57:20 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:20 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:20 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:20 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:20 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 16:57:20 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 16:57:20 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:20 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:20 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:20 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:20 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 16:57:20 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 16:57:20 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:20 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:20 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:20 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:20 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 16:57:20 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 16:57:20 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:20 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:20 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:20 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:20 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 16:57:20 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 16:57:20 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:20 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:20 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:20 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:20 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 16:57:22 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 16:57:22 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:22 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:22 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 16:57:22 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 16:57:22 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 16:57:22 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:22 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:22 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 16:57:22 | [32mINFO[0m | __main__ | setup_logger:139 | Logger '__main__' initialized
2025-09-08 16:57:22 | [32mINFO[0m | __main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:22 | [32mINFO[0m | __main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:22 | [32mINFO[0m | __main__ | __init__:53 | VoiceToTextSystem initialized
2025-09-08 16:57:22 | [32mINFO[0m | __main__ | initialize:58 | Initializing Real-time Voice-to-Text System...
2025-09-08 16:57:22 | [32mINFO[0m | utils.performance | setup_logger:139 | Logger 'utils.performance' initialized
2025-09-08 16:57:22 | [32mINFO[0m | utils.performance | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:22 | [32mINFO[0m | utils.performance | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:22 | [32mINFO[0m | utils.performance | __init__:90 | PerformanceMonitor initialized
2025-09-08 16:57:22 | [32mINFO[0m | utils.performance | start:98 | Starting performance monitoring...
2025-09-08 16:57:22 | [32mINFO[0m | utils.performance | start:106 | Performance monitoring started
2025-09-08 16:57:22 | [32mINFO[0m | ui.status_monitor | setup_logger:139 | Logger 'ui.status_monitor' initialized
2025-09-08 16:57:22 | [32mINFO[0m | ui.status_monitor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:22 | [32mINFO[0m | ui.status_monitor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:22 | [32mINFO[0m | ui.status_monitor | __init__:78 | StatusMonitor initialized
2025-09-08 16:57:22 | [32mINFO[0m | ui.status_monitor | start_monitoring:86 | Starting status monitoring...
2025-09-08 16:57:22 | [32mINFO[0m | ui.status_monitor | start_monitoring:94 | Status monitoring started
2025-09-08 16:57:22 | [32mINFO[0m | utils.file_handler | setup_logger:139 | Logger 'utils.file_handler' initialized
2025-09-08 16:57:22 | [32mINFO[0m | utils.file_handler | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:22 | [32mINFO[0m | utils.file_handler | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:22 | DEBUG    | utils.file_handler | _ensure_directories:58 | Data directories ensured
2025-09-08 16:57:22 | [32mINFO[0m | utils.file_handler | __init__:44 | FileHandler initialized
2025-09-08 16:57:22 | [32mINFO[0m | ui.console_display | setup_logger:139 | Logger 'ui.console_display' initialized
2025-09-08 16:57:22 | [32mINFO[0m | ui.console_display | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:22 | [32mINFO[0m | ui.console_display | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:22 | [32mINFO[0m | ui.colors | setup_logger:139 | Logger 'ui.colors' initialized
2025-09-08 16:57:22 | [32mINFO[0m | ui.colors | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:22 | [32mINFO[0m | ui.colors | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:22 | [32mINFO[0m | ui.colors | __init__:120 | ColorManager initialized with theme: default
2025-09-08 16:57:22 | [32mINFO[0m | ui.console_display | __init__:67 | ConsoleDisplay initialized
2025-09-08 16:57:22 | [32mINFO[0m | models.model_manager | setup_logger:139 | Logger 'models.model_manager' initialized
2025-09-08 16:57:22 | [32mINFO[0m | models.model_manager | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:22 | [32mINFO[0m | models.model_manager | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:22 | [32mINFO[0m | models.model_manager | __init__:46 | ModelManager initialized
2025-09-08 16:57:22 | [32mINFO[0m | models.model_manager | initialize:51 | Starting model initialization...
2025-09-08 16:57:22 | [32mINFO[0m | models.model_manager | _check_gpu_status:76 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 16:57:22 | [32mINFO[0m | models.model_manager | _check_gpu_status:77 | Total VRAM: 6.0GB
2025-09-08 16:57:22 | [32mINFO[0m | models.model_manager | _check_gpu_status:78 | CUDA Version: 11.6
2025-09-08 16:57:22 | [32mINFO[0m | models.model_manager | _check_gpu_status:84 | Current VRAM usage: 0.00GB allocated, 0.00GB reserved
2025-09-08 16:57:22 | [32mINFO[0m | models.model_manager | _load_whisper_models:94 | Loading Whisper models...
2025-09-08 16:57:22 | [32mINFO[0m | models.model_manager | _load_whisper_models:97 | Loading primary Whisper model: medium.en
2025-09-08 16:57:22 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12779MB
2025-09-08 16:57:27 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11361MB
2025-09-08 16:57:33 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14111MB
2025-09-08 16:57:38 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14971MB
2025-09-08 16:57:42 | [32mINFO[0m | models.model_manager | _load_whisper_models:112 | Primary Whisper model loaded in 20.17s
2025-09-08 16:57:42 | [32mINFO[0m | models.model_manager | _load_whisper_models:120 | Primary model VRAM usage: 1.42GB
2025-09-08 16:57:42 | [32mINFO[0m | models.model_manager | _load_whisper_models:123 | Loading realtime Whisper model: tiny.en
2025-09-08 16:57:43 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11959MB
2025-09-08 16:57:43 | [32mINFO[0m | models.model_manager | _load_whisper_models:134 | Realtime Whisper model loaded in 0.75s
2025-09-08 16:57:43 | [32mINFO[0m | models.model_manager | _load_whisper_models:142 | Realtime model VRAM usage: 0.07GB
2025-09-08 16:57:43 | [32mINFO[0m | models.model_manager | _load_whisper_models:143 | Total VRAM usage: 1.50GB
2025-09-08 16:57:43 | [32mINFO[0m | models.model_manager | _load_emotion_model:152 | Loading emotion detection model...
2025-09-08 16:57:43 | [32mINFO[0m | models.model_manager | _load_emotion_model:164 | Loading transformer-based emotion classifier...
2025-09-08 16:57:46 | [32mINFO[0m | models.model_manager | _load_emotion_model:178 | Emotion model loaded in 3.02s
2025-09-08 16:57:46 | [32mINFO[0m | models.model_manager | _load_emotion_model:186 | Emotion model VRAM usage: 0.15GB
2025-09-08 16:57:46 | [32mINFO[0m | models.model_manager | _load_emotion_model:187 | Total VRAM usage: 1.65GB
2025-09-08 16:57:46 | [32mINFO[0m | models.model_manager | _verify_models:207 | Optional model 'emotion_detector': ✅ Loaded
2025-09-08 16:57:46 | [32mINFO[0m | models.model_manager | _verify_models:211 | Final VRAM usage: 1.65GB / 4.5GB limit
2025-09-08 16:57:46 | [32mINFO[0m | models.model_manager | initialize:63 | All models initialized successfully
2025-09-08 16:57:46 | [32mINFO[0m | core.voice_processor | setup_logger:139 | Logger 'core.voice_processor' initialized
2025-09-08 16:57:46 | [32mINFO[0m | core.voice_processor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:46 | [32mINFO[0m | core.voice_processor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:46 | [32mINFO[0m | core.audio_utils | setup_logger:139 | Logger 'core.audio_utils' initialized
2025-09-08 16:57:46 | [32mINFO[0m | core.audio_utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:46 | [32mINFO[0m | core.audio_utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:46 | [32mINFO[0m | core.audio_utils | __init__:28 | AudioUtils initialized
2025-09-08 16:57:46 | [32mINFO[0m | core.emotion_detector | setup_logger:139 | Logger 'core.emotion_detector' initialized
2025-09-08 16:57:46 | [32mINFO[0m | core.emotion_detector | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:46 | [32mINFO[0m | core.emotion_detector | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:46 | [32mINFO[0m | core.emotion_detector | __init__:75 | EmotionDetector initialized
2025-09-08 16:57:46 | [32mINFO[0m | core.text_formatter | setup_logger:139 | Logger 'core.text_formatter' initialized
2025-09-08 16:57:46 | [32mINFO[0m | core.text_formatter | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 16:57:46 | [32mINFO[0m | core.text_formatter | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 16:57:46 | [32mINFO[0m | core.text_formatter | __init__:62 | TextFormatter initialized
2025-09-08 16:57:46 | [32mINFO[0m | core.voice_processor | __init__:77 | VoiceProcessor initialized
2025-09-08 16:57:46 | [32mINFO[0m | core.voice_processor | initialize:82 | Initializing voice processor...
2025-09-08 16:57:46 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:109 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 16:57:46 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:110 | Total VRAM: 6.0GB
2025-09-08 16:57:46 | [33mWARNING[0m | core.voice_processor | _check_gpu_resources:114 | Less than 6GB VRAM available. Using conservative settings.
2025-09-08 16:57:46 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:120 | Initial GPU memory usage: 1.65GB
2025-09-08 16:57:46 | [32mINFO[0m | core.voice_processor | _initialize_recorder:128 | Configuring RealtimeSTT recorder...
2025-09-08 16:57:46 | [31mERROR[0m | core.voice_processor | _initialize_recorder:187 | Failed to initialize recorder: AudioToTextRecorder.__init__() got an unexpected keyword argument 'realtime_batch_size'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
TypeError: AudioToTextRecorder.__init__() got an unexpected keyword argument 'realtime_batch_size'
2025-09-08 16:57:46 | [31mERROR[0m | core.voice_processor | initialize:100 | Failed to initialize voice processor: AudioToTextRecorder.__init__() got an unexpected keyword argument 'realtime_batch_size'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 88, in initialize
    await self._initialize_recorder()
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
TypeError: AudioToTextRecorder.__init__() got an unexpected keyword argument 'realtime_batch_size'
2025-09-08 16:57:46 | [32mINFO[0m | __main__ | shutdown:183 | System shutdown initiated
2025-09-08 16:57:46 | [32mINFO[0m | ui.status_monitor | stop_monitoring:101 | Stopping status monitoring...
2025-09-08 16:57:47 | [32mINFO[0m | ui.status_monitor | stop_monitoring:107 | Status monitoring stopped
2025-09-08 16:57:47 | [32mINFO[0m | utils.performance | stop:113 | Stopping performance monitoring...
2025-09-08 16:57:48 | [32mINFO[0m | utils.performance | stop:119 | Performance monitoring stopped
2025-09-08 16:57:48 | [32mINFO[0m | models.model_manager | shutdown:295 | Shutting down ModelManager...
2025-09-08 16:57:48 | [32mINFO[0m | models.model_manager | optimize_memory:262 | GPU memory optimized
2025-09-08 16:57:48 | [32mINFO[0m | models.model_manager | optimize_memory:266 | Memory after optimization: 0.00GB
2025-09-08 16:57:48 | [32mINFO[0m | models.model_manager | shutdown:311 | ModelManager shutdown complete
2025-09-08 16:57:48 | [32mINFO[0m | __main__ | shutdown:212 | System shutdown completed
2025-09-08 17:11:10 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 17:11:10 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:10 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:10 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 17:11:10 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 17:11:10 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:10 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:10 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:10 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:10 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 17:11:10 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 17:11:10 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:10 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:10 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:10 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:10 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 17:11:10 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 17:11:10 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:10 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:10 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:10 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:10 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 17:11:10 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 17:11:10 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:10 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:10 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:10 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:10 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 17:11:10 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 17:11:10 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:10 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:10 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:10 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:10 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 17:11:11 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 17:11:11 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:11 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:11 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 17:11:11 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 17:11:11 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 17:11:11 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:11 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:11 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 17:11:11 | [32mINFO[0m | __main__ | setup_logger:139 | Logger '__main__' initialized
2025-09-08 17:11:11 | [32mINFO[0m | __main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:11 | [32mINFO[0m | __main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:11 | [32mINFO[0m | __main__ | __init__:53 | VoiceToTextSystem initialized
2025-09-08 17:11:11 | [32mINFO[0m | __main__ | initialize:58 | Initializing Real-time Voice-to-Text System...
2025-09-08 17:11:11 | [32mINFO[0m | utils.performance | setup_logger:139 | Logger 'utils.performance' initialized
2025-09-08 17:11:11 | [32mINFO[0m | utils.performance | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:11 | [32mINFO[0m | utils.performance | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:11 | [32mINFO[0m | utils.performance | __init__:90 | PerformanceMonitor initialized
2025-09-08 17:11:11 | [32mINFO[0m | utils.performance | start:98 | Starting performance monitoring...
2025-09-08 17:11:11 | [32mINFO[0m | utils.performance | start:106 | Performance monitoring started
2025-09-08 17:11:11 | [32mINFO[0m | ui.status_monitor | setup_logger:139 | Logger 'ui.status_monitor' initialized
2025-09-08 17:11:11 | [32mINFO[0m | ui.status_monitor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:11 | [32mINFO[0m | ui.status_monitor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:11 | [32mINFO[0m | ui.status_monitor | __init__:78 | StatusMonitor initialized
2025-09-08 17:11:11 | [32mINFO[0m | ui.status_monitor | start_monitoring:86 | Starting status monitoring...
2025-09-08 17:11:11 | [32mINFO[0m | ui.status_monitor | start_monitoring:94 | Status monitoring started
2025-09-08 17:11:11 | [32mINFO[0m | utils.file_handler | setup_logger:139 | Logger 'utils.file_handler' initialized
2025-09-08 17:11:11 | [32mINFO[0m | utils.file_handler | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:11 | [32mINFO[0m | utils.file_handler | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:11 | DEBUG    | utils.file_handler | _ensure_directories:58 | Data directories ensured
2025-09-08 17:11:11 | [32mINFO[0m | utils.file_handler | __init__:44 | FileHandler initialized
2025-09-08 17:11:11 | [32mINFO[0m | ui.console_display | setup_logger:139 | Logger 'ui.console_display' initialized
2025-09-08 17:11:11 | [32mINFO[0m | ui.console_display | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:11 | [32mINFO[0m | ui.console_display | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:11 | [32mINFO[0m | ui.colors | setup_logger:139 | Logger 'ui.colors' initialized
2025-09-08 17:11:11 | [32mINFO[0m | ui.colors | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:11 | [32mINFO[0m | ui.colors | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:11 | [32mINFO[0m | ui.colors | __init__:120 | ColorManager initialized with theme: default
2025-09-08 17:11:11 | [32mINFO[0m | ui.console_display | __init__:67 | ConsoleDisplay initialized
2025-09-08 17:11:11 | [32mINFO[0m | models.model_manager | setup_logger:139 | Logger 'models.model_manager' initialized
2025-09-08 17:11:11 | [32mINFO[0m | models.model_manager | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:11 | [32mINFO[0m | models.model_manager | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:11 | [32mINFO[0m | models.model_manager | __init__:46 | ModelManager initialized
2025-09-08 17:11:11 | [32mINFO[0m | models.model_manager | initialize:51 | Starting model initialization...
2025-09-08 17:11:11 | [32mINFO[0m | models.model_manager | _check_gpu_status:76 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 17:11:11 | [32mINFO[0m | models.model_manager | _check_gpu_status:77 | Total VRAM: 6.0GB
2025-09-08 17:11:11 | [32mINFO[0m | models.model_manager | _check_gpu_status:78 | CUDA Version: 11.6
2025-09-08 17:11:11 | [32mINFO[0m | models.model_manager | _check_gpu_status:84 | Current VRAM usage: 0.00GB allocated, 0.00GB reserved
2025-09-08 17:11:11 | [32mINFO[0m | models.model_manager | _load_whisper_models:94 | Loading Whisper models...
2025-09-08 17:11:11 | [32mINFO[0m | models.model_manager | _load_whisper_models:97 | Loading primary Whisper model: medium.en
2025-09-08 17:11:11 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12136MB
2025-09-08 17:11:16 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11526MB
2025-09-08 17:11:21 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14527MB
2025-09-08 17:11:25 | [32mINFO[0m | models.model_manager | _load_whisper_models:112 | Primary Whisper model loaded in 14.30s
2025-09-08 17:11:25 | [32mINFO[0m | models.model_manager | _load_whisper_models:120 | Primary model VRAM usage: 1.42GB
2025-09-08 17:11:25 | [32mINFO[0m | models.model_manager | _load_whisper_models:123 | Loading realtime Whisper model: tiny.en
2025-09-08 17:11:26 | [32mINFO[0m | models.model_manager | _load_whisper_models:134 | Realtime Whisper model loaded in 0.68s
2025-09-08 17:11:26 | [32mINFO[0m | models.model_manager | _load_whisper_models:142 | Realtime model VRAM usage: 0.07GB
2025-09-08 17:11:26 | [32mINFO[0m | models.model_manager | _load_whisper_models:143 | Total VRAM usage: 1.50GB
2025-09-08 17:11:26 | [32mINFO[0m | models.model_manager | _load_emotion_model:152 | Loading emotion detection model...
2025-09-08 17:11:26 | [32mINFO[0m | models.model_manager | _load_emotion_model:164 | Loading transformer-based emotion classifier...
2025-09-08 17:11:26 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12337MB
2025-09-08 17:11:27 | [32mINFO[0m | models.model_manager | _load_emotion_model:178 | Emotion model loaded in 1.39s
2025-09-08 17:11:27 | [32mINFO[0m | models.model_manager | _load_emotion_model:186 | Emotion model VRAM usage: 0.15GB
2025-09-08 17:11:27 | [32mINFO[0m | models.model_manager | _load_emotion_model:187 | Total VRAM usage: 1.65GB
2025-09-08 17:11:27 | [32mINFO[0m | models.model_manager | _verify_models:207 | Optional model 'emotion_detector': ✅ Loaded
2025-09-08 17:11:27 | [32mINFO[0m | models.model_manager | _verify_models:211 | Final VRAM usage: 1.65GB / 4.5GB limit
2025-09-08 17:11:27 | [32mINFO[0m | models.model_manager | initialize:63 | All models initialized successfully
2025-09-08 17:11:27 | [32mINFO[0m | core.voice_processor | setup_logger:139 | Logger 'core.voice_processor' initialized
2025-09-08 17:11:27 | [32mINFO[0m | core.voice_processor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:27 | [32mINFO[0m | core.voice_processor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:27 | [32mINFO[0m | core.audio_utils | setup_logger:139 | Logger 'core.audio_utils' initialized
2025-09-08 17:11:27 | [32mINFO[0m | core.audio_utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:27 | [32mINFO[0m | core.audio_utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:27 | [32mINFO[0m | core.audio_utils | __init__:28 | AudioUtils initialized
2025-09-08 17:11:27 | [32mINFO[0m | core.emotion_detector | setup_logger:139 | Logger 'core.emotion_detector' initialized
2025-09-08 17:11:27 | [32mINFO[0m | core.emotion_detector | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:27 | [32mINFO[0m | core.emotion_detector | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:27 | [32mINFO[0m | core.emotion_detector | __init__:75 | EmotionDetector initialized
2025-09-08 17:11:27 | [32mINFO[0m | core.text_formatter | setup_logger:139 | Logger 'core.text_formatter' initialized
2025-09-08 17:11:27 | [32mINFO[0m | core.text_formatter | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:11:27 | [32mINFO[0m | core.text_formatter | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:11:27 | [32mINFO[0m | core.text_formatter | __init__:62 | TextFormatter initialized
2025-09-08 17:11:27 | [32mINFO[0m | core.voice_processor | __init__:77 | VoiceProcessor initialized
2025-09-08 17:11:27 | [32mINFO[0m | core.voice_processor | initialize:82 | Initializing voice processor...
2025-09-08 17:11:27 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:109 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 17:11:27 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:110 | Total VRAM: 6.0GB
2025-09-08 17:11:27 | [33mWARNING[0m | core.voice_processor | _check_gpu_resources:114 | Less than 6GB VRAM available. Using conservative settings.
2025-09-08 17:11:27 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:120 | Initial GPU memory usage: 1.65GB
2025-09-08 17:11:27 | [32mINFO[0m | core.voice_processor | _initialize_recorder:128 | Configuring RealtimeSTT recorder...
2025-09-08 17:11:27 | [31mERROR[0m | core.voice_processor | _initialize_recorder:186 | Failed to initialize recorder: AudioToTextRecorder.__init__() got an unexpected keyword argument 'batch_size'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
TypeError: AudioToTextRecorder.__init__() got an unexpected keyword argument 'batch_size'
2025-09-08 17:11:27 | [31mERROR[0m | core.voice_processor | initialize:100 | Failed to initialize voice processor: AudioToTextRecorder.__init__() got an unexpected keyword argument 'batch_size'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 88, in initialize
    await self._initialize_recorder()
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
TypeError: AudioToTextRecorder.__init__() got an unexpected keyword argument 'batch_size'
2025-09-08 17:11:27 | [32mINFO[0m | __main__ | shutdown:183 | System shutdown initiated
2025-09-08 17:11:27 | [32mINFO[0m | ui.status_monitor | stop_monitoring:101 | Stopping status monitoring...
2025-09-08 17:11:28 | [32mINFO[0m | ui.status_monitor | stop_monitoring:107 | Status monitoring stopped
2025-09-08 17:11:28 | [32mINFO[0m | utils.performance | stop:113 | Stopping performance monitoring...
2025-09-08 17:11:31 | [32mINFO[0m | utils.performance | stop:119 | Performance monitoring stopped
2025-09-08 17:11:31 | [32mINFO[0m | models.model_manager | shutdown:295 | Shutting down ModelManager...
2025-09-08 17:11:32 | [32mINFO[0m | models.model_manager | optimize_memory:262 | GPU memory optimized
2025-09-08 17:11:32 | [32mINFO[0m | models.model_manager | optimize_memory:266 | Memory after optimization: 0.00GB
2025-09-08 17:11:32 | [32mINFO[0m | models.model_manager | shutdown:311 | ModelManager shutdown complete
2025-09-08 17:11:32 | [32mINFO[0m | __main__ | shutdown:212 | System shutdown completed
2025-09-08 17:15:39 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 17:15:39 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:15:39 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:15:39 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 17:15:39 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 17:15:39 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:15:39 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:15:39 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:15:39 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:15:39 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 17:15:39 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 17:15:39 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:15:39 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:15:39 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:15:39 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:15:39 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 17:15:39 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 17:15:39 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:15:39 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:15:39 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:15:39 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:15:39 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 17:15:39 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 17:15:39 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:15:39 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:15:39 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:15:39 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:15:39 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 17:15:39 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 17:15:39 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:15:39 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:15:39 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:15:39 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:15:39 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 17:15:40 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 17:15:40 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:15:40 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:15:40 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 17:15:40 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 17:15:40 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 17:15:40 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:15:40 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:15:40 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 17:15:40 | [32mINFO[0m | __main__ | setup_logger:139 | Logger '__main__' initialized
2025-09-08 17:15:40 | [32mINFO[0m | __main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:15:40 | [32mINFO[0m | __main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:15:40 | [32mINFO[0m | __main__ | __init__:53 | VoiceToTextSystem initialized
2025-09-08 17:15:40 | [32mINFO[0m | __main__ | initialize:58 | Initializing Real-time Voice-to-Text System...
2025-09-08 17:15:40 | [32mINFO[0m | utils.performance | setup_logger:139 | Logger 'utils.performance' initialized
2025-09-08 17:15:40 | [32mINFO[0m | utils.performance | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:15:40 | [32mINFO[0m | utils.performance | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:15:40 | [32mINFO[0m | utils.performance | __init__:90 | PerformanceMonitor initialized
2025-09-08 17:15:40 | [32mINFO[0m | utils.performance | start:98 | Starting performance monitoring...
2025-09-08 17:15:40 | [32mINFO[0m | utils.performance | start:106 | Performance monitoring started
2025-09-08 17:15:40 | [32mINFO[0m | ui.status_monitor | setup_logger:139 | Logger 'ui.status_monitor' initialized
2025-09-08 17:15:40 | [32mINFO[0m | ui.status_monitor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:15:40 | [32mINFO[0m | ui.status_monitor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:15:40 | [32mINFO[0m | ui.status_monitor | __init__:78 | StatusMonitor initialized
2025-09-08 17:15:40 | [32mINFO[0m | ui.status_monitor | start_monitoring:86 | Starting status monitoring...
2025-09-08 17:15:40 | [32mINFO[0m | ui.status_monitor | start_monitoring:94 | Status monitoring started
2025-09-08 17:15:40 | [32mINFO[0m | utils.file_handler | setup_logger:139 | Logger 'utils.file_handler' initialized
2025-09-08 17:15:40 | [32mINFO[0m | utils.file_handler | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:15:40 | [32mINFO[0m | utils.file_handler | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:15:40 | DEBUG    | utils.file_handler | _ensure_directories:58 | Data directories ensured
2025-09-08 17:15:40 | [32mINFO[0m | utils.file_handler | __init__:44 | FileHandler initialized
2025-09-08 17:15:40 | [32mINFO[0m | ui.console_display | setup_logger:139 | Logger 'ui.console_display' initialized
2025-09-08 17:15:40 | [32mINFO[0m | ui.console_display | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:15:40 | [32mINFO[0m | ui.console_display | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:15:40 | [32mINFO[0m | ui.colors | setup_logger:139 | Logger 'ui.colors' initialized
2025-09-08 17:15:40 | [32mINFO[0m | ui.colors | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:15:40 | [32mINFO[0m | ui.colors | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:15:40 | [32mINFO[0m | ui.colors | __init__:120 | ColorManager initialized with theme: default
2025-09-08 17:15:40 | [32mINFO[0m | ui.console_display | __init__:67 | ConsoleDisplay initialized
2025-09-08 17:15:40 | [32mINFO[0m | models.model_manager | setup_logger:139 | Logger 'models.model_manager' initialized
2025-09-08 17:15:40 | [32mINFO[0m | models.model_manager | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:15:40 | [32mINFO[0m | models.model_manager | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:15:40 | [32mINFO[0m | models.model_manager | __init__:46 | ModelManager initialized
2025-09-08 17:15:40 | [32mINFO[0m | models.model_manager | initialize:51 | Starting model initialization...
2025-09-08 17:15:40 | [32mINFO[0m | models.model_manager | _check_gpu_status:76 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 17:15:40 | [32mINFO[0m | models.model_manager | _check_gpu_status:77 | Total VRAM: 6.0GB
2025-09-08 17:15:40 | [32mINFO[0m | models.model_manager | _check_gpu_status:78 | CUDA Version: 11.6
2025-09-08 17:15:40 | [32mINFO[0m | models.model_manager | _check_gpu_status:84 | Current VRAM usage: 0.00GB allocated, 0.00GB reserved
2025-09-08 17:15:40 | [32mINFO[0m | models.model_manager | _load_whisper_models:94 | Loading Whisper models...
2025-09-08 17:15:40 | [32mINFO[0m | models.model_manager | _load_whisper_models:97 | Loading primary Whisper model: medium.en
2025-09-08 17:15:40 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 13131MB
2025-09-08 17:15:46 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12022MB
2025-09-08 17:15:51 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15102MB
2025-09-08 17:15:56 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15656MB
2025-09-08 17:16:01 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15540MB
2025-09-08 17:16:03 | [32mINFO[0m | models.model_manager | _load_whisper_models:112 | Primary Whisper model loaded in 23.16s
2025-09-08 17:16:03 | [32mINFO[0m | models.model_manager | _load_whisper_models:120 | Primary model VRAM usage: 1.42GB
2025-09-08 17:16:03 | [32mINFO[0m | models.model_manager | _load_whisper_models:123 | Loading realtime Whisper model: tiny.en
2025-09-08 17:16:04 | [32mINFO[0m | models.model_manager | _load_whisper_models:134 | Realtime Whisper model loaded in 0.70s
2025-09-08 17:16:04 | [32mINFO[0m | models.model_manager | _load_whisper_models:142 | Realtime model VRAM usage: 0.07GB
2025-09-08 17:16:04 | [32mINFO[0m | models.model_manager | _load_whisper_models:143 | Total VRAM usage: 1.50GB
2025-09-08 17:16:04 | [32mINFO[0m | models.model_manager | _load_emotion_model:152 | Loading emotion detection model...
2025-09-08 17:16:04 | [32mINFO[0m | models.model_manager | _load_emotion_model:164 | Loading transformer-based emotion classifier...
2025-09-08 17:16:06 | [32mINFO[0m | models.model_manager | _load_emotion_model:178 | Emotion model loaded in 1.67s
2025-09-08 17:16:06 | [32mINFO[0m | models.model_manager | _load_emotion_model:186 | Emotion model VRAM usage: 0.15GB
2025-09-08 17:16:06 | [32mINFO[0m | models.model_manager | _load_emotion_model:187 | Total VRAM usage: 1.65GB
2025-09-08 17:16:06 | [32mINFO[0m | models.model_manager | _verify_models:207 | Optional model 'emotion_detector': ✅ Loaded
2025-09-08 17:16:06 | [32mINFO[0m | models.model_manager | _verify_models:211 | Final VRAM usage: 1.65GB / 4.5GB limit
2025-09-08 17:16:06 | [32mINFO[0m | models.model_manager | initialize:63 | All models initialized successfully
2025-09-08 17:16:06 | [32mINFO[0m | core.voice_processor | setup_logger:139 | Logger 'core.voice_processor' initialized
2025-09-08 17:16:06 | [32mINFO[0m | core.voice_processor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:16:06 | [32mINFO[0m | core.voice_processor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:16:06 | [32mINFO[0m | core.audio_utils | setup_logger:139 | Logger 'core.audio_utils' initialized
2025-09-08 17:16:06 | [32mINFO[0m | core.audio_utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:16:06 | [32mINFO[0m | core.audio_utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:16:06 | [32mINFO[0m | core.audio_utils | __init__:28 | AudioUtils initialized
2025-09-08 17:16:06 | [32mINFO[0m | core.emotion_detector | setup_logger:139 | Logger 'core.emotion_detector' initialized
2025-09-08 17:16:06 | [32mINFO[0m | core.emotion_detector | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:16:06 | [32mINFO[0m | core.emotion_detector | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:16:06 | [32mINFO[0m | core.emotion_detector | __init__:75 | EmotionDetector initialized
2025-09-08 17:16:06 | [32mINFO[0m | core.text_formatter | setup_logger:139 | Logger 'core.text_formatter' initialized
2025-09-08 17:16:06 | [32mINFO[0m | core.text_formatter | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:16:06 | [32mINFO[0m | core.text_formatter | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:16:06 | [32mINFO[0m | core.text_formatter | __init__:62 | TextFormatter initialized
2025-09-08 17:16:06 | [32mINFO[0m | core.voice_processor | __init__:77 | VoiceProcessor initialized
2025-09-08 17:16:06 | [32mINFO[0m | core.voice_processor | initialize:82 | Initializing voice processor...
2025-09-08 17:16:06 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:109 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 17:16:06 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:110 | Total VRAM: 6.0GB
2025-09-08 17:16:06 | [33mWARNING[0m | core.voice_processor | _check_gpu_resources:114 | Less than 6GB VRAM available. Using conservative settings.
2025-09-08 17:16:06 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:120 | Initial GPU memory usage: 1.65GB
2025-09-08 17:16:06 | [32mINFO[0m | core.voice_processor | _initialize_recorder:128 | Configuring RealtimeSTT recorder...
2025-09-08 17:16:06 | [31mERROR[0m | core.voice_processor | _initialize_recorder:185 | Failed to initialize recorder: AudioToTextRecorder.__init__() got an unexpected keyword argument 'on_vad_start'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
TypeError: AudioToTextRecorder.__init__() got an unexpected keyword argument 'on_vad_start'
2025-09-08 17:16:06 | [31mERROR[0m | core.voice_processor | initialize:100 | Failed to initialize voice processor: AudioToTextRecorder.__init__() got an unexpected keyword argument 'on_vad_start'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 88, in initialize
    await self._initialize_recorder()
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
TypeError: AudioToTextRecorder.__init__() got an unexpected keyword argument 'on_vad_start'
2025-09-08 17:16:06 | [32mINFO[0m | __main__ | shutdown:183 | System shutdown initiated
2025-09-08 17:16:06 | [32mINFO[0m | ui.status_monitor | stop_monitoring:101 | Stopping status monitoring...
2025-09-08 17:16:06 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12691MB
2025-09-08 17:16:06 | [32mINFO[0m | ui.status_monitor | stop_monitoring:107 | Status monitoring stopped
2025-09-08 17:16:06 | [32mINFO[0m | utils.performance | stop:113 | Stopping performance monitoring...
2025-09-08 17:16:11 | [32mINFO[0m | utils.performance | stop:119 | Performance monitoring stopped
2025-09-08 17:16:11 | [32mINFO[0m | models.model_manager | shutdown:295 | Shutting down ModelManager...
2025-09-08 17:16:11 | [32mINFO[0m | models.model_manager | optimize_memory:262 | GPU memory optimized
2025-09-08 17:16:11 | [32mINFO[0m | models.model_manager | optimize_memory:266 | Memory after optimization: 0.00GB
2025-09-08 17:16:11 | [32mINFO[0m | models.model_manager | shutdown:311 | ModelManager shutdown complete
2025-09-08 17:16:11 | [32mINFO[0m | __main__ | shutdown:212 | System shutdown completed
2025-09-08 17:22:18 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 17:22:18 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:18 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:18 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 17:22:18 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 17:22:18 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:18 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:18 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:18 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:18 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 17:22:18 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 17:22:18 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:18 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:18 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:18 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:18 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 17:22:18 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 17:22:18 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:18 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:18 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:18 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:18 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 17:22:18 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 17:22:18 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:18 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:18 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:18 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:18 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 17:22:18 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 17:22:18 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:18 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:18 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:18 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:18 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 17:22:20 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 17:22:20 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:20 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:20 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 17:22:20 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 17:22:20 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 17:22:20 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:20 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:20 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 17:22:20 | [32mINFO[0m | __main__ | setup_logger:139 | Logger '__main__' initialized
2025-09-08 17:22:20 | [32mINFO[0m | __main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:20 | [32mINFO[0m | __main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:20 | [32mINFO[0m | __main__ | __init__:53 | VoiceToTextSystem initialized
2025-09-08 17:22:20 | [32mINFO[0m | __main__ | initialize:58 | Initializing Real-time Voice-to-Text System...
2025-09-08 17:22:20 | [32mINFO[0m | utils.performance | setup_logger:139 | Logger 'utils.performance' initialized
2025-09-08 17:22:20 | [32mINFO[0m | utils.performance | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:20 | [32mINFO[0m | utils.performance | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:20 | [32mINFO[0m | utils.performance | __init__:90 | PerformanceMonitor initialized
2025-09-08 17:22:20 | [32mINFO[0m | utils.performance | start:98 | Starting performance monitoring...
2025-09-08 17:22:20 | [32mINFO[0m | utils.performance | start:106 | Performance monitoring started
2025-09-08 17:22:20 | [32mINFO[0m | ui.status_monitor | setup_logger:139 | Logger 'ui.status_monitor' initialized
2025-09-08 17:22:20 | [32mINFO[0m | ui.status_monitor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:20 | [32mINFO[0m | ui.status_monitor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:20 | [32mINFO[0m | ui.status_monitor | __init__:78 | StatusMonitor initialized
2025-09-08 17:22:20 | [32mINFO[0m | ui.status_monitor | start_monitoring:86 | Starting status monitoring...
2025-09-08 17:22:20 | [32mINFO[0m | ui.status_monitor | start_monitoring:94 | Status monitoring started
2025-09-08 17:22:20 | [32mINFO[0m | utils.file_handler | setup_logger:139 | Logger 'utils.file_handler' initialized
2025-09-08 17:22:20 | [32mINFO[0m | utils.file_handler | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:20 | [32mINFO[0m | utils.file_handler | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:20 | DEBUG    | utils.file_handler | _ensure_directories:58 | Data directories ensured
2025-09-08 17:22:20 | [32mINFO[0m | utils.file_handler | __init__:44 | FileHandler initialized
2025-09-08 17:22:20 | [32mINFO[0m | ui.console_display | setup_logger:139 | Logger 'ui.console_display' initialized
2025-09-08 17:22:20 | [32mINFO[0m | ui.console_display | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:20 | [32mINFO[0m | ui.console_display | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:20 | [32mINFO[0m | ui.colors | setup_logger:139 | Logger 'ui.colors' initialized
2025-09-08 17:22:20 | [32mINFO[0m | ui.colors | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:20 | [32mINFO[0m | ui.colors | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:20 | [32mINFO[0m | ui.colors | __init__:120 | ColorManager initialized with theme: default
2025-09-08 17:22:20 | [32mINFO[0m | ui.console_display | __init__:67 | ConsoleDisplay initialized
2025-09-08 17:22:20 | [32mINFO[0m | models.model_manager | setup_logger:139 | Logger 'models.model_manager' initialized
2025-09-08 17:22:20 | [32mINFO[0m | models.model_manager | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:20 | [32mINFO[0m | models.model_manager | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:20 | [32mINFO[0m | models.model_manager | __init__:46 | ModelManager initialized
2025-09-08 17:22:20 | [32mINFO[0m | models.model_manager | initialize:51 | Starting model initialization...
2025-09-08 17:22:20 | [32mINFO[0m | models.model_manager | _check_gpu_status:76 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 17:22:20 | [32mINFO[0m | models.model_manager | _check_gpu_status:77 | Total VRAM: 6.0GB
2025-09-08 17:22:20 | [32mINFO[0m | models.model_manager | _check_gpu_status:78 | CUDA Version: 11.6
2025-09-08 17:22:20 | [32mINFO[0m | models.model_manager | _check_gpu_status:84 | Current VRAM usage: 0.00GB allocated, 0.00GB reserved
2025-09-08 17:22:20 | [32mINFO[0m | models.model_manager | _load_whisper_models:94 | Loading Whisper models...
2025-09-08 17:22:20 | [32mINFO[0m | models.model_manager | _load_whisper_models:97 | Loading primary Whisper model: medium.en
2025-09-08 17:22:20 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 13345MB
2025-09-08 17:22:25 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12436MB
2025-09-08 17:22:30 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15024MB
2025-09-08 17:22:35 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15556MB
2025-09-08 17:22:40 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15679MB
2025-09-08 17:22:45 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15566MB
2025-09-08 17:22:48 | [32mINFO[0m | models.model_manager | _load_whisper_models:112 | Primary Whisper model loaded in 28.49s
2025-09-08 17:22:48 | [32mINFO[0m | models.model_manager | _load_whisper_models:120 | Primary model VRAM usage: 1.42GB
2025-09-08 17:22:48 | [32mINFO[0m | models.model_manager | _load_whisper_models:123 | Loading realtime Whisper model: tiny.en
2025-09-08 17:22:49 | [32mINFO[0m | models.model_manager | _load_whisper_models:134 | Realtime Whisper model loaded in 0.72s
2025-09-08 17:22:49 | [32mINFO[0m | models.model_manager | _load_whisper_models:142 | Realtime model VRAM usage: 0.07GB
2025-09-08 17:22:49 | [32mINFO[0m | models.model_manager | _load_whisper_models:143 | Total VRAM usage: 1.50GB
2025-09-08 17:22:49 | [32mINFO[0m | models.model_manager | _load_emotion_model:152 | Loading emotion detection model...
2025-09-08 17:22:49 | [32mINFO[0m | models.model_manager | _load_emotion_model:164 | Loading transformer-based emotion classifier...
2025-09-08 17:22:50 | [32mINFO[0m | models.model_manager | _load_emotion_model:178 | Emotion model loaded in 1.49s
2025-09-08 17:22:50 | [32mINFO[0m | models.model_manager | _load_emotion_model:186 | Emotion model VRAM usage: 0.15GB
2025-09-08 17:22:50 | [32mINFO[0m | models.model_manager | _load_emotion_model:187 | Total VRAM usage: 1.65GB
2025-09-08 17:22:50 | [32mINFO[0m | models.model_manager | _verify_models:207 | Optional model 'emotion_detector': ✅ Loaded
2025-09-08 17:22:50 | [32mINFO[0m | models.model_manager | _verify_models:211 | Final VRAM usage: 1.65GB / 4.5GB limit
2025-09-08 17:22:50 | [32mINFO[0m | models.model_manager | initialize:63 | All models initialized successfully
2025-09-08 17:22:50 | [32mINFO[0m | core.voice_processor | setup_logger:139 | Logger 'core.voice_processor' initialized
2025-09-08 17:22:50 | [32mINFO[0m | core.voice_processor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:50 | [32mINFO[0m | core.voice_processor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:50 | [32mINFO[0m | core.audio_utils | setup_logger:139 | Logger 'core.audio_utils' initialized
2025-09-08 17:22:50 | [32mINFO[0m | core.audio_utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:50 | [32mINFO[0m | core.audio_utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:50 | [32mINFO[0m | core.audio_utils | __init__:28 | AudioUtils initialized
2025-09-08 17:22:50 | [32mINFO[0m | core.emotion_detector | setup_logger:139 | Logger 'core.emotion_detector' initialized
2025-09-08 17:22:50 | [32mINFO[0m | core.emotion_detector | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:50 | [32mINFO[0m | core.emotion_detector | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:50 | [32mINFO[0m | core.emotion_detector | __init__:75 | EmotionDetector initialized
2025-09-08 17:22:50 | [32mINFO[0m | core.text_formatter | setup_logger:139 | Logger 'core.text_formatter' initialized
2025-09-08 17:22:50 | [32mINFO[0m | core.text_formatter | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:50 | [32mINFO[0m | core.text_formatter | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:50 | [32mINFO[0m | core.text_formatter | __init__:62 | TextFormatter initialized
2025-09-08 17:22:50 | [32mINFO[0m | core.voice_processor | __init__:77 | VoiceProcessor initialized
2025-09-08 17:22:50 | [32mINFO[0m | core.voice_processor | initialize:82 | Initializing voice processor...
2025-09-08 17:22:50 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:109 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 17:22:50 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:110 | Total VRAM: 6.0GB
2025-09-08 17:22:50 | [33mWARNING[0m | core.voice_processor | _check_gpu_resources:114 | Less than 6GB VRAM available. Using conservative settings.
2025-09-08 17:22:50 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:120 | Initial GPU memory usage: 1.65GB
2025-09-08 17:22:50 | [32mINFO[0m | core.voice_processor | _initialize_recorder:128 | Configuring RealtimeSTT recorder...
2025-09-08 17:22:51 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12737MB
2025-09-08 17:22:51 | [31mERROR[0m | core.voice_processor | _initialize_recorder:183 | Failed to initialize recorder: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 672, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 145, in __init__
    self.model = ctranslate2.models.Whisper(
RuntimeError: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
2025-09-08 17:22:51 | [31mERROR[0m | core.voice_processor | initialize:100 | Failed to initialize voice processor: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 88, in initialize
    await self._initialize_recorder()
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 672, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 145, in __init__
    self.model = ctranslate2.models.Whisper(
RuntimeError: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
2025-09-08 17:22:51 | [32mINFO[0m | __main__ | shutdown:183 | System shutdown initiated
2025-09-08 17:22:51 | [32mINFO[0m | ui.status_monitor | stop_monitoring:101 | Stopping status monitoring...
2025-09-08 17:22:52 | [32mINFO[0m | ui.status_monitor | stop_monitoring:107 | Status monitoring stopped
2025-09-08 17:22:52 | [32mINFO[0m | utils.performance | stop:113 | Stopping performance monitoring...
2025-09-08 17:22:55 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 17:22:55 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:55 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 17:22:55 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:55 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 17:22:56 | [32mINFO[0m | utils.performance | stop:119 | Performance monitoring stopped
2025-09-08 17:22:56 | [32mINFO[0m | models.model_manager | shutdown:295 | Shutting down ModelManager...
2025-09-08 17:22:56 | [32mINFO[0m | models.model_manager | optimize_memory:262 | GPU memory optimized
2025-09-08 17:22:56 | [32mINFO[0m | models.model_manager | optimize_memory:266 | Memory after optimization: 0.00GB
2025-09-08 17:22:56 | [32mINFO[0m | models.model_manager | shutdown:311 | ModelManager shutdown complete
2025-09-08 17:22:56 | [32mINFO[0m | __main__ | shutdown:212 | System shutdown completed
2025-09-08 17:22:57 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 17:22:57 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 17:22:57 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:57 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:57 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:57 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:57 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 17:22:57 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 17:22:57 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 17:22:57 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 17:22:57 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 17:22:57 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 17:22:57 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:57 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:57 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:57 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:57 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 17:22:57 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 17:22:57 | [32mINFO[0m | __mp_main__ | setup_logger:139 | Logger '__mp_main__' initialized
2025-09-08 17:22:57 | [32mINFO[0m | __mp_main__ | setup_logger:139 | Logger '__mp_main__' initialized
2025-09-08 17:22:57 | [32mINFO[0m | __mp_main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:57 | [32mINFO[0m | __mp_main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:22:57 | [32mINFO[0m | __mp_main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:22:57 | [32mINFO[0m | __mp_main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:23:41 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 17:23:41 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:23:41 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:23:41 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 17:23:41 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 17:23:41 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:23:41 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:23:41 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:23:41 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:23:41 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 17:23:41 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 17:23:41 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:23:41 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:23:41 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:23:41 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:23:41 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 17:23:41 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 17:23:41 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:23:41 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:23:41 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:23:41 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:23:41 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 17:23:41 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 17:23:41 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:23:41 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:23:41 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:23:41 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:23:41 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 17:23:41 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 17:23:41 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:23:41 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:23:41 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:23:41 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:23:41 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 17:23:42 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 17:23:42 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:23:42 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:23:42 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 17:23:42 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 17:23:42 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 17:23:42 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:23:42 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:23:42 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 17:23:42 | [32mINFO[0m | __main__ | setup_logger:139 | Logger '__main__' initialized
2025-09-08 17:23:42 | [32mINFO[0m | __main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:23:42 | [32mINFO[0m | __main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:23:42 | [32mINFO[0m | __main__ | __init__:53 | VoiceToTextSystem initialized
2025-09-08 17:23:42 | [32mINFO[0m | __main__ | initialize:58 | Initializing Real-time Voice-to-Text System...
2025-09-08 17:23:42 | [32mINFO[0m | utils.performance | setup_logger:139 | Logger 'utils.performance' initialized
2025-09-08 17:23:42 | [32mINFO[0m | utils.performance | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:23:42 | [32mINFO[0m | utils.performance | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:23:42 | [32mINFO[0m | utils.performance | __init__:90 | PerformanceMonitor initialized
2025-09-08 17:23:42 | [32mINFO[0m | utils.performance | start:98 | Starting performance monitoring...
2025-09-08 17:23:42 | [32mINFO[0m | utils.performance | start:106 | Performance monitoring started
2025-09-08 17:23:42 | [32mINFO[0m | ui.status_monitor | setup_logger:139 | Logger 'ui.status_monitor' initialized
2025-09-08 17:23:42 | [32mINFO[0m | ui.status_monitor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:23:42 | [32mINFO[0m | ui.status_monitor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:23:42 | [32mINFO[0m | ui.status_monitor | __init__:78 | StatusMonitor initialized
2025-09-08 17:23:42 | [32mINFO[0m | ui.status_monitor | start_monitoring:86 | Starting status monitoring...
2025-09-08 17:23:42 | [32mINFO[0m | ui.status_monitor | start_monitoring:94 | Status monitoring started
2025-09-08 17:23:42 | [32mINFO[0m | utils.file_handler | setup_logger:139 | Logger 'utils.file_handler' initialized
2025-09-08 17:23:42 | [32mINFO[0m | utils.file_handler | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:23:42 | [32mINFO[0m | utils.file_handler | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:23:42 | DEBUG    | utils.file_handler | _ensure_directories:58 | Data directories ensured
2025-09-08 17:23:42 | [32mINFO[0m | utils.file_handler | __init__:44 | FileHandler initialized
2025-09-08 17:23:42 | [32mINFO[0m | ui.console_display | setup_logger:139 | Logger 'ui.console_display' initialized
2025-09-08 17:23:42 | [32mINFO[0m | ui.console_display | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:23:42 | [32mINFO[0m | ui.console_display | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:23:42 | [32mINFO[0m | ui.colors | setup_logger:139 | Logger 'ui.colors' initialized
2025-09-08 17:23:42 | [32mINFO[0m | ui.colors | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:23:42 | [32mINFO[0m | ui.colors | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:23:42 | [32mINFO[0m | ui.colors | __init__:120 | ColorManager initialized with theme: default
2025-09-08 17:23:42 | [32mINFO[0m | ui.console_display | __init__:67 | ConsoleDisplay initialized
2025-09-08 17:23:42 | [32mINFO[0m | models.model_manager | setup_logger:139 | Logger 'models.model_manager' initialized
2025-09-08 17:23:42 | [32mINFO[0m | models.model_manager | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:23:42 | [32mINFO[0m | models.model_manager | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:23:42 | [32mINFO[0m | models.model_manager | __init__:46 | ModelManager initialized
2025-09-08 17:23:42 | [32mINFO[0m | models.model_manager | initialize:51 | Starting model initialization...
2025-09-08 17:23:42 | [32mINFO[0m | models.model_manager | _check_gpu_status:76 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 17:23:42 | [32mINFO[0m | models.model_manager | _check_gpu_status:77 | Total VRAM: 6.0GB
2025-09-08 17:23:42 | [32mINFO[0m | models.model_manager | _check_gpu_status:78 | CUDA Version: 11.6
2025-09-08 17:23:42 | [32mINFO[0m | models.model_manager | _check_gpu_status:84 | Current VRAM usage: 0.00GB allocated, 0.00GB reserved
2025-09-08 17:23:42 | [32mINFO[0m | models.model_manager | _load_whisper_models:94 | Loading Whisper models...
2025-09-08 17:23:42 | [32mINFO[0m | models.model_manager | _load_whisper_models:97 | Loading primary Whisper model: medium.en
2025-09-08 17:23:42 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14514MB
2025-09-08 17:23:47 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 13146MB
2025-09-08 17:23:52 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15504MB
2025-09-08 17:23:57 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15941MB
2025-09-08 17:24:02 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15616MB
2025-09-08 17:24:07 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 13052MB
2025-09-08 17:24:08 | [32mINFO[0m | models.model_manager | _load_whisper_models:112 | Primary Whisper model loaded in 26.08s
2025-09-08 17:24:08 | [32mINFO[0m | models.model_manager | _load_whisper_models:120 | Primary model VRAM usage: 1.42GB
2025-09-08 17:24:08 | [32mINFO[0m | models.model_manager | _load_whisper_models:123 | Loading realtime Whisper model: tiny.en
2025-09-08 17:24:08 | [32mINFO[0m | models.model_manager | _load_whisper_models:134 | Realtime Whisper model loaded in 0.77s
2025-09-08 17:24:08 | [32mINFO[0m | models.model_manager | _load_whisper_models:142 | Realtime model VRAM usage: 0.07GB
2025-09-08 17:24:08 | [32mINFO[0m | models.model_manager | _load_whisper_models:143 | Total VRAM usage: 1.50GB
2025-09-08 17:24:08 | [32mINFO[0m | models.model_manager | _load_emotion_model:152 | Loading emotion detection model...
2025-09-08 17:24:08 | [32mINFO[0m | models.model_manager | _load_emotion_model:164 | Loading transformer-based emotion classifier...
2025-09-08 17:24:11 | [32mINFO[0m | models.model_manager | _load_emotion_model:178 | Emotion model loaded in 2.75s
2025-09-08 17:24:11 | [32mINFO[0m | models.model_manager | _load_emotion_model:186 | Emotion model VRAM usage: 0.15GB
2025-09-08 17:24:11 | [32mINFO[0m | models.model_manager | _load_emotion_model:187 | Total VRAM usage: 1.65GB
2025-09-08 17:24:11 | [32mINFO[0m | models.model_manager | _verify_models:207 | Optional model 'emotion_detector': ✅ Loaded
2025-09-08 17:24:11 | [32mINFO[0m | models.model_manager | _verify_models:211 | Final VRAM usage: 1.65GB / 4.5GB limit
2025-09-08 17:24:11 | [32mINFO[0m | models.model_manager | initialize:63 | All models initialized successfully
2025-09-08 17:24:11 | [32mINFO[0m | core.voice_processor | setup_logger:139 | Logger 'core.voice_processor' initialized
2025-09-08 17:24:11 | [32mINFO[0m | core.voice_processor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:11 | [32mINFO[0m | core.voice_processor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:11 | [32mINFO[0m | core.audio_utils | setup_logger:139 | Logger 'core.audio_utils' initialized
2025-09-08 17:24:11 | [32mINFO[0m | core.audio_utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:11 | [32mINFO[0m | core.audio_utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:11 | [32mINFO[0m | core.audio_utils | __init__:28 | AudioUtils initialized
2025-09-08 17:24:11 | [32mINFO[0m | core.emotion_detector | setup_logger:139 | Logger 'core.emotion_detector' initialized
2025-09-08 17:24:11 | [32mINFO[0m | core.emotion_detector | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:11 | [32mINFO[0m | core.emotion_detector | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:11 | [32mINFO[0m | core.emotion_detector | __init__:75 | EmotionDetector initialized
2025-09-08 17:24:11 | [32mINFO[0m | core.text_formatter | setup_logger:139 | Logger 'core.text_formatter' initialized
2025-09-08 17:24:11 | [32mINFO[0m | core.text_formatter | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:11 | [32mINFO[0m | core.text_formatter | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:11 | [32mINFO[0m | core.text_formatter | __init__:62 | TextFormatter initialized
2025-09-08 17:24:11 | [32mINFO[0m | core.voice_processor | __init__:77 | VoiceProcessor initialized
2025-09-08 17:24:11 | [32mINFO[0m | core.voice_processor | initialize:82 | Initializing voice processor...
2025-09-08 17:24:11 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:109 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 17:24:11 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:110 | Total VRAM: 6.0GB
2025-09-08 17:24:11 | [33mWARNING[0m | core.voice_processor | _check_gpu_resources:114 | Less than 6GB VRAM available. Using conservative settings.
2025-09-08 17:24:11 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:120 | Initial GPU memory usage: 1.65GB
2025-09-08 17:24:11 | [32mINFO[0m | core.voice_processor | _initialize_recorder:128 | Configuring RealtimeSTT recorder...
2025-09-08 17:24:12 | [31mERROR[0m | core.voice_processor | _initialize_recorder:184 | Failed to initialize recorder: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 672, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 145, in __init__
    self.model = ctranslate2.models.Whisper(
TypeError: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
2025-09-08 17:24:12 | [31mERROR[0m | core.voice_processor | initialize:100 | Failed to initialize voice processor: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 88, in initialize
    await self._initialize_recorder()
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 672, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 145, in __init__
    self.model = ctranslate2.models.Whisper(
TypeError: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
2025-09-08 17:24:12 | [32mINFO[0m | __main__ | shutdown:183 | System shutdown initiated
2025-09-08 17:24:12 | [32mINFO[0m | ui.status_monitor | stop_monitoring:101 | Stopping status monitoring...
2025-09-08 17:24:13 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 13023MB
2025-09-08 17:24:13 | [32mINFO[0m | ui.status_monitor | stop_monitoring:107 | Status monitoring stopped
2025-09-08 17:24:13 | [32mINFO[0m | utils.performance | stop:113 | Stopping performance monitoring...
2025-09-08 17:24:17 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:17 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:17 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:17 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:17 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:17 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:17 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:17 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:17 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:17 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs

2025-09-08 17:24:17 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:17 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:17 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:17 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:17 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:17 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:17 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:17 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:17 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:17 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 17:24:17 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 17:24:17 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:17 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:17 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:17 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 17:24:18 | [32mINFO[0m | utils.performance | stop:119 | Performance monitoring stopped
2025-09-08 17:24:18 | [32mINFO[0m | models.model_manager | shutdown:295 | Shutting down ModelManager...
2025-09-08 17:24:18 | [32mINFO[0m | models.model_manager | optimize_memory:262 | GPU memory optimized
2025-09-08 17:24:18 | [32mINFO[0m | models.model_manager | optimize_memory:266 | Memory after optimization: 0.00GB
2025-09-08 17:24:18 | [32mINFO[0m | models.model_manager | shutdown:311 | ModelManager shutdown complete
2025-09-08 17:24:18 | [32mINFO[0m | __main__ | shutdown:212 | System shutdown completed
2025-09-08 17:24:18 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 17:24:18 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:18 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:18 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 17:24:18 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 17:24:18 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 17:24:18 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:18 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:18 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 17:24:18 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 17:24:18 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 17:24:18 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:18 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:18 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 17:24:18 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:18 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:18 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 17:24:18 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 17:24:18 | [32mINFO[0m | __mp_main__ | setup_logger:139 | Logger '__mp_main__' initialized
2025-09-08 17:24:18 | [32mINFO[0m | __mp_main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:18 | [32mINFO[0m | __mp_main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 17:24:18 | [32mINFO[0m | __mp_main__ | setup_logger:139 | Logger '__mp_main__' initialized
2025-09-08 17:24:18 | [32mINFO[0m | __mp_main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 17:24:18 | [32mINFO[0m | __mp_main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:14 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 18:21:14 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:14 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:14 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:21:14 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:21:14 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:14 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:14 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:14 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:14 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:21:14 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:21:14 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:14 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:14 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:14 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:14 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:21:14 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:21:14 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:14 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:14 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:14 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:14 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:21:14 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:21:14 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:14 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:14 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:14 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:14 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:21:14 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:21:14 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:14 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:14 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:14 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:14 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:21:17 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 18:21:17 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:17 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:17 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 18:21:17 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 18:21:17 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 18:21:17 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:17 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:17 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:21:17 | [32mINFO[0m | __main__ | setup_logger:139 | Logger '__main__' initialized
2025-09-08 18:21:17 | [32mINFO[0m | __main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:17 | [32mINFO[0m | __main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:17 | [32mINFO[0m | __main__ | __init__:53 | VoiceToTextSystem initialized
2025-09-08 18:21:17 | [32mINFO[0m | __main__ | initialize:58 | Initializing Real-time Voice-to-Text System...
2025-09-08 18:21:17 | [32mINFO[0m | utils.performance | setup_logger:139 | Logger 'utils.performance' initialized
2025-09-08 18:21:17 | [32mINFO[0m | utils.performance | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:17 | [32mINFO[0m | utils.performance | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:17 | [32mINFO[0m | utils.performance | __init__:90 | PerformanceMonitor initialized
2025-09-08 18:21:17 | [32mINFO[0m | utils.performance | start:98 | Starting performance monitoring...
2025-09-08 18:21:17 | [32mINFO[0m | utils.performance | start:106 | Performance monitoring started
2025-09-08 18:21:17 | [32mINFO[0m | ui.status_monitor | setup_logger:139 | Logger 'ui.status_monitor' initialized
2025-09-08 18:21:17 | [32mINFO[0m | ui.status_monitor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:17 | [32mINFO[0m | ui.status_monitor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:17 | [32mINFO[0m | ui.status_monitor | __init__:78 | StatusMonitor initialized
2025-09-08 18:21:17 | [32mINFO[0m | ui.status_monitor | start_monitoring:86 | Starting status monitoring...
2025-09-08 18:21:17 | [32mINFO[0m | ui.status_monitor | start_monitoring:94 | Status monitoring started
2025-09-08 18:21:17 | [32mINFO[0m | utils.file_handler | setup_logger:139 | Logger 'utils.file_handler' initialized
2025-09-08 18:21:17 | [32mINFO[0m | utils.file_handler | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:17 | [32mINFO[0m | utils.file_handler | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:17 | DEBUG    | utils.file_handler | _ensure_directories:58 | Data directories ensured
2025-09-08 18:21:17 | [32mINFO[0m | utils.file_handler | __init__:44 | FileHandler initialized
2025-09-08 18:21:17 | [32mINFO[0m | ui.console_display | setup_logger:139 | Logger 'ui.console_display' initialized
2025-09-08 18:21:17 | [32mINFO[0m | ui.console_display | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:17 | [32mINFO[0m | ui.console_display | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:17 | [32mINFO[0m | ui.colors | setup_logger:139 | Logger 'ui.colors' initialized
2025-09-08 18:21:17 | [32mINFO[0m | ui.colors | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:17 | [32mINFO[0m | ui.colors | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:17 | [32mINFO[0m | ui.colors | __init__:120 | ColorManager initialized with theme: default
2025-09-08 18:21:17 | [32mINFO[0m | ui.console_display | __init__:67 | ConsoleDisplay initialized
2025-09-08 18:21:17 | [32mINFO[0m | models.model_manager | setup_logger:139 | Logger 'models.model_manager' initialized
2025-09-08 18:21:17 | [32mINFO[0m | models.model_manager | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:17 | [32mINFO[0m | models.model_manager | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:17 | [32mINFO[0m | models.model_manager | __init__:46 | ModelManager initialized
2025-09-08 18:21:17 | [32mINFO[0m | models.model_manager | initialize:51 | Starting model initialization...
2025-09-08 18:21:17 | [32mINFO[0m | models.model_manager | _check_gpu_status:76 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 18:21:17 | [32mINFO[0m | models.model_manager | _check_gpu_status:77 | Total VRAM: 6.0GB
2025-09-08 18:21:17 | [32mINFO[0m | models.model_manager | _check_gpu_status:78 | CUDA Version: 11.6
2025-09-08 18:21:17 | [32mINFO[0m | models.model_manager | _check_gpu_status:84 | Current VRAM usage: 0.00GB allocated, 0.00GB reserved
2025-09-08 18:21:17 | [32mINFO[0m | models.model_manager | _load_whisper_models:94 | Loading Whisper models...
2025-09-08 18:21:17 | [32mINFO[0m | models.model_manager | _load_whisper_models:97 | Loading primary Whisper model: medium.en
2025-09-08 18:21:18 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11880MB
2025-09-08 18:21:23 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11339MB
2025-09-08 18:21:28 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14190MB
2025-09-08 18:21:32 | [32mINFO[0m | models.model_manager | _load_whisper_models:112 | Primary Whisper model loaded in 14.99s
2025-09-08 18:21:32 | [32mINFO[0m | models.model_manager | _load_whisper_models:120 | Primary model VRAM usage: 1.42GB
2025-09-08 18:21:32 | [32mINFO[0m | models.model_manager | _load_whisper_models:123 | Loading realtime Whisper model: tiny.en
2025-09-08 18:21:33 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12099MB
2025-09-08 18:21:33 | [32mINFO[0m | models.model_manager | _load_whisper_models:134 | Realtime Whisper model loaded in 0.73s
2025-09-08 18:21:33 | [32mINFO[0m | models.model_manager | _load_whisper_models:142 | Realtime model VRAM usage: 0.07GB
2025-09-08 18:21:33 | [32mINFO[0m | models.model_manager | _load_whisper_models:143 | Total VRAM usage: 1.50GB
2025-09-08 18:21:33 | [32mINFO[0m | models.model_manager | _load_emotion_model:152 | Loading emotion detection model...
2025-09-08 18:21:33 | [32mINFO[0m | models.model_manager | _load_emotion_model:164 | Loading transformer-based emotion classifier...
2025-09-08 18:21:35 | [32mINFO[0m | models.model_manager | _load_emotion_model:178 | Emotion model loaded in 1.65s
2025-09-08 18:21:35 | [32mINFO[0m | models.model_manager | _load_emotion_model:186 | Emotion model VRAM usage: 0.15GB
2025-09-08 18:21:35 | [32mINFO[0m | models.model_manager | _load_emotion_model:187 | Total VRAM usage: 1.65GB
2025-09-08 18:21:35 | [32mINFO[0m | models.model_manager | _verify_models:207 | Optional model 'emotion_detector': ✅ Loaded
2025-09-08 18:21:35 | [32mINFO[0m | models.model_manager | _verify_models:211 | Final VRAM usage: 1.65GB / 4.5GB limit
2025-09-08 18:21:35 | [32mINFO[0m | models.model_manager | initialize:63 | All models initialized successfully
2025-09-08 18:21:35 | [32mINFO[0m | core.voice_processor | setup_logger:139 | Logger 'core.voice_processor' initialized
2025-09-08 18:21:35 | [32mINFO[0m | core.voice_processor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:35 | [32mINFO[0m | core.voice_processor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:35 | [32mINFO[0m | core.audio_utils | setup_logger:139 | Logger 'core.audio_utils' initialized
2025-09-08 18:21:35 | [32mINFO[0m | core.audio_utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:35 | [32mINFO[0m | core.audio_utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:35 | [32mINFO[0m | core.audio_utils | __init__:28 | AudioUtils initialized
2025-09-08 18:21:35 | [32mINFO[0m | core.emotion_detector | setup_logger:139 | Logger 'core.emotion_detector' initialized
2025-09-08 18:21:35 | [32mINFO[0m | core.emotion_detector | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:35 | [32mINFO[0m | core.emotion_detector | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:35 | [32mINFO[0m | core.emotion_detector | __init__:75 | EmotionDetector initialized
2025-09-08 18:21:35 | [32mINFO[0m | core.text_formatter | setup_logger:139 | Logger 'core.text_formatter' initialized
2025-09-08 18:21:35 | [32mINFO[0m | core.text_formatter | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:35 | [32mINFO[0m | core.text_formatter | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:35 | [32mINFO[0m | core.text_formatter | __init__:62 | TextFormatter initialized
2025-09-08 18:21:35 | [32mINFO[0m | core.voice_processor | __init__:77 | VoiceProcessor initialized
2025-09-08 18:21:35 | [32mINFO[0m | core.voice_processor | initialize:82 | Initializing voice processor...
2025-09-08 18:21:35 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:109 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 18:21:35 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:110 | Total VRAM: 6.0GB
2025-09-08 18:21:35 | [33mWARNING[0m | core.voice_processor | _check_gpu_resources:114 | Less than 6GB VRAM available. Using conservative settings.
2025-09-08 18:21:35 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:120 | Initial GPU memory usage: 1.65GB
2025-09-08 18:21:35 | [32mINFO[0m | core.voice_processor | _initialize_recorder:128 | Configuring RealtimeSTT recorder...
2025-09-08 18:21:35 | [31mERROR[0m | core.voice_processor | _initialize_recorder:184 | Failed to initialize recorder: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 672, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 145, in __init__
    self.model = ctranslate2.models.Whisper(
TypeError: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
2025-09-08 18:21:35 | [31mERROR[0m | core.voice_processor | initialize:100 | Failed to initialize voice processor: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 88, in initialize
    await self._initialize_recorder()
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 672, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 145, in __init__
    self.model = ctranslate2.models.Whisper(
TypeError: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
2025-09-08 18:21:35 | [32mINFO[0m | __main__ | shutdown:183 | System shutdown initiated
2025-09-08 18:21:35 | [32mINFO[0m | ui.status_monitor | stop_monitoring:101 | Stopping status monitoring...
2025-09-08 18:21:35 | [32mINFO[0m | ui.status_monitor | stop_monitoring:107 | Status monitoring stopped
2025-09-08 18:21:35 | [32mINFO[0m | utils.performance | stop:113 | Stopping performance monitoring...
2025-09-08 18:21:38 | [32mINFO[0m | utils.performance | stop:119 | Performance monitoring stopped
2025-09-08 18:21:38 | [32mINFO[0m | models.model_manager | shutdown:295 | Shutting down ModelManager...
2025-09-08 18:21:38 | [32mINFO[0m | models.model_manager | optimize_memory:262 | GPU memory optimized
2025-09-08 18:21:38 | [32mINFO[0m | models.model_manager | optimize_memory:266 | Memory after optimization: 0.00GB
2025-09-08 18:21:38 | [32mINFO[0m | models.model_manager | shutdown:311 | ModelManager shutdown complete
2025-09-08 18:21:38 | [32mINFO[0m | __main__ | shutdown:212 | System shutdown completed
2025-09-08 18:21:39 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:21:39 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:21:39 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:39 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:39 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:21:41 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 18:21:41 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:41 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:41 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 18:21:41 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 18:21:41 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 18:21:41 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:41 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:41 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 18:21:41 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:41 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:41 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 18:21:41 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 18:21:41 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:21:41 | [32mINFO[0m | __mp_main__ | setup_logger:139 | Logger '__mp_main__' initialized
2025-09-08 18:21:41 | [32mINFO[0m | __mp_main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:41 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 18:21:41 | [32mINFO[0m | __mp_main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:41 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:41 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:21:41 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:21:41 | [32mINFO[0m | __mp_main__ | setup_logger:139 | Logger '__mp_main__' initialized
2025-09-08 18:21:41 | [32mINFO[0m | __mp_main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:21:41 | [32mINFO[0m | __mp_main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:11 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 18:22:11 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:11 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:11 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:22:11 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:22:11 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:11 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:11 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:11 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:11 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:22:11 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:22:11 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:11 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:11 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:11 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:11 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:22:11 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:22:11 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:11 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:11 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:11 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:11 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:22:11 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:22:11 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:11 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:11 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:11 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:11 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:22:11 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:22:11 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:11 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:11 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:11 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:11 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:22:12 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 18:22:12 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:12 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:12 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 18:22:12 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 18:22:12 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 18:22:12 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:12 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:12 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:22:12 | [32mINFO[0m | __main__ | setup_logger:139 | Logger '__main__' initialized
2025-09-08 18:22:12 | [32mINFO[0m | __main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:12 | [32mINFO[0m | __main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:12 | [32mINFO[0m | __main__ | __init__:53 | VoiceToTextSystem initialized
2025-09-08 18:22:12 | [32mINFO[0m | __main__ | initialize:58 | Initializing Real-time Voice-to-Text System...
2025-09-08 18:22:12 | [32mINFO[0m | utils.performance | setup_logger:139 | Logger 'utils.performance' initialized
2025-09-08 18:22:12 | [32mINFO[0m | utils.performance | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:12 | [32mINFO[0m | utils.performance | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:12 | [32mINFO[0m | utils.performance | __init__:90 | PerformanceMonitor initialized
2025-09-08 18:22:12 | [32mINFO[0m | utils.performance | start:98 | Starting performance monitoring...
2025-09-08 18:22:12 | [32mINFO[0m | utils.performance | start:106 | Performance monitoring started
2025-09-08 18:22:12 | [32mINFO[0m | ui.status_monitor | setup_logger:139 | Logger 'ui.status_monitor' initialized
2025-09-08 18:22:12 | [32mINFO[0m | ui.status_monitor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:12 | [32mINFO[0m | ui.status_monitor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:12 | [32mINFO[0m | ui.status_monitor | __init__:78 | StatusMonitor initialized
2025-09-08 18:22:12 | [32mINFO[0m | ui.status_monitor | start_monitoring:86 | Starting status monitoring...
2025-09-08 18:22:12 | [32mINFO[0m | ui.status_monitor | start_monitoring:94 | Status monitoring started
2025-09-08 18:22:12 | [32mINFO[0m | utils.file_handler | setup_logger:139 | Logger 'utils.file_handler' initialized
2025-09-08 18:22:12 | [32mINFO[0m | utils.file_handler | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:12 | [32mINFO[0m | utils.file_handler | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:12 | DEBUG    | utils.file_handler | _ensure_directories:58 | Data directories ensured
2025-09-08 18:22:12 | [32mINFO[0m | utils.file_handler | __init__:44 | FileHandler initialized
2025-09-08 18:22:12 | [32mINFO[0m | ui.console_display | setup_logger:139 | Logger 'ui.console_display' initialized
2025-09-08 18:22:12 | [32mINFO[0m | ui.console_display | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:12 | [32mINFO[0m | ui.console_display | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:12 | [32mINFO[0m | ui.colors | setup_logger:139 | Logger 'ui.colors' initialized
2025-09-08 18:22:12 | [32mINFO[0m | ui.colors | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:12 | [32mINFO[0m | ui.colors | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:12 | [32mINFO[0m | ui.colors | __init__:120 | ColorManager initialized with theme: default
2025-09-08 18:22:12 | [32mINFO[0m | ui.console_display | __init__:67 | ConsoleDisplay initialized
2025-09-08 18:22:12 | [32mINFO[0m | models.model_manager | setup_logger:139 | Logger 'models.model_manager' initialized
2025-09-08 18:22:12 | [32mINFO[0m | models.model_manager | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:12 | [32mINFO[0m | models.model_manager | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:12 | [32mINFO[0m | models.model_manager | __init__:46 | ModelManager initialized
2025-09-08 18:22:12 | [32mINFO[0m | models.model_manager | initialize:51 | Starting model initialization...
2025-09-08 18:22:12 | [32mINFO[0m | models.model_manager | _check_gpu_status:76 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 18:22:12 | [32mINFO[0m | models.model_manager | _check_gpu_status:77 | Total VRAM: 6.0GB
2025-09-08 18:22:12 | [32mINFO[0m | models.model_manager | _check_gpu_status:78 | CUDA Version: 11.6
2025-09-08 18:22:12 | [32mINFO[0m | models.model_manager | _check_gpu_status:84 | Current VRAM usage: 0.00GB allocated, 0.00GB reserved
2025-09-08 18:22:12 | [32mINFO[0m | models.model_manager | _load_whisper_models:94 | Loading Whisper models...
2025-09-08 18:22:12 | [32mINFO[0m | models.model_manager | _load_whisper_models:97 | Loading primary Whisper model: medium.en
2025-09-08 18:22:12 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 13753MB
2025-09-08 18:22:17 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12652MB
2025-09-08 18:22:22 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15206MB
2025-09-08 18:22:27 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15646MB
2025-09-08 18:22:32 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14882MB
2025-09-08 18:22:33 | [32mINFO[0m | models.model_manager | _load_whisper_models:112 | Primary Whisper model loaded in 21.54s
2025-09-08 18:22:33 | [32mINFO[0m | models.model_manager | _load_whisper_models:120 | Primary model VRAM usage: 1.42GB
2025-09-08 18:22:33 | [32mINFO[0m | models.model_manager | _load_whisper_models:123 | Loading realtime Whisper model: tiny.en
2025-09-08 18:22:34 | [32mINFO[0m | models.model_manager | _load_whisper_models:134 | Realtime Whisper model loaded in 0.71s
2025-09-08 18:22:34 | [32mINFO[0m | models.model_manager | _load_whisper_models:142 | Realtime model VRAM usage: 0.07GB
2025-09-08 18:22:34 | [32mINFO[0m | models.model_manager | _load_whisper_models:143 | Total VRAM usage: 1.50GB
2025-09-08 18:22:34 | [32mINFO[0m | models.model_manager | _load_emotion_model:152 | Loading emotion detection model...
2025-09-08 18:22:34 | [32mINFO[0m | models.model_manager | _load_emotion_model:164 | Loading transformer-based emotion classifier...
2025-09-08 18:22:35 | [32mINFO[0m | models.model_manager | _load_emotion_model:178 | Emotion model loaded in 1.57s
2025-09-08 18:22:35 | [32mINFO[0m | models.model_manager | _load_emotion_model:186 | Emotion model VRAM usage: 0.15GB
2025-09-08 18:22:35 | [32mINFO[0m | models.model_manager | _load_emotion_model:187 | Total VRAM usage: 1.65GB
2025-09-08 18:22:35 | [32mINFO[0m | models.model_manager | _verify_models:207 | Optional model 'emotion_detector': ✅ Loaded
2025-09-08 18:22:35 | [32mINFO[0m | models.model_manager | _verify_models:211 | Final VRAM usage: 1.65GB / 4.5GB limit
2025-09-08 18:22:35 | [32mINFO[0m | models.model_manager | initialize:63 | All models initialized successfully
2025-09-08 18:22:35 | [32mINFO[0m | core.voice_processor | setup_logger:139 | Logger 'core.voice_processor' initialized
2025-09-08 18:22:35 | [32mINFO[0m | core.voice_processor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:35 | [32mINFO[0m | core.voice_processor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:35 | [32mINFO[0m | core.audio_utils | setup_logger:139 | Logger 'core.audio_utils' initialized
2025-09-08 18:22:35 | [32mINFO[0m | core.audio_utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:35 | [32mINFO[0m | core.audio_utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:35 | [32mINFO[0m | core.audio_utils | __init__:28 | AudioUtils initialized
2025-09-08 18:22:35 | [32mINFO[0m | core.emotion_detector | setup_logger:139 | Logger 'core.emotion_detector' initialized
2025-09-08 18:22:35 | [32mINFO[0m | core.emotion_detector | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:35 | [32mINFO[0m | core.emotion_detector | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:35 | [32mINFO[0m | core.emotion_detector | __init__:75 | EmotionDetector initialized
2025-09-08 18:22:35 | [32mINFO[0m | core.text_formatter | setup_logger:139 | Logger 'core.text_formatter' initialized
2025-09-08 18:22:35 | [32mINFO[0m | core.text_formatter | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:35 | [32mINFO[0m | core.text_formatter | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:35 | [32mINFO[0m | core.text_formatter | __init__:62 | TextFormatter initialized
2025-09-08 18:22:35 | [32mINFO[0m | core.voice_processor | __init__:77 | VoiceProcessor initialized
2025-09-08 18:22:35 | [32mINFO[0m | core.voice_processor | initialize:82 | Initializing voice processor...
2025-09-08 18:22:35 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:109 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 18:22:35 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:110 | Total VRAM: 6.0GB
2025-09-08 18:22:35 | [33mWARNING[0m | core.voice_processor | _check_gpu_resources:114 | Less than 6GB VRAM available. Using conservative settings.
2025-09-08 18:22:35 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:120 | Initial GPU memory usage: 1.65GB
2025-09-08 18:22:35 | [32mINFO[0m | core.voice_processor | _initialize_recorder:128 | Configuring RealtimeSTT recorder...
2025-09-08 18:22:37 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 13303MB
2025-09-08 18:22:40 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:40 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:40 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:40 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:40 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:40 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:40 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:40 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:40 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:40 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:40 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:40 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:40 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:40 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:40 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:40 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:40 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:40 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:40 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:40 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:22:40 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:40 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:40 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:22:40 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:40 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:22:41 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 18:22:41 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 18:22:41 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:41 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:41 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:41 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:41 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 18:22:41 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 18:22:41 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 18:22:41 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 18:22:41 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 18:22:41 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 18:22:41 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:41 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:41 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:41 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:41 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:22:41 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:22:41 | [32mINFO[0m | __mp_main__ | setup_logger:139 | Logger '__mp_main__' initialized
2025-09-08 18:22:41 | [32mINFO[0m | __mp_main__ | setup_logger:139 | Logger '__mp_main__' initialized
2025-09-08 18:22:41 | [32mINFO[0m | __mp_main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:41 | [32mINFO[0m | __mp_main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:22:41 | [32mINFO[0m | __mp_main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:41 | [32mINFO[0m | __mp_main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:22:42 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14528MB
2025-09-08 18:22:43 | [32mINFO[0m | core.voice_processor | _initialize_recorder:180 | RealtimeSTT recorder configured successfully
2025-09-08 18:22:43 | [32mINFO[0m | core.emotion_detector | initialize:80 | Initializing emotion detection models...
2025-09-08 18:22:43 | [32mINFO[0m | core.emotion_detector | _initialize_text_classifier:98 | Loading text emotion classifier...
2025-09-08 18:22:44 | [32mINFO[0m | core.emotion_detector | _initialize_text_classifier:111 | Text emotion classifier loaded successfully
2025-09-08 18:22:44 | [32mINFO[0m | core.emotion_detector | _initialize_audio_classifier:176 | Custom audio model not found, creating feature-based classifier...
2025-09-08 18:22:44 | [32mINFO[0m | core.emotion_detector | _create_audio_feature_classifier:185 | Creating feature-based audio emotion classifier...
2025-09-08 18:22:44 | [32mINFO[0m | core.emotion_detector | initialize:88 | Emotion detection models initialized successfully
2025-09-08 18:22:44 | [32mINFO[0m | core.text_formatter | initialize:66 | TextFormatter ready for processing
2025-09-08 18:22:44 | [32mINFO[0m | core.voice_processor | initialize:96 | Voice processor initialization completed
2025-09-08 18:22:44 | DEBUG    | ui.status_monitor | add_status_callback:347 | Status callback added
2025-09-08 18:22:44 | DEBUG    | __main__ | _setup_callbacks:129 | System callbacks configured
2025-09-08 18:22:44 | [32mINFO[0m | __main__ | initialize:108 | System initialization completed
2025-09-08 18:22:44 | [31mERROR[0m | __main__ | main:294 | System error: 'VoiceToTextSystem' object has no attribute 'start_console_mode'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\main.py", line 283, in main
    await system.start_console_mode()
AttributeError: 'VoiceToTextSystem' object has no attribute 'start_console_mode'
2025-09-08 18:22:44 | [32mINFO[0m | __main__ | shutdown:183 | System shutdown initiated
2025-09-08 18:22:44 | [32mINFO[0m | ui.status_monitor | stop_monitoring:101 | Stopping status monitoring...
2025-09-08 18:22:44 | [32mINFO[0m | ui.status_monitor | stop_monitoring:107 | Status monitoring stopped
2025-09-08 18:22:44 | [32mINFO[0m | utils.performance | stop:113 | Stopping performance monitoring...
2025-09-08 18:22:48 | [32mINFO[0m | utils.performance | stop:119 | Performance monitoring stopped
2025-09-08 18:22:48 | [32mINFO[0m | models.model_manager | shutdown:295 | Shutting down ModelManager...
2025-09-08 18:22:48 | [32mINFO[0m | models.model_manager | optimize_memory:262 | GPU memory optimized
2025-09-08 18:22:48 | [32mINFO[0m | models.model_manager | optimize_memory:266 | Memory after optimization: 0.15GB
2025-09-08 18:22:48 | [32mINFO[0m | models.model_manager | shutdown:311 | ModelManager shutdown complete
2025-09-08 18:22:48 | [32mINFO[0m | __main__ | shutdown:212 | System shutdown completed
2025-09-08 18:29:38 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 18:29:38 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:38 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:38 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:29:38 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:29:38 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:38 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:38 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:38 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:38 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:29:38 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:29:38 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:38 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:38 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:38 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:38 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:29:38 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:29:38 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:38 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:38 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:38 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:38 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:29:38 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:29:38 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:38 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:38 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:38 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:38 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:29:38 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:29:38 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:38 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:38 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:38 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:38 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:29:39 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 18:29:39 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:39 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:39 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 18:29:39 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 18:29:39 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 18:29:39 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:39 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:39 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:29:39 | [32mINFO[0m | __main__ | setup_logger:139 | Logger '__main__' initialized
2025-09-08 18:29:39 | [32mINFO[0m | __main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:39 | [32mINFO[0m | __main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:39 | [32mINFO[0m | __main__ | __init__:53 | VoiceToTextSystem initialized
2025-09-08 18:29:39 | [32mINFO[0m | __main__ | initialize:58 | Initializing Real-time Voice-to-Text System...
2025-09-08 18:29:39 | [32mINFO[0m | utils.performance | setup_logger:139 | Logger 'utils.performance' initialized
2025-09-08 18:29:39 | [32mINFO[0m | utils.performance | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:39 | [32mINFO[0m | utils.performance | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:39 | [32mINFO[0m | utils.performance | __init__:90 | PerformanceMonitor initialized
2025-09-08 18:29:39 | [32mINFO[0m | utils.performance | start:98 | Starting performance monitoring...
2025-09-08 18:29:39 | [32mINFO[0m | utils.performance | start:106 | Performance monitoring started
2025-09-08 18:29:39 | [32mINFO[0m | ui.status_monitor | setup_logger:139 | Logger 'ui.status_monitor' initialized
2025-09-08 18:29:39 | [32mINFO[0m | ui.status_monitor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:39 | [32mINFO[0m | ui.status_monitor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:39 | [32mINFO[0m | ui.status_monitor | __init__:78 | StatusMonitor initialized
2025-09-08 18:29:39 | [32mINFO[0m | ui.status_monitor | start_monitoring:86 | Starting status monitoring...
2025-09-08 18:29:39 | [32mINFO[0m | ui.status_monitor | start_monitoring:94 | Status monitoring started
2025-09-08 18:29:39 | [32mINFO[0m | utils.file_handler | setup_logger:139 | Logger 'utils.file_handler' initialized
2025-09-08 18:29:39 | [32mINFO[0m | utils.file_handler | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:39 | [32mINFO[0m | utils.file_handler | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:39 | DEBUG    | utils.file_handler | _ensure_directories:58 | Data directories ensured
2025-09-08 18:29:39 | [32mINFO[0m | utils.file_handler | __init__:44 | FileHandler initialized
2025-09-08 18:29:39 | [32mINFO[0m | ui.console_display | setup_logger:139 | Logger 'ui.console_display' initialized
2025-09-08 18:29:39 | [32mINFO[0m | ui.console_display | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:39 | [32mINFO[0m | ui.console_display | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:39 | [32mINFO[0m | ui.colors | setup_logger:139 | Logger 'ui.colors' initialized
2025-09-08 18:29:39 | [32mINFO[0m | ui.colors | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:39 | [32mINFO[0m | ui.colors | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:39 | [32mINFO[0m | ui.colors | __init__:120 | ColorManager initialized with theme: default
2025-09-08 18:29:39 | [32mINFO[0m | ui.console_display | __init__:67 | ConsoleDisplay initialized
2025-09-08 18:29:39 | [32mINFO[0m | models.model_manager | setup_logger:139 | Logger 'models.model_manager' initialized
2025-09-08 18:29:39 | [32mINFO[0m | models.model_manager | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:39 | [32mINFO[0m | models.model_manager | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:39 | [32mINFO[0m | models.model_manager | __init__:46 | ModelManager initialized
2025-09-08 18:29:39 | [32mINFO[0m | models.model_manager | initialize:51 | Starting model initialization...
2025-09-08 18:29:39 | [32mINFO[0m | models.model_manager | _check_gpu_status:76 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 18:29:39 | [32mINFO[0m | models.model_manager | _check_gpu_status:77 | Total VRAM: 6.0GB
2025-09-08 18:29:39 | [32mINFO[0m | models.model_manager | _check_gpu_status:78 | CUDA Version: 11.6
2025-09-08 18:29:39 | [32mINFO[0m | models.model_manager | _check_gpu_status:84 | Current VRAM usage: 0.00GB allocated, 0.00GB reserved
2025-09-08 18:29:39 | [32mINFO[0m | models.model_manager | _load_whisper_models:94 | Loading Whisper models...
2025-09-08 18:29:39 | [32mINFO[0m | models.model_manager | _load_whisper_models:97 | Loading primary Whisper model: medium.en
2025-09-08 18:29:39 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 13073MB
2025-09-08 18:29:44 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11794MB
2025-09-08 18:29:49 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14750MB
2025-09-08 18:29:54 | [32mINFO[0m | models.model_manager | _load_whisper_models:112 | Primary Whisper model loaded in 15.03s
2025-09-08 18:29:54 | [32mINFO[0m | models.model_manager | _load_whisper_models:120 | Primary model VRAM usage: 1.42GB
2025-09-08 18:29:54 | [32mINFO[0m | models.model_manager | _load_whisper_models:123 | Loading realtime Whisper model: tiny.en
2025-09-08 18:29:54 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12751MB
2025-09-08 18:29:54 | [32mINFO[0m | models.model_manager | _load_whisper_models:134 | Realtime Whisper model loaded in 0.71s
2025-09-08 18:29:54 | [32mINFO[0m | models.model_manager | _load_whisper_models:142 | Realtime model VRAM usage: 0.07GB
2025-09-08 18:29:54 | [32mINFO[0m | models.model_manager | _load_whisper_models:143 | Total VRAM usage: 1.50GB
2025-09-08 18:29:54 | [32mINFO[0m | models.model_manager | _load_emotion_model:152 | Loading emotion detection model...
2025-09-08 18:29:54 | [32mINFO[0m | models.model_manager | _load_emotion_model:164 | Loading transformer-based emotion classifier...
2025-09-08 18:29:56 | [32mINFO[0m | models.model_manager | _load_emotion_model:178 | Emotion model loaded in 1.71s
2025-09-08 18:29:56 | [32mINFO[0m | models.model_manager | _load_emotion_model:186 | Emotion model VRAM usage: 0.15GB
2025-09-08 18:29:56 | [32mINFO[0m | models.model_manager | _load_emotion_model:187 | Total VRAM usage: 1.65GB
2025-09-08 18:29:56 | [32mINFO[0m | models.model_manager | _verify_models:207 | Optional model 'emotion_detector': ✅ Loaded
2025-09-08 18:29:56 | [32mINFO[0m | models.model_manager | _verify_models:211 | Final VRAM usage: 1.65GB / 4.5GB limit
2025-09-08 18:29:56 | [32mINFO[0m | models.model_manager | initialize:63 | All models initialized successfully
2025-09-08 18:29:56 | [32mINFO[0m | core.voice_processor | setup_logger:139 | Logger 'core.voice_processor' initialized
2025-09-08 18:29:56 | [32mINFO[0m | core.voice_processor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:56 | [32mINFO[0m | core.voice_processor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:56 | [32mINFO[0m | core.audio_utils | setup_logger:139 | Logger 'core.audio_utils' initialized
2025-09-08 18:29:56 | [32mINFO[0m | core.audio_utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:56 | [32mINFO[0m | core.audio_utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:56 | [32mINFO[0m | core.audio_utils | __init__:28 | AudioUtils initialized
2025-09-08 18:29:56 | [32mINFO[0m | core.emotion_detector | setup_logger:139 | Logger 'core.emotion_detector' initialized
2025-09-08 18:29:56 | [32mINFO[0m | core.emotion_detector | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:56 | [32mINFO[0m | core.emotion_detector | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:56 | [32mINFO[0m | core.emotion_detector | __init__:75 | EmotionDetector initialized
2025-09-08 18:29:56 | [32mINFO[0m | core.text_formatter | setup_logger:139 | Logger 'core.text_formatter' initialized
2025-09-08 18:29:56 | [32mINFO[0m | core.text_formatter | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:29:56 | [32mINFO[0m | core.text_formatter | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:29:56 | [32mINFO[0m | core.text_formatter | __init__:62 | TextFormatter initialized
2025-09-08 18:29:56 | [32mINFO[0m | core.voice_processor | __init__:77 | VoiceProcessor initialized
2025-09-08 18:29:56 | [32mINFO[0m | core.voice_processor | initialize:82 | Initializing voice processor...
2025-09-08 18:29:56 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:109 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 18:29:56 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:110 | Total VRAM: 6.0GB
2025-09-08 18:29:56 | [33mWARNING[0m | core.voice_processor | _check_gpu_resources:114 | Less than 6GB VRAM available. Using conservative settings.
2025-09-08 18:29:56 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:120 | Initial GPU memory usage: 1.65GB
2025-09-08 18:29:56 | [32mINFO[0m | core.voice_processor | _initialize_recorder:128 | Configuring RealtimeSTT recorder...
2025-09-08 18:29:59 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 13176MB
2025-09-08 18:30:01 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:30:01 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:01 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:30:01 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:01 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:30:02 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 18:30:02 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 18:30:02 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:02 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:02 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:02 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:02 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 18:30:02 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 18:30:02 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 18:30:02 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 18:30:02 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 18:30:02 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 18:30:02 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:02 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:02 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:02 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:02 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:30:02 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:30:02 | [32mINFO[0m | __mp_main__ | setup_logger:139 | Logger '__mp_main__' initialized
2025-09-08 18:30:02 | [32mINFO[0m | __mp_main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:02 | [32mINFO[0m | __mp_main__ | setup_logger:139 | Logger '__mp_main__' initialized
2025-09-08 18:30:02 | [32mINFO[0m | __mp_main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:02 | [32mINFO[0m | __mp_main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:30:02 | [32mINFO[0m | __mp_main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:30:03 | [32mINFO[0m | core.voice_processor | _initialize_recorder:180 | RealtimeSTT recorder configured successfully
2025-09-08 18:30:03 | [32mINFO[0m | core.emotion_detector | initialize:80 | Initializing emotion detection models...
2025-09-08 18:30:03 | [32mINFO[0m | core.emotion_detector | _initialize_text_classifier:98 | Loading text emotion classifier...
2025-09-08 18:30:04 | [32mINFO[0m | core.emotion_detector | _initialize_text_classifier:111 | Text emotion classifier loaded successfully
2025-09-08 18:30:04 | [32mINFO[0m | core.emotion_detector | _initialize_audio_classifier:176 | Custom audio model not found, creating feature-based classifier...
2025-09-08 18:30:04 | [32mINFO[0m | core.emotion_detector | _create_audio_feature_classifier:185 | Creating feature-based audio emotion classifier...
2025-09-08 18:30:04 | [32mINFO[0m | core.emotion_detector | initialize:88 | Emotion detection models initialized successfully
2025-09-08 18:30:04 | [32mINFO[0m | core.text_formatter | initialize:66 | TextFormatter ready for processing
2025-09-08 18:30:04 | [32mINFO[0m | core.voice_processor | initialize:96 | Voice processor initialization completed
2025-09-08 18:30:04 | DEBUG    | ui.status_monitor | add_status_callback:347 | Status callback added
2025-09-08 18:30:04 | DEBUG    | __main__ | _setup_callbacks:129 | System callbacks configured
2025-09-08 18:30:04 | [32mINFO[0m | __main__ | initialize:108 | System initialization completed
2025-09-08 18:30:04 | [31mERROR[0m | __main__ | start_console_mode:273 | Console mode error: 'VoiceProcessor' object has no attribute 'start_listening'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\main.py", line 259, in start_console_mode
    await self.voice_processor.start_listening()
AttributeError: 'VoiceProcessor' object has no attribute 'start_listening'
2025-09-08 18:30:04 | [32mINFO[0m | __main__ | shutdown:183 | System shutdown initiated
2025-09-08 18:30:04 | [32mINFO[0m | ui.status_monitor | stop_monitoring:101 | Stopping status monitoring...
2025-09-08 18:30:05 | [32mINFO[0m | ui.status_monitor | stop_monitoring:107 | Status monitoring stopped
2025-09-08 18:30:05 | [32mINFO[0m | utils.performance | stop:113 | Stopping performance monitoring...
2025-09-08 18:30:05 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14333MB
2025-09-08 18:30:10 | [32mINFO[0m | utils.performance | stop:119 | Performance monitoring stopped
2025-09-08 18:30:10 | [32mINFO[0m | models.model_manager | shutdown:295 | Shutting down ModelManager...
2025-09-08 18:30:10 | [32mINFO[0m | models.model_manager | optimize_memory:262 | GPU memory optimized
2025-09-08 18:30:10 | [32mINFO[0m | models.model_manager | optimize_memory:266 | Memory after optimization: 0.15GB
2025-09-08 18:30:10 | [32mINFO[0m | models.model_manager | shutdown:311 | ModelManager shutdown complete
2025-09-08 18:30:10 | [32mINFO[0m | __main__ | shutdown:212 | System shutdown completed
2025-09-08 18:31:41 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 18:31:41 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:41 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:41 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:31:41 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:31:41 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:41 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:41 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:41 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:41 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:31:41 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:31:41 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:41 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:41 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:41 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:41 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:31:41 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:31:41 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:41 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:41 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:41 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:41 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:31:41 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:31:41 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:41 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:41 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:41 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:41 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:31:41 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:31:41 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:41 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:41 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:41 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:41 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:31:42 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 18:31:42 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:42 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:42 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 18:31:42 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 18:31:42 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 18:31:42 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:42 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:42 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:31:42 | [32mINFO[0m | __main__ | setup_logger:139 | Logger '__main__' initialized
2025-09-08 18:31:42 | [32mINFO[0m | __main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:42 | [32mINFO[0m | __main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:42 | [32mINFO[0m | __main__ | __init__:53 | VoiceToTextSystem initialized
2025-09-08 18:31:42 | [32mINFO[0m | __main__ | initialize:58 | Initializing Real-time Voice-to-Text System...
2025-09-08 18:31:42 | [32mINFO[0m | utils.performance | setup_logger:139 | Logger 'utils.performance' initialized
2025-09-08 18:31:42 | [32mINFO[0m | utils.performance | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:42 | [32mINFO[0m | utils.performance | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:42 | [32mINFO[0m | utils.performance | __init__:90 | PerformanceMonitor initialized
2025-09-08 18:31:42 | [32mINFO[0m | utils.performance | start:98 | Starting performance monitoring...
2025-09-08 18:31:42 | [32mINFO[0m | utils.performance | start:106 | Performance monitoring started
2025-09-08 18:31:42 | [32mINFO[0m | ui.status_monitor | setup_logger:139 | Logger 'ui.status_monitor' initialized
2025-09-08 18:31:42 | [32mINFO[0m | ui.status_monitor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:42 | [32mINFO[0m | ui.status_monitor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:42 | [32mINFO[0m | ui.status_monitor | __init__:78 | StatusMonitor initialized
2025-09-08 18:31:42 | [32mINFO[0m | ui.status_monitor | start_monitoring:86 | Starting status monitoring...
2025-09-08 18:31:42 | [32mINFO[0m | ui.status_monitor | start_monitoring:94 | Status monitoring started
2025-09-08 18:31:42 | [32mINFO[0m | utils.file_handler | setup_logger:139 | Logger 'utils.file_handler' initialized
2025-09-08 18:31:42 | [32mINFO[0m | utils.file_handler | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:42 | [32mINFO[0m | utils.file_handler | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:42 | DEBUG    | utils.file_handler | _ensure_directories:58 | Data directories ensured
2025-09-08 18:31:42 | [32mINFO[0m | utils.file_handler | __init__:44 | FileHandler initialized
2025-09-08 18:31:42 | [32mINFO[0m | ui.console_display | setup_logger:139 | Logger 'ui.console_display' initialized
2025-09-08 18:31:42 | [32mINFO[0m | ui.console_display | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:42 | [32mINFO[0m | ui.console_display | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:42 | [32mINFO[0m | ui.colors | setup_logger:139 | Logger 'ui.colors' initialized
2025-09-08 18:31:42 | [32mINFO[0m | ui.colors | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:42 | [32mINFO[0m | ui.colors | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:42 | [32mINFO[0m | ui.colors | __init__:120 | ColorManager initialized with theme: default
2025-09-08 18:31:42 | [32mINFO[0m | ui.console_display | __init__:67 | ConsoleDisplay initialized
2025-09-08 18:31:42 | [32mINFO[0m | models.model_manager | setup_logger:139 | Logger 'models.model_manager' initialized
2025-09-08 18:31:42 | [32mINFO[0m | models.model_manager | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:42 | [32mINFO[0m | models.model_manager | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:42 | [32mINFO[0m | models.model_manager | __init__:46 | ModelManager initialized
2025-09-08 18:31:42 | [32mINFO[0m | models.model_manager | initialize:51 | Starting model initialization...
2025-09-08 18:31:42 | [32mINFO[0m | models.model_manager | _check_gpu_status:76 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 18:31:42 | [32mINFO[0m | models.model_manager | _check_gpu_status:77 | Total VRAM: 6.0GB
2025-09-08 18:31:42 | [32mINFO[0m | models.model_manager | _check_gpu_status:78 | CUDA Version: 11.6
2025-09-08 18:31:42 | [32mINFO[0m | models.model_manager | _check_gpu_status:84 | Current VRAM usage: 0.00GB allocated, 0.00GB reserved
2025-09-08 18:31:42 | [32mINFO[0m | models.model_manager | _load_whisper_models:94 | Loading Whisper models...
2025-09-08 18:31:42 | [32mINFO[0m | models.model_manager | _load_whisper_models:97 | Loading primary Whisper model: medium.en
2025-09-08 18:31:42 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12555MB
2025-09-08 18:31:48 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11501MB
2025-09-08 18:31:53 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14211MB
2025-09-08 18:31:57 | [32mINFO[0m | models.model_manager | _load_whisper_models:112 | Primary Whisper model loaded in 14.56s
2025-09-08 18:31:57 | [32mINFO[0m | models.model_manager | _load_whisper_models:120 | Primary model VRAM usage: 1.42GB
2025-09-08 18:31:57 | [32mINFO[0m | models.model_manager | _load_whisper_models:123 | Loading realtime Whisper model: tiny.en
2025-09-08 18:31:58 | [32mINFO[0m | models.model_manager | _load_whisper_models:134 | Realtime Whisper model loaded in 0.70s
2025-09-08 18:31:58 | [32mINFO[0m | models.model_manager | _load_whisper_models:142 | Realtime model VRAM usage: 0.07GB
2025-09-08 18:31:58 | [32mINFO[0m | models.model_manager | _load_whisper_models:143 | Total VRAM usage: 1.50GB
2025-09-08 18:31:58 | [32mINFO[0m | models.model_manager | _load_emotion_model:152 | Loading emotion detection model...
2025-09-08 18:31:58 | [32mINFO[0m | models.model_manager | _load_emotion_model:164 | Loading transformer-based emotion classifier...
2025-09-08 18:31:58 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12388MB
2025-09-08 18:31:59 | [32mINFO[0m | models.model_manager | _load_emotion_model:178 | Emotion model loaded in 1.55s
2025-09-08 18:31:59 | [32mINFO[0m | models.model_manager | _load_emotion_model:186 | Emotion model VRAM usage: 0.15GB
2025-09-08 18:31:59 | [32mINFO[0m | models.model_manager | _load_emotion_model:187 | Total VRAM usage: 1.65GB
2025-09-08 18:31:59 | [32mINFO[0m | models.model_manager | _verify_models:207 | Optional model 'emotion_detector': ✅ Loaded
2025-09-08 18:31:59 | [32mINFO[0m | models.model_manager | _verify_models:211 | Final VRAM usage: 1.65GB / 4.5GB limit
2025-09-08 18:31:59 | [32mINFO[0m | models.model_manager | initialize:63 | All models initialized successfully
2025-09-08 18:31:59 | [32mINFO[0m | core.voice_processor | setup_logger:139 | Logger 'core.voice_processor' initialized
2025-09-08 18:31:59 | [32mINFO[0m | core.voice_processor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:59 | [32mINFO[0m | core.voice_processor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:59 | [32mINFO[0m | core.audio_utils | setup_logger:139 | Logger 'core.audio_utils' initialized
2025-09-08 18:31:59 | [32mINFO[0m | core.audio_utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:59 | [32mINFO[0m | core.audio_utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:59 | [32mINFO[0m | core.audio_utils | __init__:28 | AudioUtils initialized
2025-09-08 18:31:59 | [32mINFO[0m | core.emotion_detector | setup_logger:139 | Logger 'core.emotion_detector' initialized
2025-09-08 18:31:59 | [32mINFO[0m | core.emotion_detector | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:59 | [32mINFO[0m | core.emotion_detector | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:59 | [32mINFO[0m | core.emotion_detector | __init__:75 | EmotionDetector initialized
2025-09-08 18:31:59 | [32mINFO[0m | core.text_formatter | setup_logger:139 | Logger 'core.text_formatter' initialized
2025-09-08 18:31:59 | [32mINFO[0m | core.text_formatter | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:31:59 | [32mINFO[0m | core.text_formatter | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:31:59 | [32mINFO[0m | core.text_formatter | __init__:62 | TextFormatter initialized
2025-09-08 18:31:59 | [32mINFO[0m | core.voice_processor | __init__:77 | VoiceProcessor initialized
2025-09-08 18:31:59 | [32mINFO[0m | core.voice_processor | initialize:82 | Initializing voice processor...
2025-09-08 18:31:59 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:109 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 18:31:59 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:110 | Total VRAM: 6.0GB
2025-09-08 18:31:59 | [33mWARNING[0m | core.voice_processor | _check_gpu_resources:114 | Less than 6GB VRAM available. Using conservative settings.
2025-09-08 18:31:59 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:120 | Initial GPU memory usage: 1.65GB
2025-09-08 18:31:59 | [32mINFO[0m | core.voice_processor | _initialize_recorder:128 | Configuring RealtimeSTT recorder...
2025-09-08 18:32:03 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12993MB
2025-09-08 18:32:05 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:05 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:05 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:05 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:05 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:05 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:05 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:05 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:05 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:05 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:05 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:05 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:05 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:05 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:05 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:05 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:05 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:05 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:05 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:05 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:32:05 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:05 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:05 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:32:05 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:05 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:32:06 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 18:32:06 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 18:32:06 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:06 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:06 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:06 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:06 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 18:32:06 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 18:32:06 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 18:32:06 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 18:32:06 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 18:32:06 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 18:32:06 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:06 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:06 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:06 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:06 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:32:06 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:32:06 | [32mINFO[0m | __mp_main__ | setup_logger:139 | Logger '__mp_main__' initialized
2025-09-08 18:32:06 | [32mINFO[0m | __mp_main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:06 | [32mINFO[0m | __mp_main__ | setup_logger:139 | Logger '__mp_main__' initialized
2025-09-08 18:32:06 | [32mINFO[0m | __mp_main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:06 | [32mINFO[0m | __mp_main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:32:06 | [32mINFO[0m | __mp_main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:32:08 | [32mINFO[0m | core.voice_processor | _initialize_recorder:180 | RealtimeSTT recorder configured successfully
2025-09-08 18:32:08 | [32mINFO[0m | core.emotion_detector | initialize:80 | Initializing emotion detection models...
2025-09-08 18:32:08 | [32mINFO[0m | core.emotion_detector | _initialize_text_classifier:98 | Loading text emotion classifier...
2025-09-08 18:32:08 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14131MB
2025-09-08 18:32:09 | [32mINFO[0m | core.emotion_detector | _initialize_text_classifier:111 | Text emotion classifier loaded successfully
2025-09-08 18:32:09 | [32mINFO[0m | core.emotion_detector | _initialize_audio_classifier:176 | Custom audio model not found, creating feature-based classifier...
2025-09-08 18:32:09 | [32mINFO[0m | core.emotion_detector | _create_audio_feature_classifier:185 | Creating feature-based audio emotion classifier...
2025-09-08 18:32:09 | [32mINFO[0m | core.emotion_detector | initialize:88 | Emotion detection models initialized successfully
2025-09-08 18:32:09 | [32mINFO[0m | core.text_formatter | initialize:66 | TextFormatter ready for processing
2025-09-08 18:32:09 | [32mINFO[0m | core.voice_processor | initialize:96 | Voice processor initialization completed
2025-09-08 18:32:09 | DEBUG    | ui.status_monitor | add_status_callback:347 | Status callback added
2025-09-08 18:32:09 | DEBUG    | __main__ | _setup_callbacks:129 | System callbacks configured
2025-09-08 18:32:09 | [32mINFO[0m | __main__ | initialize:108 | System initialization completed
2025-09-08 18:32:09 | [32mINFO[0m | core.voice_processor | start:289 | Starting voice processor...
2025-09-08 18:32:09 | [32mINFO[0m | core.voice_processor | _main_processing_loop:302 | Voice processing loop started
2025-09-08 18:32:13 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14181MB
2025-09-08 18:32:18 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14228MB
2025-09-08 18:32:24 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14263MB
2025-09-08 18:32:29 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14284MB
2025-09-08 18:32:34 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14349MB
2025-09-08 18:32:39 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14342MB
2025-09-08 18:32:44 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14156MB
2025-09-08 18:32:46 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:32:46 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:32:47 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:32:47 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:32:49 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14131MB
2025-09-08 18:32:50 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Yeah. Yeah....
2025-09-08 18:32:52 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:32:52 | DEBUG    | core.emotion_detector | detect_emotion:287 | Emotion detected: neutral (1.00)
2025-09-08 18:32:52 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:32:52 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:32:52 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Yeah. Yeah....
2025-09-08 18:32:52 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:32:52 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 1.890s
2025-09-08 18:32:54 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14693MB
2025-09-08 18:32:59 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14686MB
2025-09-08 18:33:00 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:33:00 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:33:01 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:33:01 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:33:04 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Thank you very much....
2025-09-08 18:33:04 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:33:04 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:33:04 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:33:04 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:33:04 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Thank you very much....
2025-09-08 18:33:04 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:33:04 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 0.058s
2025-09-08 18:33:04 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:33:04 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:33:04 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14699MB
2025-09-08 18:33:05 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:33:05 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:33:10 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14788MB
2025-09-08 18:33:15 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14671MB
2025-09-08 18:33:20 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14662MB
2025-09-08 18:33:25 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14673MB
2025-09-08 18:33:30 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14703MB
2025-09-08 18:33:35 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14710MB
2025-09-08 18:33:40 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14708MB
2025-09-08 18:33:45 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14671MB
2025-09-08 18:33:51 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14666MB
2025-09-08 18:33:56 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14684MB
2025-09-08 18:34:01 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14671MB
2025-09-08 18:34:06 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14674MB
2025-09-08 18:34:10 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:34:10 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:34:11 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14673MB
2025-09-08 18:34:11 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:34:11 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:34:14 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Thank you very much....
2025-09-08 18:34:14 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:34:14 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:34:14 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:34:14 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:34:14 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Thank you very much....
2025-09-08 18:34:14 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:34:14 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 0.058s
2025-09-08 18:34:16 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14665MB
2025-09-08 18:34:21 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14666MB
2025-09-08 18:34:26 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14670MB
2025-09-08 18:34:32 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14667MB
2025-09-08 18:34:36 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:34:36 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:34:37 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14676MB
2025-09-08 18:34:37 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:34:37 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:34:40 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Thank you very much....
2025-09-08 18:34:40 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:34:40 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:34:40 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:34:40 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:34:40 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Thank you very much....
2025-09-08 18:34:40 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:34:40 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 0.057s
2025-09-08 18:34:42 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14680MB
2025-09-08 18:34:47 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14693MB
2025-09-08 18:34:52 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14708MB
2025-09-08 18:34:57 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14699MB
2025-09-08 18:35:02 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14702MB
2025-09-08 18:35:07 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14691MB
2025-09-08 18:35:12 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14692MB
2025-09-08 18:35:15 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:35:15 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:35:16 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:35:16 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:35:18 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14915MB
2025-09-08 18:35:19 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: You're welcome, Amy....
2025-09-08 18:35:19 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:35:19 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:35:19 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:35:19 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:35:19 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: You're welcome, Amy....
2025-09-08 18:35:19 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:35:19 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 0.057s
2025-09-08 18:35:23 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14699MB
2025-09-08 18:35:28 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14708MB
2025-09-08 18:35:33 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14730MB
2025-09-08 18:35:38 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14755MB
2025-09-08 18:35:43 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14730MB
2025-09-08 18:35:48 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14739MB
2025-09-08 18:35:53 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14723MB
2025-09-08 18:35:59 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14723MB
2025-09-08 18:36:04 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14722MB
2025-09-08 18:36:09 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14728MB
2025-09-08 18:36:13 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:36:13 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:36:14 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14740MB
2025-09-08 18:36:14 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:36:14 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:36:19 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14719MB
2025-09-08 18:36:24 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14749MB
2025-09-08 18:36:29 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14789MB
2025-09-08 18:36:34 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14748MB
2025-09-08 18:36:36 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:36:36 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:36:37 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:36:37 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:36:40 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14303MB
2025-09-08 18:36:45 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14262MB
2025-09-08 18:36:48 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:36:48 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:36:48 | [31mERROR[0m | ui.console_display | display_realtime_text:355 | Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:36:49 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:36:49 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:36:50 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14252MB
2025-09-08 18:36:51 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Hello, can you hear me?...
2025-09-08 18:36:52 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:36:52 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:36:52 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:36:52 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:36:52 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Hello, can you hear me?...
2025-09-08 18:36:52 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:36:52 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 0.057s
2025-09-08 18:36:55 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14205MB
2025-09-08 18:37:00 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14171MB
2025-09-08 18:37:05 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14162MB
2025-09-08 18:37:07 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:37:07 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:37:08 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:37:08 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:37:10 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14396MB
2025-09-08 18:37:11 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Thank you very much....
2025-09-08 18:37:11 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:37:11 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:37:11 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:37:11 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:37:11 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Thank you very much....
2025-09-08 18:37:11 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:37:11 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 0.057s
2025-09-08 18:37:15 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14187MB
2025-09-08 18:37:20 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14197MB
2025-09-08 18:37:26 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14200MB
2025-09-08 18:37:31 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14175MB
2025-09-08 18:37:36 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14163MB
2025-09-08 18:37:41 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14206MB
2025-09-08 18:37:46 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14199MB
2025-09-08 18:37:51 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14195MB
2025-09-08 18:37:56 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14222MB
2025-09-08 18:38:01 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:38:01 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:38:01 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14220MB
2025-09-08 18:38:02 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:38:02 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:38:05 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Thank you very much....
2025-09-08 18:38:05 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:38:05 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:38:05 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:38:05 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:38:05 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Thank you very much....
2025-09-08 18:38:05 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:38:05 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 0.057s
2025-09-08 18:38:07 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14290MB
2025-09-08 18:38:12 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14332MB
2025-09-08 18:38:17 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14297MB
2025-09-08 18:38:21 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 18:38:21 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:38:21 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:38:21 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:38:21 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:38:21 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:38:21 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:38:21 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:38:21 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:38:21 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:38:21 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:38:21 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:38:21 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:38:21 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:38:21 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:38:21 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:38:21 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:38:21 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:38:21 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:38:21 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:38:21 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:38:21 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:38:21 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:38:21 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:38:21 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:38:21 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:38:21 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:38:21 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:38:21 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:38:21 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:38:21 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:38:21 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:38:21 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:38:21 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:38:22 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14580MB
2025-09-08 18:38:22 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 18:38:22 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:38:22 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:38:22 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 18:38:22 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 18:38:22 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 18:38:22 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:38:22 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:38:22 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:38:22 | [32mINFO[0m | __main__ | setup_logger:139 | Logger '__main__' initialized
2025-09-08 18:38:22 | [32mINFO[0m | __main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:38:22 | [32mINFO[0m | __main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:38:22 | [32mINFO[0m | __main__ | __init__:53 | VoiceToTextSystem initialized
2025-09-08 18:38:22 | [32mINFO[0m | __main__ | initialize:58 | Initializing Real-time Voice-to-Text System...
2025-09-08 18:38:22 | [32mINFO[0m | utils.performance | setup_logger:139 | Logger 'utils.performance' initialized
2025-09-08 18:38:22 | [32mINFO[0m | utils.performance | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:38:22 | [32mINFO[0m | utils.performance | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:38:22 | [32mINFO[0m | utils.performance | __init__:90 | PerformanceMonitor initialized
2025-09-08 18:38:22 | [32mINFO[0m | utils.performance | start:98 | Starting performance monitoring...
2025-09-08 18:38:22 | [32mINFO[0m | utils.performance | start:106 | Performance monitoring started
2025-09-08 18:38:22 | [32mINFO[0m | ui.status_monitor | setup_logger:139 | Logger 'ui.status_monitor' initialized
2025-09-08 18:38:22 | [32mINFO[0m | ui.status_monitor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:38:22 | [32mINFO[0m | ui.status_monitor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:38:22 | [32mINFO[0m | ui.status_monitor | __init__:78 | StatusMonitor initialized
2025-09-08 18:38:22 | [32mINFO[0m | ui.status_monitor | start_monitoring:86 | Starting status monitoring...
2025-09-08 18:38:22 | [32mINFO[0m | ui.status_monitor | start_monitoring:94 | Status monitoring started
2025-09-08 18:38:22 | [32mINFO[0m | utils.file_handler | setup_logger:139 | Logger 'utils.file_handler' initialized
2025-09-08 18:38:22 | [32mINFO[0m | utils.file_handler | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:38:22 | [32mINFO[0m | utils.file_handler | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:38:22 | DEBUG    | utils.file_handler | _ensure_directories:58 | Data directories ensured
2025-09-08 18:38:22 | [32mINFO[0m | utils.file_handler | __init__:44 | FileHandler initialized
2025-09-08 18:38:22 | [32mINFO[0m | ui.console_display | setup_logger:139 | Logger 'ui.console_display' initialized
2025-09-08 18:38:22 | [32mINFO[0m | ui.console_display | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:38:22 | [32mINFO[0m | ui.console_display | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:38:22 | [32mINFO[0m | ui.colors | setup_logger:139 | Logger 'ui.colors' initialized
2025-09-08 18:38:22 | [32mINFO[0m | ui.colors | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:38:22 | [32mINFO[0m | ui.colors | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:38:22 | [32mINFO[0m | ui.colors | __init__:120 | ColorManager initialized with theme: default
2025-09-08 18:38:22 | [32mINFO[0m | ui.console_display | __init__:67 | ConsoleDisplay initialized
2025-09-08 18:38:22 | [32mINFO[0m | models.model_manager | setup_logger:139 | Logger 'models.model_manager' initialized
2025-09-08 18:38:22 | [32mINFO[0m | models.model_manager | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:38:22 | [32mINFO[0m | models.model_manager | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:38:22 | [32mINFO[0m | models.model_manager | __init__:46 | ModelManager initialized
2025-09-08 18:38:22 | [32mINFO[0m | models.model_manager | initialize:51 | Starting model initialization...
2025-09-08 18:38:22 | [32mINFO[0m | models.model_manager | _check_gpu_status:76 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 18:38:22 | [32mINFO[0m | models.model_manager | _check_gpu_status:77 | Total VRAM: 6.0GB
2025-09-08 18:38:22 | [32mINFO[0m | models.model_manager | _check_gpu_status:78 | CUDA Version: 11.6
2025-09-08 18:38:22 | [32mINFO[0m | models.model_manager | _check_gpu_status:84 | Current VRAM usage: 0.00GB allocated, 0.00GB reserved
2025-09-08 18:38:22 | [32mINFO[0m | models.model_manager | _load_whisper_models:94 | Loading Whisper models...
2025-09-08 18:38:22 | [32mINFO[0m | models.model_manager | _load_whisper_models:97 | Loading primary Whisper model: medium.en
2025-09-08 18:38:22 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15205MB
2025-09-08 18:38:27 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15899MB
2025-09-08 18:38:27 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15660MB
2025-09-08 18:38:32 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 13943MB
2025-09-08 18:38:33 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14018MB
2025-09-08 18:38:37 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15750MB
2025-09-08 18:38:38 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15826MB
2025-09-08 18:38:42 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 16049MB
2025-09-08 18:38:43 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 16010MB
2025-09-08 18:38:48 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15796MB
2025-09-08 18:38:48 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15786MB
2025-09-08 18:38:53 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15611MB
2025-09-08 18:38:53 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15599MB
2025-09-08 18:38:58 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15350MB
2025-09-08 18:38:58 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15409MB
2025-09-08 18:39:03 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15124MB
2025-09-08 18:39:03 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15267MB
2025-09-08 18:39:08 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15348MB
2025-09-08 18:39:08 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15325MB
2025-09-08 18:39:09 | [32mINFO[0m | models.model_manager | _load_whisper_models:112 | Primary Whisper model loaded in 47.24s
2025-09-08 18:39:09 | [32mINFO[0m | models.model_manager | _load_whisper_models:120 | Primary model VRAM usage: 1.42GB
2025-09-08 18:39:09 | [32mINFO[0m | models.model_manager | _load_whisper_models:123 | Loading realtime Whisper model: tiny.en
2025-09-08 18:39:11 | [32mINFO[0m | models.model_manager | _load_whisper_models:134 | Realtime Whisper model loaded in 1.31s
2025-09-08 18:39:11 | [32mINFO[0m | models.model_manager | _load_whisper_models:142 | Realtime model VRAM usage: 0.07GB
2025-09-08 18:39:11 | [32mINFO[0m | models.model_manager | _load_whisper_models:143 | Total VRAM usage: 1.50GB
2025-09-08 18:39:11 | [32mINFO[0m | models.model_manager | _load_emotion_model:152 | Loading emotion detection model...
2025-09-08 18:39:11 | [32mINFO[0m | models.model_manager | _load_emotion_model:164 | Loading transformer-based emotion classifier...
2025-09-08 18:39:13 | [32mINFO[0m | models.model_manager | _load_emotion_model:178 | Emotion model loaded in 2.37s
2025-09-08 18:39:13 | [32mINFO[0m | models.model_manager | _load_emotion_model:186 | Emotion model VRAM usage: 0.15GB
2025-09-08 18:39:13 | [32mINFO[0m | models.model_manager | _load_emotion_model:187 | Total VRAM usage: 1.65GB
2025-09-08 18:39:13 | [32mINFO[0m | models.model_manager | _verify_models:207 | Optional model 'emotion_detector': ✅ Loaded
2025-09-08 18:39:13 | [32mINFO[0m | models.model_manager | _verify_models:211 | Final VRAM usage: 1.65GB / 4.5GB limit
2025-09-08 18:39:13 | [32mINFO[0m | models.model_manager | initialize:63 | All models initialized successfully
2025-09-08 18:39:13 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14957MB
2025-09-08 18:39:13 | [32mINFO[0m | core.voice_processor | setup_logger:139 | Logger 'core.voice_processor' initialized
2025-09-08 18:39:13 | [32mINFO[0m | core.voice_processor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:13 | [32mINFO[0m | core.voice_processor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:14 | [32mINFO[0m | core.audio_utils | setup_logger:139 | Logger 'core.audio_utils' initialized
2025-09-08 18:39:14 | [32mINFO[0m | core.audio_utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:14 | [32mINFO[0m | core.audio_utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:14 | [32mINFO[0m | core.audio_utils | __init__:28 | AudioUtils initialized
2025-09-08 18:39:14 | [32mINFO[0m | core.emotion_detector | setup_logger:139 | Logger 'core.emotion_detector' initialized
2025-09-08 18:39:14 | [32mINFO[0m | core.emotion_detector | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:14 | [32mINFO[0m | core.emotion_detector | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:14 | [32mINFO[0m | core.emotion_detector | __init__:75 | EmotionDetector initialized
2025-09-08 18:39:14 | [32mINFO[0m | core.text_formatter | setup_logger:139 | Logger 'core.text_formatter' initialized
2025-09-08 18:39:14 | [32mINFO[0m | core.text_formatter | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:14 | [32mINFO[0m | core.text_formatter | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:14 | [32mINFO[0m | core.text_formatter | __init__:62 | TextFormatter initialized
2025-09-08 18:39:14 | [32mINFO[0m | core.voice_processor | __init__:77 | VoiceProcessor initialized
2025-09-08 18:39:14 | [32mINFO[0m | core.voice_processor | initialize:82 | Initializing voice processor...
2025-09-08 18:39:14 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:109 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 18:39:14 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:110 | Total VRAM: 6.0GB
2025-09-08 18:39:14 | [33mWARNING[0m | core.voice_processor | _check_gpu_resources:114 | Less than 6GB VRAM available. Using conservative settings.
2025-09-08 18:39:14 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:120 | Initial GPU memory usage: 1.65GB
2025-09-08 18:39:14 | [32mINFO[0m | core.voice_processor | _initialize_recorder:128 | Configuring RealtimeSTT recorder...
2025-09-08 18:39:14 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14942MB
2025-09-08 18:39:18 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15362MB
2025-09-08 18:39:19 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15175MB
2025-09-08 18:39:21 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 18:39:21 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 18:39:21 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:39:21 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:39:21 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:39:21 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:39:21 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:39:21 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:39:21 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:39:21 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:39:21 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:39:21 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:39:21 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:39:21 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:39:21 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:39:21 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:39:21 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:39:21 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:39:21 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:39:21 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:39:21 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:39:21 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:21 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:21 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:39:21 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:39:23 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15112MB
2025-09-08 18:39:24 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15111MB
2025-09-08 18:39:24 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 18:39:24 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 18:39:24 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:24 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:24 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:24 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:24 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 18:39:24 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 18:39:24 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 18:39:24 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 18:39:24 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 18:39:24 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:24 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:24 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:24 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:24 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:39:24 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:39:24 | [32mINFO[0m | __mp_main__ | setup_logger:139 | Logger '__mp_main__' initialized
2025-09-08 18:39:24 | [32mINFO[0m | __mp_main__ | setup_logger:139 | Logger '__mp_main__' initialized
2025-09-08 18:39:24 | [32mINFO[0m | __mp_main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:24 | [32mINFO[0m | __mp_main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:39:24 | [32mINFO[0m | __mp_main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:24 | [32mINFO[0m | __mp_main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:39:29 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15140MB
2025-09-08 18:39:29 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15144MB
2025-09-08 18:39:34 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15384MB
2025-09-08 18:39:34 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15563MB
2025-09-08 18:39:34 | [32mINFO[0m | core.voice_processor | _initialize_recorder:180 | RealtimeSTT recorder configured successfully
2025-09-08 18:39:34 | [32mINFO[0m | core.emotion_detector | initialize:80 | Initializing emotion detection models...
2025-09-08 18:39:34 | [32mINFO[0m | core.emotion_detector | _initialize_text_classifier:98 | Loading text emotion classifier...
2025-09-08 18:39:38 | [32mINFO[0m | core.emotion_detector | _initialize_text_classifier:111 | Text emotion classifier loaded successfully
2025-09-08 18:39:38 | [32mINFO[0m | core.emotion_detector | _initialize_audio_classifier:176 | Custom audio model not found, creating feature-based classifier...
2025-09-08 18:39:38 | [32mINFO[0m | core.emotion_detector | _create_audio_feature_classifier:185 | Creating feature-based audio emotion classifier...
2025-09-08 18:39:38 | [32mINFO[0m | core.emotion_detector | initialize:88 | Emotion detection models initialized successfully
2025-09-08 18:39:38 | [32mINFO[0m | core.text_formatter | initialize:66 | TextFormatter ready for processing
2025-09-08 18:39:38 | [32mINFO[0m | core.voice_processor | initialize:96 | Voice processor initialization completed
2025-09-08 18:39:38 | DEBUG    | ui.status_monitor | add_status_callback:347 | Status callback added
2025-09-08 18:39:38 | DEBUG    | __main__ | _setup_callbacks:129 | System callbacks configured
2025-09-08 18:39:38 | [32mINFO[0m | __main__ | initialize:108 | System initialization completed
2025-09-08 18:39:38 | [32mINFO[0m | core.voice_processor | start:289 | Starting voice processor...
2025-09-08 18:39:38 | [32mINFO[0m | core.voice_processor | _main_processing_loop:302 | Voice processing loop started
2025-09-08 18:39:39 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15021MB
2025-09-08 18:39:39 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15024MB
2025-09-08 18:39:44 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14686MB
2025-09-08 18:39:44 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14672MB
2025-09-08 18:39:45 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:39:45 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:39:45 | [31mERROR[0m | ui.console_display | display_realtime_text:355 | Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:39:46 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:39:46 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:39:49 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Hello, can you hear me?...
2025-09-08 18:39:49 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15737MB
2025-09-08 18:39:50 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15694MB
2025-09-08 18:39:52 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:39:52 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:39:53 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:39:53 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:39:53 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:39:53 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:39:53 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Hello, can you hear me?...
2025-09-08 18:39:53 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:39:53 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 4.350s
2025-09-08 18:39:54 | [31mERROR[0m | ui.console_display | display_realtime_text:355 | Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:39:54 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:39:54 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:39:54 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15670MB
2025-09-08 18:39:55 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15745MB
2025-09-08 18:39:57 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Hello, can you hear me?...
2025-09-08 18:39:59 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15832MB
2025-09-08 18:40:00 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15845MB
2025-09-08 18:40:04 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:40:04 | DEBUG    | core.emotion_detector | detect_emotion:287 | Emotion detected: neutral (1.00)
2025-09-08 18:40:04 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:40:04 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:40:04 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Hello, can you hear me?...
2025-09-08 18:40:04 | DEBUG    | __main__ | _on_emotion_detected:150 | Strong emotion detected: neutral
2025-09-08 18:40:04 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 7.796s
2025-09-08 18:40:05 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15843MB
2025-09-08 18:40:05 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15843MB
2025-09-08 18:40:10 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15873MB
2025-09-08 18:40:10 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15871MB
2025-09-08 18:40:11 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:40:11 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:40:12 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:40:12 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:40:15 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15638MB
2025-09-08 18:40:15 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15651MB
2025-09-08 18:40:18 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:40:18 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:40:19 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:40:19 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:40:20 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15783MB
2025-09-08 18:40:20 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15792MB
2025-09-08 18:40:23 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Thank you very much....
2025-09-08 18:40:23 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:40:23 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:40:23 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:40:23 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:40:23 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Thank you very much....
2025-09-08 18:40:23 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 0.105s
2025-09-08 18:40:25 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15433MB
2025-09-08 18:40:25 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15431MB
2025-09-08 18:40:30 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15428MB
2025-09-08 18:40:31 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15427MB
2025-09-08 18:40:35 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15422MB
2025-09-08 18:40:36 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15423MB
2025-09-08 18:40:41 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15475MB
2025-09-08 18:40:41 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15473MB
2025-09-08 18:40:46 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15471MB
2025-09-08 18:40:46 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15477MB
2025-09-08 18:40:51 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15483MB
2025-09-08 18:40:51 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15489MB
2025-09-08 18:40:56 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15502MB
2025-09-08 18:40:56 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15503MB
2025-09-08 18:41:01 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15534MB
2025-09-08 18:41:01 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15504MB
2025-09-08 18:41:06 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15567MB
2025-09-08 18:41:06 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15564MB
2025-09-08 18:41:11 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15550MB
2025-09-08 18:41:12 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15556MB
2025-09-08 18:41:16 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15540MB
2025-09-08 18:41:17 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15539MB
2025-09-08 18:41:22 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15562MB
2025-09-08 18:41:22 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15556MB
2025-09-08 18:41:27 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15548MB
2025-09-08 18:41:27 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15562MB
2025-09-08 18:41:32 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15549MB
2025-09-08 18:41:32 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15543MB
2025-09-08 18:41:37 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15338MB
2025-09-08 18:41:37 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15338MB
2025-09-08 18:41:42 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14813MB
2025-09-08 18:41:42 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14812MB
2025-09-08 18:41:47 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14804MB
2025-09-08 18:41:47 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14790MB
2025-09-08 18:41:52 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14808MB
2025-09-08 18:41:52 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14798MB
2025-09-08 18:41:57 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14822MB
2025-09-08 18:41:58 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14778MB
2025-09-08 18:42:02 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14647MB
2025-09-08 18:42:03 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14672MB
2025-09-08 18:42:08 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14677MB
2025-09-08 18:42:08 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14713MB
2025-09-08 18:42:13 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14743MB
2025-09-08 18:42:13 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14739MB
2025-09-08 18:42:18 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14762MB
2025-09-08 18:42:18 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14759MB
2025-09-08 18:42:23 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14793MB
2025-09-08 18:42:23 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14789MB
2025-09-08 18:42:28 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14810MB
2025-09-08 18:42:28 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14815MB
2025-09-08 18:42:33 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14857MB
2025-09-08 18:42:33 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14855MB
2025-09-08 18:42:38 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14879MB
2025-09-08 18:42:39 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14875MB
2025-09-08 18:42:43 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14921MB
2025-09-08 18:42:44 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14925MB
2025-09-08 18:42:49 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14925MB
2025-09-08 18:42:49 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14925MB
2025-09-08 18:42:54 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14922MB
2025-09-08 18:42:54 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14952MB
2025-09-08 18:42:59 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14957MB
2025-09-08 18:42:59 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14988MB
2025-09-08 18:43:04 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15021MB
2025-09-08 18:43:04 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15021MB
2025-09-08 18:43:09 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15399MB
2025-09-08 18:43:09 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15369MB
2025-09-08 18:43:14 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15155MB
2025-09-08 18:43:14 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15167MB
2025-09-08 18:43:19 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15149MB
2025-09-08 18:43:20 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15138MB
2025-09-08 18:43:24 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15125MB
2025-09-08 18:43:25 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15122MB
2025-09-08 18:43:30 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15156MB
2025-09-08 18:43:30 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15165MB
2025-09-08 18:43:35 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15186MB
2025-09-08 18:43:35 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15183MB
2025-09-08 18:43:40 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15204MB
2025-09-08 18:43:40 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15212MB
2025-09-08 18:43:44 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:43:44 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:43:44 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:43:44 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:43:45 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15402MB
2025-09-08 18:43:45 | [31mERROR[0m | ui.console_display | display_realtime_text:355 | Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:43:45 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:43:45 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:43:45 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15285MB
2025-09-08 18:43:45 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:43:45 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:43:50 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High CPU usage: 100.0%
2025-09-08 18:43:50 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15445MB
2025-09-08 18:43:50 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High CPU usage: 100.0%
2025-09-08 18:43:50 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15427MB
2025-09-08 18:43:51 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: And open your head away....
2025-09-08 18:43:51 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: And open your head away....
2025-09-08 18:43:55 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15966MB
2025-09-08 18:43:55 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15507MB
2025-09-08 18:43:55 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:43:55 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:43:55 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:43:55 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:43:55 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: And open your head away....
2025-09-08 18:43:55 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:43:55 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 4.412s
2025-09-08 18:43:58 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:43:58 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:43:58 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:43:58 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:43:58 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: And open your head away....
2025-09-08 18:43:58 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 7.090s
2025-09-08 18:44:00 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15151MB
2025-09-08 18:44:01 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15175MB
2025-09-08 18:44:05 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15165MB
2025-09-08 18:44:06 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15165MB
2025-09-08 18:44:11 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15223MB
2025-09-08 18:44:11 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15235MB
2025-09-08 18:44:16 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15260MB
2025-09-08 18:44:16 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15261MB
2025-09-08 18:44:21 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15249MB
2025-09-08 18:44:21 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15249MB
2025-09-08 18:44:25 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:44:25 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:44:26 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15235MB
2025-09-08 18:44:26 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15319MB
2025-09-08 18:44:26 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:44:26 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:44:29 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Thank you very much....
2025-09-08 18:44:31 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:44:31 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:44:31 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:44:31 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:44:31 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Thank you very much....
2025-09-08 18:44:31 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:44:31 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 1.613s
2025-09-08 18:44:31 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15829MB
2025-09-08 18:44:31 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15832MB
2025-09-08 18:44:36 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15716MB
2025-09-08 18:44:36 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15714MB
2025-09-08 18:44:41 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15763MB
2025-09-08 18:44:41 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15756MB
2025-09-08 18:44:44 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:44:44 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:44:45 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:44:45 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:44:46 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15718MB
2025-09-08 18:44:47 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15721MB
2025-09-08 18:44:49 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Thank you very much....
2025-09-08 18:44:50 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:44:50 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:44:51 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:44:51 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:44:51 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:44:51 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:44:51 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Thank you very much....
2025-09-08 18:44:51 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 1.914s
2025-09-08 18:44:52 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:44:52 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:44:52 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High CPU usage: 83.3%
2025-09-08 18:44:52 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15846MB
2025-09-08 18:44:52 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High CPU usage: 84.4%
2025-09-08 18:44:52 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15846MB
2025-09-08 18:44:57 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15409MB
2025-09-08 18:44:57 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15410MB
2025-09-08 18:45:02 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15245MB
2025-09-08 18:45:02 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15245MB
2025-09-08 18:45:07 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15113MB
2025-09-08 18:45:07 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15113MB
2025-09-08 18:45:12 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15105MB
2025-09-08 18:45:12 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15105MB
2025-09-08 18:45:17 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15096MB
2025-09-08 18:45:17 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15096MB
2025-09-08 18:45:18 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:45:18 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:45:18 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:45:18 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:45:19 | [31mERROR[0m | ui.console_display | display_realtime_text:355 | Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:45:20 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:45:20 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:45:20 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:45:20 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:45:22 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High CPU usage: 85.9%
2025-09-08 18:45:22 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15488MB
2025-09-08 18:45:23 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High CPU usage: 82.1%
2025-09-08 18:45:23 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15193MB
2025-09-08 18:45:23 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Thank you....
2025-09-08 18:45:24 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Thank you very much....
2025-09-08 18:45:27 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:45:27 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:45:27 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:45:27 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:45:27 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Thank you....
2025-09-08 18:45:27 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:45:27 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 3.549s
2025-09-08 18:45:28 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15854MB
2025-09-08 18:45:28 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15854MB
2025-09-08 18:45:30 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:45:30 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:45:30 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:45:30 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:45:30 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Thank you very much....
2025-09-08 18:45:30 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 5.544s
2025-09-08 18:45:33 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15857MB
2025-09-08 18:45:33 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15829MB
2025-09-08 18:45:33 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:45:33 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:45:33 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:45:33 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:45:34 | [31mERROR[0m | ui.console_display | display_realtime_text:355 | Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:45:35 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:45:35 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:45:35 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:45:35 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:45:38 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High CPU usage: 98.8%
2025-09-08 18:45:38 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15443MB
2025-09-08 18:45:38 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High CPU usage: 96.4%
2025-09-08 18:45:38 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15414MB
2025-09-08 18:45:41 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: I don't know if you can hear me....
2025-09-08 18:45:43 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 16056MB
2025-09-08 18:45:43 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 16046MB
2025-09-08 18:45:43 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:45:43 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:45:43 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:45:43 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:45:43 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: I don't know if you can hear me....
2025-09-08 18:45:43 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:45:43 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 2.538s
2025-09-08 18:45:44 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Hello, can you hear me?...
2025-09-08 18:45:46 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:45:46 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:45:46 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:45:46 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:45:46 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Hello, can you hear me?...
2025-09-08 18:45:46 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 1.806s
2025-09-08 18:45:48 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15778MB
2025-09-08 18:45:48 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15772MB
2025-09-08 18:45:53 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15615MB
2025-09-08 18:45:53 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15611MB
2025-09-08 18:45:58 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15709MB
2025-09-08 18:45:58 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15707MB
2025-09-08 18:46:00 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:46:00 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:46:01 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:46:01 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:46:03 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15573MB
2025-09-08 18:46:04 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15717MB
2025-09-08 18:46:06 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Thank you very much....
2025-09-08 18:46:08 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:46:08 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:46:08 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:46:08 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:46:08 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Thank you very much....
2025-09-08 18:46:08 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:46:08 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 2.205s
2025-09-08 18:46:09 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 16011MB
2025-09-08 18:46:09 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 16010MB
2025-09-08 18:46:14 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15699MB
2025-09-08 18:46:14 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15700MB
2025-09-08 18:46:19 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15682MB
2025-09-08 18:46:19 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15682MB
2025-09-08 18:46:24 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15681MB
2025-09-08 18:46:24 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15638MB
2025-09-08 18:46:29 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15345MB
2025-09-08 18:46:29 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15344MB
2025-09-08 18:46:34 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15343MB
2025-09-08 18:46:34 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15342MB
2025-09-08 18:46:39 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15019MB
2025-09-08 18:46:39 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15019MB
2025-09-08 18:46:44 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14990MB
2025-09-08 18:46:45 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14990MB
2025-09-08 18:46:50 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15085MB
2025-09-08 18:46:50 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15080MB
2025-09-08 18:46:55 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15041MB
2025-09-08 18:46:55 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15041MB
2025-09-08 18:47:00 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15033MB
2025-09-08 18:47:00 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15033MB
2025-09-08 18:47:05 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15007MB
2025-09-08 18:47:05 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15007MB
2025-09-08 18:47:10 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14964MB
2025-09-08 18:47:10 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14964MB
2025-09-08 18:47:15 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14995MB
2025-09-08 18:47:15 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14995MB
2025-09-08 18:47:20 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15028MB
2025-09-08 18:47:20 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15028MB
2025-09-08 18:47:25 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15053MB
2025-09-08 18:47:25 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15053MB
2025-09-08 18:47:31 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15025MB
2025-09-08 18:47:31 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15027MB
2025-09-08 18:47:36 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14977MB
2025-09-08 18:47:36 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14977MB
2025-09-08 18:47:41 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14993MB
2025-09-08 18:47:41 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14993MB
2025-09-08 18:47:46 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14962MB
2025-09-08 18:47:46 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14962MB
2025-09-08 18:47:51 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14987MB
2025-09-08 18:47:51 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14984MB
2025-09-08 18:47:56 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14994MB
2025-09-08 18:47:56 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14994MB
2025-09-08 18:48:01 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14952MB
2025-09-08 18:48:01 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14957MB
2025-09-08 18:48:06 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14928MB
2025-09-08 18:48:06 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14928MB
2025-09-08 18:48:12 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14907MB
2025-09-08 18:48:12 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14907MB
2025-09-08 18:48:17 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14912MB
2025-09-08 18:48:17 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14910MB
2025-09-08 18:48:22 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14848MB
2025-09-08 18:48:22 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14847MB
2025-09-08 18:48:27 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14788MB
2025-09-08 18:48:27 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14790MB
2025-09-08 18:48:29 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:48:29 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:48:30 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:48:30 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:48:32 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14835MB
2025-09-08 18:48:32 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14858MB
2025-09-08 18:48:33 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Thank you very much....
2025-09-08 18:48:36 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:48:36 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:48:36 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:48:36 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:48:36 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Thank you very much....
2025-09-08 18:48:36 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 2.407s
2025-09-08 18:48:37 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15893MB
2025-09-08 18:48:37 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15892MB
2025-09-08 18:48:42 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15796MB
2025-09-08 18:48:42 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15795MB
2025-09-08 18:48:47 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15766MB
2025-09-08 18:48:47 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15765MB
2025-09-08 18:48:53 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15814MB
2025-09-08 18:48:53 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15814MB
2025-09-08 18:48:58 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15691MB
2025-09-08 18:48:58 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15691MB
2025-09-08 18:49:03 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15766MB
2025-09-08 18:49:03 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15766MB
2025-09-08 18:49:08 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15714MB
2025-09-08 18:49:08 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15716MB
2025-09-08 18:49:13 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15695MB
2025-09-08 18:49:13 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15691MB
2025-09-08 18:49:18 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15678MB
2025-09-08 18:49:18 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15678MB
2025-09-08 18:49:23 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15663MB
2025-09-08 18:49:23 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15663MB
2025-09-08 18:49:28 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15634MB
2025-09-08 18:49:28 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15634MB
2025-09-08 18:49:33 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:49:33 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:49:33 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15647MB
2025-09-08 18:49:34 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15626MB
2025-09-08 18:49:34 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:49:34 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:49:37 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Thank you very much....
2025-09-08 18:49:37 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:49:37 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:49:37 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:49:37 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:49:37 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Thank you very much....
2025-09-08 18:49:37 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 0.058s
2025-09-08 18:49:39 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15434MB
2025-09-08 18:49:39 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15434MB
2025-09-08 18:49:44 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15412MB
2025-09-08 18:49:44 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15412MB
2025-09-08 18:49:49 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15379MB
2025-09-08 18:49:49 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15376MB
2025-09-08 18:49:54 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15382MB
2025-09-08 18:49:54 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15382MB
2025-09-08 18:49:59 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15359MB
2025-09-08 18:49:59 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15359MB
2025-09-08 18:50:04 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15346MB
2025-09-08 18:50:04 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15346MB
2025-09-08 18:50:09 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15329MB
2025-09-08 18:50:09 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15329MB
2025-09-08 18:50:15 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15302MB
2025-09-08 18:50:15 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15302MB
2025-09-08 18:50:18 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:50:18 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:50:19 | [31mERROR[0m | ui.console_display | display_realtime_text:355 | Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:50:19 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:50:19 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:50:20 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15323MB
2025-09-08 18:50:20 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15318MB
2025-09-08 18:50:21 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Thank you....
2025-09-08 18:50:21 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:50:21 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:50:21 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:50:21 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:50:21 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Thank you....
2025-09-08 18:50:21 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 0.057s
2025-09-08 18:50:25 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15278MB
2025-09-08 18:50:25 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15278MB
2025-09-08 18:50:30 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15258MB
2025-09-08 18:50:30 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15258MB
2025-09-08 18:50:35 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15241MB
2025-09-08 18:50:35 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15241MB
2025-09-08 18:50:40 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15230MB
2025-09-08 18:50:40 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15230MB
2025-09-08 18:50:45 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15211MB
2025-09-08 18:50:45 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15211MB
2025-09-08 18:50:48 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:50:48 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:50:48 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:50:48 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:50:49 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:50:49 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:50:49 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:50:49 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:50:50 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High CPU usage: 83.3%
2025-09-08 18:50:50 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14579MB
2025-09-08 18:50:50 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High CPU usage: 85.4%
2025-09-08 18:50:50 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14569MB
2025-09-08 18:50:52 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Thank you....
2025-09-08 18:50:52 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:50:52 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:50:52 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:50:52 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:50:52 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Thank you....
2025-09-08 18:50:52 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 0.059s
2025-09-08 18:50:56 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14548MB
2025-09-08 18:50:56 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14548MB
2025-09-08 18:51:01 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14338MB
2025-09-08 18:51:01 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14338MB
2025-09-08 18:51:06 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14333MB
2025-09-08 18:51:06 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14333MB
2025-09-08 18:51:09 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:51:09 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:51:10 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:51:10 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:51:11 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High CPU usage: 82.1%
2025-09-08 18:51:11 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14386MB
2025-09-08 18:51:11 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High CPU usage: 82.1%
2025-09-08 18:51:11 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14386MB
2025-09-08 18:51:14 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:51:14 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:51:15 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:51:15 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:51:16 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14470MB
2025-09-08 18:51:16 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14470MB
2025-09-08 18:51:18 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Thank you very much....
2025-09-08 18:51:21 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15688MB
2025-09-08 18:51:21 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15688MB
2025-09-08 18:51:22 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:51:22 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:51:22 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:51:22 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:51:22 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Thank you very much....
2025-09-08 18:51:22 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:51:22 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 3.512s
2025-09-08 18:51:24 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:51:24 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:51:24 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:51:24 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:51:25 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:51:25 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:51:25 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:51:25 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:51:26 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High CPU usage: 100.0%
2025-09-08 18:51:26 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15225MB
2025-09-08 18:51:26 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High CPU usage: 100.0%
2025-09-08 18:51:26 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15222MB
2025-09-08 18:51:31 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: One, two, three....
2025-09-08 18:51:31 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:51:31 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:51:31 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:51:31 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:51:31 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: One, two, three....
2025-09-08 18:51:31 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:51:31 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 0.072s
2025-09-08 18:51:31 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High CPU usage: 83.3%
2025-09-08 18:51:31 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15201MB
2025-09-08 18:51:31 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High CPU usage: 80.2%
2025-09-08 18:51:31 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15201MB
2025-09-08 18:51:34 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:51:34 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:51:35 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:51:35 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:51:37 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15290MB
2025-09-08 18:51:37 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15289MB
2025-09-08 18:51:38 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Thank you....
2025-09-08 18:51:38 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:51:38 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:51:38 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:51:38 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:51:38 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Thank you....
2025-09-08 18:51:38 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:51:38 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 0.058s
2025-09-08 18:51:42 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14966MB
2025-09-08 18:51:42 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14966MB
2025-09-08 18:51:47 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14890MB
2025-09-08 18:51:47 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14891MB
2025-09-08 18:51:51 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:51:51 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:51:51 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:51:51 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:51:52 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High CPU usage: 88.9%
2025-09-08 18:51:52 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14934MB
2025-09-08 18:51:52 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High CPU usage: 88.9%
2025-09-08 18:51:52 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 14977MB
2025-09-08 18:51:52 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:51:52 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:51:52 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:51:52 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:51:56 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Thank you....
2025-09-08 18:51:56 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Thank you....
2025-09-08 18:51:57 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15142MB
2025-09-08 18:51:57 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15142MB
2025-09-08 18:51:58 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:51:58 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:51:58 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:51:58 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:51:58 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Thank you....
2025-09-08 18:51:58 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 1.825s
2025-09-08 18:52:00 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:52:00 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:52:00 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:52:00 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:52:00 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Thank you....
2025-09-08 18:52:00 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:52:00 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 4.046s
2025-09-08 18:52:02 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15762MB
2025-09-08 18:52:02 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15762MB
2025-09-08 18:52:04 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:52:04 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:52:05 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:52:05 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:52:07 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15868MB
2025-09-08 18:52:07 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15868MB
2025-09-08 18:52:08 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Thank you very much....
2025-09-08 18:52:08 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:52:08 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:52:08 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:52:08 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:52:08 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Thank you very much....
2025-09-08 18:52:08 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:52:08 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 0.058s
2025-09-08 18:52:12 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15205MB
2025-09-08 18:52:12 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15205MB
2025-09-08 18:52:18 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15191MB
2025-09-08 18:52:18 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15191MB
2025-09-08 18:52:23 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15160MB
2025-09-08 18:52:23 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15160MB
2025-09-08 18:52:28 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15207MB
2025-09-08 18:52:28 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15207MB
2025-09-08 18:52:33 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15298MB
2025-09-08 18:52:33 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15298MB
2025-09-08 18:52:38 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15250MB
2025-09-08 18:52:38 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15250MB
2025-09-08 18:52:43 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15232MB
2025-09-08 18:52:48 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15218MB
2025-09-08 18:52:48 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15218MB
2025-09-08 18:52:53 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15209MB
2025-09-08 18:52:53 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15209MB
2025-09-08 18:52:59 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15251MB
2025-09-08 18:52:59 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15251MB
2025-09-08 18:53:04 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15261MB
2025-09-08 18:53:04 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15261MB
2025-09-08 18:53:09 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15275MB
2025-09-08 18:53:09 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15275MB
2025-09-08 18:53:14 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15287MB
2025-09-08 18:53:14 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15287MB
2025-09-08 18:53:19 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15281MB
2025-09-08 18:53:19 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15281MB
2025-09-08 18:53:24 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15269MB
2025-09-08 18:53:24 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15269MB
2025-09-08 18:53:29 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15293MB
2025-09-08 18:53:29 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15293MB
2025-09-08 18:53:34 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15381MB
2025-09-08 18:53:34 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15381MB
2025-09-08 18:53:40 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15376MB
2025-09-08 18:53:40 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15376MB
2025-09-08 18:53:45 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15354MB
2025-09-08 18:53:45 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15354MB
2025-09-08 18:53:50 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15319MB
2025-09-08 18:53:50 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15319MB
2025-09-08 18:53:55 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15321MB
2025-09-08 18:53:55 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15321MB
2025-09-08 18:54:00 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15241MB
2025-09-08 18:54:00 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15241MB
2025-09-08 18:54:05 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15237MB
2025-09-08 18:54:05 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15237MB
2025-09-08 18:54:10 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15247MB
2025-09-08 18:54:10 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15247MB
2025-09-08 18:54:15 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15365MB
2025-09-08 18:54:15 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15364MB
2025-09-08 18:54:21 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15391MB
2025-09-08 18:54:21 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15391MB
2025-09-08 18:54:26 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15382MB
2025-09-08 18:54:26 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15382MB
2025-09-08 18:54:31 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15369MB
2025-09-08 18:54:31 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15369MB
2025-09-08 18:54:36 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15369MB
2025-09-08 18:54:36 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15368MB
2025-09-08 18:54:41 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15232MB
2025-09-08 18:54:41 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15232MB
2025-09-08 18:54:46 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15239MB
2025-09-08 18:54:46 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15239MB
2025-09-08 18:54:51 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15247MB
2025-09-08 18:54:51 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15247MB
2025-09-08 18:54:56 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15265MB
2025-09-08 18:54:56 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15265MB
2025-09-08 18:55:02 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15276MB
2025-09-08 18:55:02 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15276MB
2025-09-08 18:55:07 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15309MB
2025-09-08 18:55:07 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15309MB
2025-09-08 18:55:12 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15338MB
2025-09-08 18:55:12 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15337MB
2025-09-08 18:55:17 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15298MB
2025-09-08 18:55:17 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15298MB
2025-09-08 18:55:22 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15213MB
2025-09-08 18:55:22 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 15213MB
2025-09-08 18:55:27 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9758MB
2025-09-08 18:55:32 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9541MB
2025-09-08 18:55:37 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9542MB
2025-09-08 18:55:42 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9544MB
2025-09-08 18:55:48 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9541MB
2025-09-08 18:55:53 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9563MB
2025-09-08 18:55:58 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9587MB
2025-09-08 18:56:03 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9590MB
2025-09-08 18:56:08 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9605MB
2025-09-08 18:56:13 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9577MB
2025-09-08 18:56:18 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9628MB
2025-09-08 18:56:23 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9639MB
2025-09-08 18:56:29 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9626MB
2025-09-08 18:56:34 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9602MB
2025-09-08 18:56:39 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9579MB
2025-09-08 18:56:44 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9587MB
2025-09-08 18:56:47 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:56:47 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:56:47 | [31mERROR[0m | ui.console_display | display_realtime_text:355 | Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:56:48 | [31mERROR[0m | ui.console_display | display_realtime_text:355 | Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:56:48 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:56:48 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:56:49 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9707MB
2025-09-08 18:56:51 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Have a good one....
2025-09-08 18:56:51 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:56:51 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:56:51 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:56:51 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:56:51 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Have a good one....
2025-09-08 18:56:51 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:56:51 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 0.063s
2025-09-08 18:56:54 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9535MB
2025-09-08 18:56:59 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9505MB
2025-09-08 18:57:04 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9558MB
2025-09-08 18:57:07 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:57:07 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:57:09 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:57:09 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:57:10 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9585MB
2025-09-08 18:57:11 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Thank you....
2025-09-08 18:57:12 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:57:12 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:57:12 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:57:12 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:57:12 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Thank you....
2025-09-08 18:57:12 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:57:12 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 0.056s
2025-09-08 18:57:15 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9618MB
2025-09-08 18:57:15 | [32mINFO[0m | utils.performance | _log_performance_summary:296 | Performance summary - Latency: 0.2ms avg, CPU: 14.8%, Memory: 14028MB, GPU Memory: 1850MB, Score: 100.0/100
2025-09-08 18:57:20 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9579MB
2025-09-08 18:57:22 | [32mINFO[0m | __main__ | shutdown:183 | System shutdown initiated
2025-09-08 18:57:22 | [32mINFO[0m | core.voice_processor | stop:376 | Stopping voice processor...
2025-09-08 18:57:24 | [32mINFO[0m | core.voice_processor | stop:385 | Voice processor stopped
2025-09-08 18:57:24 | [32mINFO[0m | ui.status_monitor | stop_monitoring:101 | Stopping status monitoring...
2025-09-08 18:57:24 | [32mINFO[0m | ui.status_monitor | stop_monitoring:107 | Status monitoring stopped
2025-09-08 18:57:24 | [32mINFO[0m | utils.performance | stop:113 | Stopping performance monitoring...
2025-09-08 18:57:25 | [32mINFO[0m | utils.performance | stop:119 | Performance monitoring stopped
2025-09-08 18:57:25 | [32mINFO[0m | models.model_manager | shutdown:295 | Shutting down ModelManager...
2025-09-08 18:57:25 | [32mINFO[0m | models.model_manager | optimize_memory:262 | GPU memory optimized
2025-09-08 18:57:25 | [32mINFO[0m | models.model_manager | optimize_memory:266 | Memory after optimization: 0.15GB
2025-09-08 18:57:25 | [32mINFO[0m | models.model_manager | shutdown:311 | ModelManager shutdown complete
2025-09-08 18:57:25 | [32mINFO[0m | __main__ | shutdown:212 | System shutdown completed
2025-09-08 18:57:25 | [32mINFO[0m | ui.console_display | start_display:86 | Starting console display...
2025-09-08 18:57:25 | [32mINFO[0m | ui.console_display | start_display:97 | Console display started
2025-09-08 18:57:25 | [31mERROR[0m | __main__ | start_console_mode:273 | Console mode error: object NoneType can't be used in 'await' expression
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\main.py", line 263, in start_console_mode
    print("=" * 60)
TypeError: object NoneType can't be used in 'await' expression
2025-09-08 18:58:07 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 18:58:07 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:07 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:07 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:58:07 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:58:07 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:07 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:07 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:07 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:07 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:58:07 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:58:07 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:07 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:07 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:07 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:07 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:58:07 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:58:07 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:07 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:07 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:07 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:07 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:58:07 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:58:07 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:07 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:07 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:07 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:07 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:58:07 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:58:07 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:07 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:07 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:07 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:07 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:58:08 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 18:58:08 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:08 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:08 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 18:58:08 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 18:58:08 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 18:58:08 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:08 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:08 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:58:08 | [32mINFO[0m | __main__ | setup_logger:139 | Logger '__main__' initialized
2025-09-08 18:58:08 | [32mINFO[0m | __main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:08 | [32mINFO[0m | __main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:08 | [32mINFO[0m | __main__ | __init__:53 | VoiceToTextSystem initialized
2025-09-08 18:58:08 | [32mINFO[0m | __main__ | initialize:58 | Initializing Real-time Voice-to-Text System...
2025-09-08 18:58:08 | [32mINFO[0m | utils.performance | setup_logger:139 | Logger 'utils.performance' initialized
2025-09-08 18:58:08 | [32mINFO[0m | utils.performance | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:08 | [32mINFO[0m | utils.performance | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:08 | [32mINFO[0m | utils.performance | __init__:90 | PerformanceMonitor initialized
2025-09-08 18:58:08 | [32mINFO[0m | utils.performance | start:98 | Starting performance monitoring...
2025-09-08 18:58:08 | [32mINFO[0m | utils.performance | start:106 | Performance monitoring started
2025-09-08 18:58:08 | [32mINFO[0m | ui.status_monitor | setup_logger:139 | Logger 'ui.status_monitor' initialized
2025-09-08 18:58:08 | [32mINFO[0m | ui.status_monitor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:08 | [32mINFO[0m | ui.status_monitor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:08 | [32mINFO[0m | ui.status_monitor | __init__:78 | StatusMonitor initialized
2025-09-08 18:58:08 | [32mINFO[0m | ui.status_monitor | start_monitoring:86 | Starting status monitoring...
2025-09-08 18:58:08 | [32mINFO[0m | ui.status_monitor | start_monitoring:94 | Status monitoring started
2025-09-08 18:58:08 | [32mINFO[0m | utils.file_handler | setup_logger:139 | Logger 'utils.file_handler' initialized
2025-09-08 18:58:08 | [32mINFO[0m | utils.file_handler | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:08 | [32mINFO[0m | utils.file_handler | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:08 | DEBUG    | utils.file_handler | _ensure_directories:58 | Data directories ensured
2025-09-08 18:58:08 | [32mINFO[0m | utils.file_handler | __init__:44 | FileHandler initialized
2025-09-08 18:58:08 | [32mINFO[0m | ui.console_display | setup_logger:139 | Logger 'ui.console_display' initialized
2025-09-08 18:58:08 | [32mINFO[0m | ui.console_display | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:08 | [32mINFO[0m | ui.console_display | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:08 | [32mINFO[0m | ui.colors | setup_logger:139 | Logger 'ui.colors' initialized
2025-09-08 18:58:08 | [32mINFO[0m | ui.colors | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:08 | [32mINFO[0m | ui.colors | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:08 | [32mINFO[0m | ui.colors | __init__:120 | ColorManager initialized with theme: default
2025-09-08 18:58:08 | [32mINFO[0m | ui.console_display | __init__:67 | ConsoleDisplay initialized
2025-09-08 18:58:08 | [32mINFO[0m | models.model_manager | setup_logger:139 | Logger 'models.model_manager' initialized
2025-09-08 18:58:08 | [32mINFO[0m | models.model_manager | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:08 | [32mINFO[0m | models.model_manager | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:08 | [32mINFO[0m | models.model_manager | __init__:46 | ModelManager initialized
2025-09-08 18:58:08 | [32mINFO[0m | models.model_manager | initialize:51 | Starting model initialization...
2025-09-08 18:58:08 | [32mINFO[0m | models.model_manager | _check_gpu_status:76 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 18:58:08 | [32mINFO[0m | models.model_manager | _check_gpu_status:77 | Total VRAM: 6.0GB
2025-09-08 18:58:08 | [32mINFO[0m | models.model_manager | _check_gpu_status:78 | CUDA Version: 11.6
2025-09-08 18:58:08 | [32mINFO[0m | models.model_manager | _check_gpu_status:84 | Current VRAM usage: 0.00GB allocated, 0.00GB reserved
2025-09-08 18:58:08 | [32mINFO[0m | models.model_manager | _load_whisper_models:94 | Loading Whisper models...
2025-09-08 18:58:08 | [32mINFO[0m | models.model_manager | _load_whisper_models:97 | Loading primary Whisper model: medium.en
2025-09-08 18:58:08 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 8646MB
2025-09-08 18:58:14 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 8179MB
2025-09-08 18:58:19 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11128MB
2025-09-08 18:58:23 | [32mINFO[0m | models.model_manager | _load_whisper_models:112 | Primary Whisper model loaded in 14.78s
2025-09-08 18:58:23 | [32mINFO[0m | models.model_manager | _load_whisper_models:120 | Primary model VRAM usage: 1.42GB
2025-09-08 18:58:23 | [32mINFO[0m | models.model_manager | _load_whisper_models:123 | Loading realtime Whisper model: tiny.en
2025-09-08 18:58:24 | [32mINFO[0m | models.model_manager | _load_whisper_models:134 | Realtime Whisper model loaded in 0.67s
2025-09-08 18:58:24 | [32mINFO[0m | models.model_manager | _load_whisper_models:142 | Realtime model VRAM usage: 0.07GB
2025-09-08 18:58:24 | [32mINFO[0m | models.model_manager | _load_whisper_models:143 | Total VRAM usage: 1.50GB
2025-09-08 18:58:24 | [32mINFO[0m | models.model_manager | _load_emotion_model:152 | Loading emotion detection model...
2025-09-08 18:58:24 | [32mINFO[0m | models.model_manager | _load_emotion_model:164 | Loading transformer-based emotion classifier...
2025-09-08 18:58:24 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9113MB
2025-09-08 18:58:25 | [32mINFO[0m | models.model_manager | _load_emotion_model:178 | Emotion model loaded in 1.60s
2025-09-08 18:58:25 | [32mINFO[0m | models.model_manager | _load_emotion_model:186 | Emotion model VRAM usage: 0.15GB
2025-09-08 18:58:25 | [32mINFO[0m | models.model_manager | _load_emotion_model:187 | Total VRAM usage: 1.65GB
2025-09-08 18:58:25 | [32mINFO[0m | models.model_manager | _verify_models:207 | Optional model 'emotion_detector': ✅ Loaded
2025-09-08 18:58:25 | [32mINFO[0m | models.model_manager | _verify_models:211 | Final VRAM usage: 1.65GB / 4.5GB limit
2025-09-08 18:58:25 | [32mINFO[0m | models.model_manager | initialize:63 | All models initialized successfully
2025-09-08 18:58:25 | [32mINFO[0m | core.voice_processor | setup_logger:139 | Logger 'core.voice_processor' initialized
2025-09-08 18:58:25 | [32mINFO[0m | core.voice_processor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:25 | [32mINFO[0m | core.voice_processor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:25 | [32mINFO[0m | core.audio_utils | setup_logger:139 | Logger 'core.audio_utils' initialized
2025-09-08 18:58:25 | [32mINFO[0m | core.audio_utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:25 | [32mINFO[0m | core.audio_utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:25 | [32mINFO[0m | core.audio_utils | __init__:28 | AudioUtils initialized
2025-09-08 18:58:25 | [32mINFO[0m | core.emotion_detector | setup_logger:139 | Logger 'core.emotion_detector' initialized
2025-09-08 18:58:25 | [32mINFO[0m | core.emotion_detector | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:25 | [32mINFO[0m | core.emotion_detector | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:25 | [32mINFO[0m | core.emotion_detector | __init__:75 | EmotionDetector initialized
2025-09-08 18:58:25 | [32mINFO[0m | core.text_formatter | setup_logger:139 | Logger 'core.text_formatter' initialized
2025-09-08 18:58:25 | [32mINFO[0m | core.text_formatter | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:25 | [32mINFO[0m | core.text_formatter | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:25 | [32mINFO[0m | core.text_formatter | __init__:62 | TextFormatter initialized
2025-09-08 18:58:25 | [32mINFO[0m | core.voice_processor | __init__:77 | VoiceProcessor initialized
2025-09-08 18:58:25 | [32mINFO[0m | core.voice_processor | initialize:82 | Initializing voice processor...
2025-09-08 18:58:25 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:109 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 18:58:25 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:110 | Total VRAM: 6.0GB
2025-09-08 18:58:25 | [33mWARNING[0m | core.voice_processor | _check_gpu_resources:114 | Less than 6GB VRAM available. Using conservative settings.
2025-09-08 18:58:25 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:120 | Initial GPU memory usage: 1.65GB
2025-09-08 18:58:25 | [32mINFO[0m | core.voice_processor | _initialize_recorder:128 | Configuring RealtimeSTT recorder...
2025-09-08 18:58:29 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 18:58:29 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:58:29 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:58:29 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 18:58:29 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:58:29 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:58:29 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:58:29 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:58:29 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:58:29 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:58:29 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
lstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:58:29 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:58:29 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:58:29 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:58:29 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:58:29 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:58:29 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:58:29 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:58:29 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:58:29 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:58:29 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:58:29 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:58:29 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:29 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:29 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:58:29 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9856MB
2025-09-08 18:58:30 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 18:58:30 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 18:58:30 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:30 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:30 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:30 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:30 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 18:58:30 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 18:58:30 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 18:58:30 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 18:58:30 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 18:58:30 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 18:58:30 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:30 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:30 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:30 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:30 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:58:30 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:58:30 | [32mINFO[0m | __mp_main__ | setup_logger:139 | Logger '__mp_main__' initialized
2025-09-08 18:58:30 | [32mINFO[0m | __mp_main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:30 | [32mINFO[0m | __mp_main__ | setup_logger:139 | Logger '__mp_main__' initialized
2025-09-08 18:58:30 | [32mINFO[0m | __mp_main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:30 | [32mINFO[0m | __mp_main__ | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:58:30 | [32mINFO[0m | __mp_main__ | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:58:31 | [32mINFO[0m | core.voice_processor | _initialize_recorder:180 | RealtimeSTT recorder configured successfully
2025-09-08 18:58:31 | [32mINFO[0m | core.emotion_detector | initialize:80 | Initializing emotion detection models...
2025-09-08 18:58:31 | [32mINFO[0m | core.emotion_detector | _initialize_text_classifier:98 | Loading text emotion classifier...
2025-09-08 18:58:32 | [32mINFO[0m | core.emotion_detector | _initialize_text_classifier:111 | Text emotion classifier loaded successfully
2025-09-08 18:58:32 | [32mINFO[0m | core.emotion_detector | _initialize_audio_classifier:176 | Custom audio model not found, creating feature-based classifier...
2025-09-08 18:58:32 | [32mINFO[0m | core.emotion_detector | _create_audio_feature_classifier:185 | Creating feature-based audio emotion classifier...
2025-09-08 18:58:32 | [32mINFO[0m | core.emotion_detector | initialize:88 | Emotion detection models initialized successfully
2025-09-08 18:58:32 | [32mINFO[0m | core.text_formatter | initialize:66 | TextFormatter ready for processing
2025-09-08 18:58:32 | [32mINFO[0m | core.voice_processor | initialize:96 | Voice processor initialization completed
2025-09-08 18:58:32 | DEBUG    | ui.status_monitor | add_status_callback:347 | Status callback added
2025-09-08 18:58:32 | DEBUG    | __main__ | _setup_callbacks:129 | System callbacks configured
2025-09-08 18:58:32 | [32mINFO[0m | __main__ | initialize:108 | System initialization completed
2025-09-08 18:58:32 | [32mINFO[0m | core.voice_processor | start:289 | Starting voice processor...
2025-09-08 18:58:32 | [32mINFO[0m | core.voice_processor | _main_processing_loop:302 | Voice processing loop started
2025-09-08 18:58:34 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 10920MB
2025-09-08 18:58:39 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 10921MB
2025-09-08 18:58:44 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 10945MB
2025-09-08 18:58:47 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:58:47 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:58:48 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:58:48 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:58:49 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11249MB
2025-09-08 18:58:51 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Hello, can I hear you?...
2025-09-08 18:58:53 | DEBUG    | core.emotion_detector | detect_emotion:287 | Emotion detected: neutral (0.00)
2025-09-08 18:58:53 | [32mINFO[0m | __main__ | _on_transcription:140 | High-confidence transcription: Hello, can I hear you?...
2025-09-08 18:58:53 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 1.758s
2025-09-08 18:58:55 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11542MB
2025-09-08 18:59:00 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11542MB
2025-09-08 18:59:05 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11744MB
2025-09-08 18:59:07 | [32mINFO[0m | __main__ | shutdown:183 | System shutdown initiated
2025-09-08 18:59:07 | [32mINFO[0m | core.voice_processor | stop:376 | Stopping voice processor...
2025-09-08 18:59:08 | [32mINFO[0m | core.voice_processor | stop:385 | Voice processor stopped
2025-09-08 18:59:08 | [32mINFO[0m | ui.status_monitor | stop_monitoring:101 | Stopping status monitoring...
2025-09-08 18:59:09 | [32mINFO[0m | ui.status_monitor | stop_monitoring:107 | Status monitoring stopped
2025-09-08 18:59:09 | [32mINFO[0m | utils.performance | stop:113 | Stopping performance monitoring...
2025-09-08 18:59:10 | [32mINFO[0m | utils.performance | stop:119 | Performance monitoring stopped
2025-09-08 18:59:10 | [32mINFO[0m | models.model_manager | shutdown:295 | Shutting down ModelManager...
2025-09-08 18:59:10 | [32mINFO[0m | models.model_manager | optimize_memory:262 | GPU memory optimized
2025-09-08 18:59:10 | [32mINFO[0m | models.model_manager | optimize_memory:266 | Memory after optimization: 0.15GB
2025-09-08 18:59:10 | [32mINFO[0m | models.model_manager | shutdown:311 | ModelManager shutdown complete
2025-09-08 18:59:10 | [32mINFO[0m | __main__ | shutdown:212 | System shutdown completed
2025-09-08 18:59:24 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 18:59:24 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:24 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:24 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:59:24 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:59:24 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:24 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:24 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:24 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:24 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:59:24 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:59:24 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:24 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:24 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:24 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:24 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:59:24 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:59:24 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:24 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:24 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:24 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:24 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:59:24 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:59:24 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:24 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:24 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:24 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:24 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:59:24 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:59:24 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:24 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:24 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:24 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:24 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:59:25 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 18:59:25 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:25 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:25 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 18:59:25 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 18:59:25 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 18:59:25 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:25 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:25 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:59:25 | [32mINFO[0m | main | setup_logger:139 | Logger 'main' initialized
2025-09-08 18:59:25 | [32mINFO[0m | main | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:25 | [32mINFO[0m | main | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:25 | [32mINFO[0m | main | __init__:53 | VoiceToTextSystem initialized
2025-09-08 18:59:25 | [32mINFO[0m | main | initialize:58 | Initializing Real-time Voice-to-Text System...
2025-09-08 18:59:25 | [32mINFO[0m | utils.performance | setup_logger:139 | Logger 'utils.performance' initialized
2025-09-08 18:59:25 | [32mINFO[0m | utils.performance | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:25 | [32mINFO[0m | utils.performance | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:25 | [32mINFO[0m | utils.performance | __init__:90 | PerformanceMonitor initialized
2025-09-08 18:59:25 | [32mINFO[0m | utils.performance | start:98 | Starting performance monitoring...
2025-09-08 18:59:25 | [32mINFO[0m | utils.performance | start:106 | Performance monitoring started
2025-09-08 18:59:25 | [32mINFO[0m | ui.status_monitor | setup_logger:139 | Logger 'ui.status_monitor' initialized
2025-09-08 18:59:25 | [32mINFO[0m | ui.status_monitor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:25 | [32mINFO[0m | ui.status_monitor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:25 | [32mINFO[0m | ui.status_monitor | __init__:78 | StatusMonitor initialized
2025-09-08 18:59:25 | [32mINFO[0m | ui.status_monitor | start_monitoring:86 | Starting status monitoring...
2025-09-08 18:59:25 | [32mINFO[0m | ui.status_monitor | start_monitoring:94 | Status monitoring started
2025-09-08 18:59:25 | [32mINFO[0m | utils.file_handler | setup_logger:139 | Logger 'utils.file_handler' initialized
2025-09-08 18:59:25 | [32mINFO[0m | utils.file_handler | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:25 | [32mINFO[0m | utils.file_handler | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:25 | DEBUG    | utils.file_handler | _ensure_directories:58 | Data directories ensured
2025-09-08 18:59:25 | [32mINFO[0m | utils.file_handler | __init__:44 | FileHandler initialized
2025-09-08 18:59:25 | [32mINFO[0m | ui.console_display | setup_logger:139 | Logger 'ui.console_display' initialized
2025-09-08 18:59:25 | [32mINFO[0m | ui.console_display | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:25 | [32mINFO[0m | ui.console_display | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:25 | [32mINFO[0m | ui.colors | setup_logger:139 | Logger 'ui.colors' initialized
2025-09-08 18:59:25 | [32mINFO[0m | ui.colors | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:25 | [32mINFO[0m | ui.colors | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:25 | [32mINFO[0m | ui.colors | __init__:120 | ColorManager initialized with theme: default
2025-09-08 18:59:25 | [32mINFO[0m | ui.console_display | __init__:67 | ConsoleDisplay initialized
2025-09-08 18:59:25 | [32mINFO[0m | models.model_manager | setup_logger:139 | Logger 'models.model_manager' initialized
2025-09-08 18:59:25 | [32mINFO[0m | models.model_manager | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:25 | [32mINFO[0m | models.model_manager | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:25 | [32mINFO[0m | models.model_manager | __init__:46 | ModelManager initialized
2025-09-08 18:59:25 | [32mINFO[0m | models.model_manager | initialize:51 | Starting model initialization...
2025-09-08 18:59:25 | [32mINFO[0m | models.model_manager | _check_gpu_status:76 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 18:59:25 | [32mINFO[0m | models.model_manager | _check_gpu_status:77 | Total VRAM: 6.0GB
2025-09-08 18:59:25 | [32mINFO[0m | models.model_manager | _check_gpu_status:78 | CUDA Version: 11.6
2025-09-08 18:59:25 | [32mINFO[0m | models.model_manager | _check_gpu_status:84 | Current VRAM usage: 0.00GB allocated, 0.00GB reserved
2025-09-08 18:59:25 | [32mINFO[0m | models.model_manager | _load_whisper_models:94 | Loading Whisper models...
2025-09-08 18:59:25 | [32mINFO[0m | models.model_manager | _load_whisper_models:97 | Loading primary Whisper model: medium.en
2025-09-08 18:59:25 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9362MB
2025-09-08 18:59:31 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 8495MB
2025-09-08 18:59:36 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 12148MB
2025-09-08 18:59:37 | [32mINFO[0m | models.model_manager | _load_whisper_models:112 | Primary Whisper model loaded in 11.86s
2025-09-08 18:59:37 | [32mINFO[0m | models.model_manager | _load_whisper_models:120 | Primary model VRAM usage: 1.42GB
2025-09-08 18:59:37 | [32mINFO[0m | models.model_manager | _load_whisper_models:123 | Loading realtime Whisper model: tiny.en
2025-09-08 18:59:38 | [32mINFO[0m | models.model_manager | _load_whisper_models:134 | Realtime Whisper model loaded in 0.61s
2025-09-08 18:59:38 | [32mINFO[0m | models.model_manager | _load_whisper_models:142 | Realtime model VRAM usage: 0.07GB
2025-09-08 18:59:38 | [32mINFO[0m | models.model_manager | _load_whisper_models:143 | Total VRAM usage: 1.50GB
2025-09-08 18:59:38 | [32mINFO[0m | models.model_manager | _load_emotion_model:152 | Loading emotion detection model...
2025-09-08 18:59:38 | [32mINFO[0m | models.model_manager | _load_emotion_model:164 | Loading transformer-based emotion classifier...
2025-09-08 18:59:39 | [32mINFO[0m | models.model_manager | _load_emotion_model:178 | Emotion model loaded in 1.13s
2025-09-08 18:59:39 | [32mINFO[0m | models.model_manager | _load_emotion_model:186 | Emotion model VRAM usage: 0.15GB
2025-09-08 18:59:39 | [32mINFO[0m | models.model_manager | _load_emotion_model:187 | Total VRAM usage: 1.65GB
2025-09-08 18:59:39 | [32mINFO[0m | models.model_manager | _verify_models:207 | Optional model 'emotion_detector': ✅ Loaded
2025-09-08 18:59:39 | [32mINFO[0m | models.model_manager | _verify_models:211 | Final VRAM usage: 1.65GB / 4.5GB limit
2025-09-08 18:59:39 | [32mINFO[0m | models.model_manager | initialize:63 | All models initialized successfully
2025-09-08 18:59:39 | [32mINFO[0m | core.voice_processor | setup_logger:139 | Logger 'core.voice_processor' initialized
2025-09-08 18:59:39 | [32mINFO[0m | core.voice_processor | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:39 | [32mINFO[0m | core.voice_processor | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:39 | [32mINFO[0m | core.audio_utils | setup_logger:139 | Logger 'core.audio_utils' initialized
2025-09-08 18:59:39 | [32mINFO[0m | core.audio_utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:39 | [32mINFO[0m | core.audio_utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:39 | [32mINFO[0m | core.audio_utils | __init__:28 | AudioUtils initialized
2025-09-08 18:59:39 | [32mINFO[0m | core.emotion_detector | setup_logger:139 | Logger 'core.emotion_detector' initialized
2025-09-08 18:59:39 | [32mINFO[0m | core.emotion_detector | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:39 | [32mINFO[0m | core.emotion_detector | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:39 | [32mINFO[0m | core.emotion_detector | __init__:75 | EmotionDetector initialized
2025-09-08 18:59:39 | [32mINFO[0m | core.text_formatter | setup_logger:139 | Logger 'core.text_formatter' initialized
2025-09-08 18:59:39 | [32mINFO[0m | core.text_formatter | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:39 | [32mINFO[0m | core.text_formatter | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:39 | [32mINFO[0m | core.text_formatter | __init__:62 | TextFormatter initialized
2025-09-08 18:59:39 | [32mINFO[0m | core.voice_processor | __init__:77 | VoiceProcessor initialized
2025-09-08 18:59:39 | [32mINFO[0m | core.voice_processor | initialize:82 | Initializing voice processor...
2025-09-08 18:59:39 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:109 | GPU: NVIDIA GeForce GTX 1660 Ti
2025-09-08 18:59:39 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:110 | Total VRAM: 6.0GB
2025-09-08 18:59:39 | [33mWARNING[0m | core.voice_processor | _check_gpu_resources:114 | Less than 6GB VRAM available. Using conservative settings.
2025-09-08 18:59:39 | [32mINFO[0m | core.voice_processor | _check_gpu_resources:120 | Initial GPU memory usage: 1.65GB
2025-09-08 18:59:39 | [32mINFO[0m | core.voice_processor | _initialize_recorder:128 | Configuring RealtimeSTT recorder...
2025-09-08 18:59:41 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 9781MB
2025-09-08 18:59:42 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 18:59:42 | [32mINFO[0m | realstt | setup_logger:139 | Logger 'realstt' initialized
2025-09-08 18:59:42 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:59:42 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:59:42 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:59:42 | [32mINFO[0m | realstt.core | setup_logger:139 | Logger 'realstt.core' initialized
2025-09-08 18:59:42 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt.core | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt.core | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:59:42 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:59:42 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:59:42 | [32mINFO[0m | realstt.api | setup_logger:139 | Logger 'realstt.api' initialized
2025-09-08 18:59:42 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt.api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt.api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:59:42 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:59:42 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:59:42 | [32mINFO[0m | realstt.ui | setup_logger:139 | Logger 'realstt.ui' initialized
2025-09-08 18:59:42 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt.ui | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt.ui | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:59:42 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:59:42 | [32mINFO[0m | realstt.utils | setup_logger:139 | Logger 'realstt.utils' initialized
2025-09-08 18:59:42 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt.utils | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt.utils | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:59:42 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:59:42 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:59:42 | [32mINFO[0m | realstt.models | setup_logger:139 | Logger 'realstt.models' initialized
2025-09-08 18:59:42 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt.models | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:42 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt.models | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:42 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:59:42 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:59:43 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 18:59:43 | [32mINFO[0m | api.websocket_api | setup_logger:139 | Logger 'api.websocket_api' initialized
2025-09-08 18:59:43 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:43 | [32mINFO[0m | api.websocket_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:43 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:43 | [32mINFO[0m | api.websocket_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:43 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 18:59:43 | [32mINFO[0m | api.websocket_api | __init__:45 | WebSocket ConnectionManager initialized
2025-09-08 18:59:43 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 18:59:43 | [32mINFO[0m | api.websocket_api | __init__:205 | WebSocketManager initialized
2025-09-08 18:59:43 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 18:59:43 | [32mINFO[0m | api.rest_api | setup_logger:139 | Logger 'api.rest_api' initialized
2025-09-08 18:59:43 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:43 | [32mINFO[0m | api.rest_api | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:43 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:43 | [32mINFO[0m | api.rest_api | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:43 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:59:43 | [32mINFO[0m | realstt | setup_system_logging:251 | System logging configured
2025-09-08 18:59:43 | [32mINFO[0m | main | setup_logger:139 | Logger 'main' initialized
2025-09-08 18:59:43 | [32mINFO[0m | main | setup_logger:139 | Logger 'main' initialized
2025-09-08 18:59:43 | [32mINFO[0m | main | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:43 | [32mINFO[0m | main | setup_logger:140 | Console level: INFO, File level: DEBUG
2025-09-08 18:59:43 | [32mINFO[0m | main | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:43 | [32mINFO[0m | main | setup_logger:141 | Log files: C:\Users\<USER>\Desktop\realstt\data\logs
2025-09-08 18:59:45 | [32mINFO[0m | core.voice_processor | _initialize_recorder:180 | RealtimeSTT recorder configured successfully
2025-09-08 18:59:45 | [32mINFO[0m | core.emotion_detector | initialize:80 | Initializing emotion detection models...
2025-09-08 18:59:45 | [32mINFO[0m | core.emotion_detector | _initialize_text_classifier:98 | Loading text emotion classifier...
2025-09-08 18:59:46 | [32mINFO[0m | core.emotion_detector | _initialize_text_classifier:111 | Text emotion classifier loaded successfully
2025-09-08 18:59:46 | [32mINFO[0m | core.emotion_detector | _initialize_audio_classifier:176 | Custom audio model not found, creating feature-based classifier...
2025-09-08 18:59:46 | [32mINFO[0m | core.emotion_detector | _create_audio_feature_classifier:185 | Creating feature-based audio emotion classifier...
2025-09-08 18:59:46 | [32mINFO[0m | core.emotion_detector | initialize:88 | Emotion detection models initialized successfully
2025-09-08 18:59:46 | [32mINFO[0m | core.text_formatter | initialize:66 | TextFormatter ready for processing
2025-09-08 18:59:46 | [32mINFO[0m | core.voice_processor | initialize:96 | Voice processor initialization completed
2025-09-08 18:59:46 | DEBUG    | ui.status_monitor | add_status_callback:347 | Status callback added
2025-09-08 18:59:46 | DEBUG    | main | _setup_callbacks:129 | System callbacks configured
2025-09-08 18:59:46 | [32mINFO[0m | main | initialize:108 | System initialization completed
2025-09-08 18:59:46 | [32mINFO[0m | core.voice_processor | start:289 | Starting voice processor...
2025-09-08 18:59:46 | [32mINFO[0m | core.voice_processor | _main_processing_loop:302 | Voice processing loop started
2025-09-08 18:59:46 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11037MB
2025-09-08 18:59:48 | DEBUG    | core.voice_processor | _on_recording_start:192 | Recording started
2025-09-08 18:59:48 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = True
2025-09-08 18:59:50 | DEBUG    | core.voice_processor | _on_recording_stop:202 | Recording stopped
2025-09-08 18:59:50 | DEBUG    | ui.status_monitor | update_status:185 | Status updated: recording = False
2025-09-08 18:59:51 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11011MB
2025-09-08 18:59:52 | DEBUG    | core.voice_processor | _process_final_transcription:324 | Processing final transcription: Can you hear me?...
2025-09-08 18:59:53 | DEBUG    | core.emotion_detector | detect_emotion:287 | Emotion detected: neutral (0.00)
2025-09-08 18:59:53 | [32mINFO[0m | main | _on_transcription:140 | High-confidence transcription: Can you hear me?...
2025-09-08 18:59:53 | DEBUG    | core.voice_processor | _process_final_transcription:366 | Final transcription processed in 0.905s
2025-09-08 18:59:56 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11539MB
2025-09-08 19:00:01 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11596MB
2025-09-08 19:00:06 | [33mWARNING[0m | utils.performance | _check_alerts:284 | Performance alert: High memory usage: 11714MB
2025-09-08 19:00:09 | [32mINFO[0m | main | shutdown:183 | System shutdown initiated
2025-09-08 19:00:09 | [32mINFO[0m | core.voice_processor | stop:376 | Stopping voice processor...
2025-09-08 19:00:09 | [32mINFO[0m | core.voice_processor | stop:385 | Voice processor stopped
2025-09-08 19:00:09 | [32mINFO[0m | ui.status_monitor | stop_monitoring:101 | Stopping status monitoring...
2025-09-08 19:00:09 | [32mINFO[0m | ui.status_monitor | stop_monitoring:107 | Status monitoring stopped
2025-09-08 19:00:09 | [32mINFO[0m | utils.performance | stop:113 | Stopping performance monitoring...
2025-09-08 19:00:11 | [32mINFO[0m | utils.performance | stop:119 | Performance monitoring stopped
2025-09-08 19:00:11 | [32mINFO[0m | models.model_manager | shutdown:295 | Shutting down ModelManager...
2025-09-08 19:00:12 | [32mINFO[0m | models.model_manager | optimize_memory:262 | GPU memory optimized
2025-09-08 19:00:12 | [32mINFO[0m | models.model_manager | optimize_memory:266 | Memory after optimization: 0.15GB
2025-09-08 19:00:12 | [32mINFO[0m | models.model_manager | shutdown:311 | ModelManager shutdown complete
2025-09-08 19:00:12 | [32mINFO[0m | main | shutdown:212 | System shutdown completed
