2025-09-08 16:29:58 | [31mERROR[0m | core.voice_processor | _initialize_recorder:187 | Failed to initialize recorder: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 746, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 647, in __init__
    self.model = ctranslate2.models.Whisper(
RuntimeError: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
2025-09-08 16:29:58 | [31mERROR[0m | core.voice_processor | initialize:100 | Failed to initialize voice processor: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 88, in initialize
    await self._initialize_recorder()
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 746, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 647, in __init__
    self.model = ctranslate2.models.Whisper(
RuntimeError: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
2025-09-08 16:47:09 | [31mERROR[0m | models.model_manager | _load_emotion_model:190 | Failed to load emotion model: At least one of TensorFlow 2.0 or PyTorch should be installed. To install TensorFlow 2.0, read the instructions at https://www.tensorflow.org/install/ To install PyTorch, read the instructions at https://pytorch.org/.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\models\model_manager.py", line 168, in _load_emotion_model
    emotion_classifier = pipeline(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\transformers\pipelines\__init__.py", line 1028, in pipeline
    framework, model = infer_framework_load_model(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\transformers\pipelines\base.py", line 244, in infer_framework_load_model
    raise RuntimeError(
RuntimeError: At least one of TensorFlow 2.0 or PyTorch should be installed. To install TensorFlow 2.0, read the instructions at https://www.tensorflow.org/install/ To install PyTorch, read the instructions at https://pytorch.org/.
2025-09-08 16:47:09 | [31mERROR[0m | core.voice_processor | _initialize_recorder:187 | Failed to initialize recorder: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 746, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 647, in __init__
    self.model = ctranslate2.models.Whisper(
RuntimeError: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
2025-09-08 16:47:09 | [31mERROR[0m | core.voice_processor | initialize:100 | Failed to initialize voice processor: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 88, in initialize
    await self._initialize_recorder()
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 746, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 647, in __init__
    self.model = ctranslate2.models.Whisper(
RuntimeError: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
2025-09-08 16:57:46 | [31mERROR[0m | core.voice_processor | _initialize_recorder:187 | Failed to initialize recorder: AudioToTextRecorder.__init__() got an unexpected keyword argument 'realtime_batch_size'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
TypeError: AudioToTextRecorder.__init__() got an unexpected keyword argument 'realtime_batch_size'
2025-09-08 16:57:46 | [31mERROR[0m | core.voice_processor | initialize:100 | Failed to initialize voice processor: AudioToTextRecorder.__init__() got an unexpected keyword argument 'realtime_batch_size'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 88, in initialize
    await self._initialize_recorder()
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
TypeError: AudioToTextRecorder.__init__() got an unexpected keyword argument 'realtime_batch_size'
2025-09-08 17:11:27 | [31mERROR[0m | core.voice_processor | _initialize_recorder:186 | Failed to initialize recorder: AudioToTextRecorder.__init__() got an unexpected keyword argument 'batch_size'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
TypeError: AudioToTextRecorder.__init__() got an unexpected keyword argument 'batch_size'
2025-09-08 17:11:27 | [31mERROR[0m | core.voice_processor | initialize:100 | Failed to initialize voice processor: AudioToTextRecorder.__init__() got an unexpected keyword argument 'batch_size'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 88, in initialize
    await self._initialize_recorder()
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
TypeError: AudioToTextRecorder.__init__() got an unexpected keyword argument 'batch_size'
2025-09-08 17:16:06 | [31mERROR[0m | core.voice_processor | _initialize_recorder:185 | Failed to initialize recorder: AudioToTextRecorder.__init__() got an unexpected keyword argument 'on_vad_start'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
TypeError: AudioToTextRecorder.__init__() got an unexpected keyword argument 'on_vad_start'
2025-09-08 17:16:06 | [31mERROR[0m | core.voice_processor | initialize:100 | Failed to initialize voice processor: AudioToTextRecorder.__init__() got an unexpected keyword argument 'on_vad_start'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 88, in initialize
    await self._initialize_recorder()
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
TypeError: AudioToTextRecorder.__init__() got an unexpected keyword argument 'on_vad_start'
2025-09-08 17:22:51 | [31mERROR[0m | core.voice_processor | _initialize_recorder:183 | Failed to initialize recorder: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 672, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 145, in __init__
    self.model = ctranslate2.models.Whisper(
RuntimeError: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
2025-09-08 17:22:51 | [31mERROR[0m | core.voice_processor | initialize:100 | Failed to initialize voice processor: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 88, in initialize
    await self._initialize_recorder()
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 672, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 145, in __init__
    self.model = ctranslate2.models.Whisper(
RuntimeError: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
2025-09-08 17:24:12 | [31mERROR[0m | core.voice_processor | _initialize_recorder:184 | Failed to initialize recorder: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 672, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 145, in __init__
    self.model = ctranslate2.models.Whisper(
TypeError: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
2025-09-08 17:24:12 | [31mERROR[0m | core.voice_processor | initialize:100 | Failed to initialize voice processor: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 88, in initialize
    await self._initialize_recorder()
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 672, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 145, in __init__
    self.model = ctranslate2.models.Whisper(
TypeError: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
2025-09-08 18:21:35 | [31mERROR[0m | core.voice_processor | _initialize_recorder:184 | Failed to initialize recorder: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 672, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 145, in __init__
    self.model = ctranslate2.models.Whisper(
TypeError: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
2025-09-08 18:21:35 | [31mERROR[0m | core.voice_processor | initialize:100 | Failed to initialize voice processor: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 88, in initialize
    await self._initialize_recorder()
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 672, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 145, in __init__
    self.model = ctranslate2.models.Whisper(
TypeError: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
2025-09-08 18:22:44 | [31mERROR[0m | __main__ | main:294 | System error: 'VoiceToTextSystem' object has no attribute 'start_console_mode'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\main.py", line 283, in main
    await system.start_console_mode()
AttributeError: 'VoiceToTextSystem' object has no attribute 'start_console_mode'
2025-09-08 18:30:04 | [31mERROR[0m | __main__ | start_console_mode:273 | Console mode error: 'VoiceProcessor' object has no attribute 'start_listening'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\main.py", line 259, in start_console_mode
    await self.voice_processor.start_listening()
AttributeError: 'VoiceProcessor' object has no attribute 'start_listening'
2025-09-08 18:32:52 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:32:52 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:32:52 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:32:52 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:33:04 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:33:04 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:33:04 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:33:04 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:33:04 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:34:14 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:34:14 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:34:14 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:34:14 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:34:14 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:34:40 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:34:40 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:34:40 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:34:40 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:34:40 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:35:19 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:35:19 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:35:19 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:35:19 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:35:19 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:36:48 | [31mERROR[0m | ui.console_display | display_realtime_text:355 | Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:36:52 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:36:52 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:36:52 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:36:52 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:36:52 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:37:11 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:37:11 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:37:11 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:37:11 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:37:11 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:38:05 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:38:05 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:38:05 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:38:05 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:38:05 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:39:45 | [31mERROR[0m | ui.console_display | display_realtime_text:355 | Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:39:53 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:39:53 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:39:53 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:39:53 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:39:53 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:39:54 | [31mERROR[0m | ui.console_display | display_realtime_text:355 | Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:40:04 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:40:04 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:40:04 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:40:23 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:40:23 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:40:23 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:40:23 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:43:45 | [31mERROR[0m | ui.console_display | display_realtime_text:355 | Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:43:55 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:43:55 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:43:55 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:43:55 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:43:55 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:43:58 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:43:58 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:43:58 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:43:58 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:44:31 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:44:31 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:44:31 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:44:31 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:44:31 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:44:51 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:44:51 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:44:51 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:44:51 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:45:19 | [31mERROR[0m | ui.console_display | display_realtime_text:355 | Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:45:27 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:45:27 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:45:27 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:45:27 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:45:27 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:45:30 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:45:30 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:45:30 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:45:30 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:45:34 | [31mERROR[0m | ui.console_display | display_realtime_text:355 | Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:45:43 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:45:43 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:45:43 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:45:43 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:45:43 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:45:46 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:45:46 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:45:46 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:45:46 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:46:08 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:46:08 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:46:08 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:46:08 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:46:08 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:48:36 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:48:36 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:48:36 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:48:36 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:49:37 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:49:37 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:49:37 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:49:37 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:50:19 | [31mERROR[0m | ui.console_display | display_realtime_text:355 | Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:50:21 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:50:21 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:50:21 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:50:21 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:50:52 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:50:52 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:50:52 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:50:52 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:51:22 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:51:22 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:51:22 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:51:22 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:51:22 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:51:31 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:51:31 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:51:31 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:51:31 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:51:31 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:51:38 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:51:38 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:51:38 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:51:38 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:51:38 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:51:58 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:51:58 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:51:58 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:51:58 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:52:00 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:52:00 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:52:00 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:52:00 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:52:00 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:52:08 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:52:08 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:52:08 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:52:08 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:52:08 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:56:47 | [31mERROR[0m | ui.console_display | display_realtime_text:355 | Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:56:48 | [31mERROR[0m | ui.console_display | display_realtime_text:355 | Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:56:51 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:56:51 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:56:51 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:56:51 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:56:51 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:57:12 | [31mERROR[0m | core.emotion_detector | _detect_text_emotion:341 | Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:57:12 | [31mERROR[0m | core.emotion_detector | detect_emotion:291 | Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:57:12 | [31mERROR[0m | core.text_formatter | _add_emotion_tag:271 | Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:57:12 | [31mERROR[0m | ui.console_display | display_final_text:373 | Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:57:12 | [31mERROR[0m | __main__ | _on_emotion_detected:153 | Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:57:25 | [31mERROR[0m | __main__ | start_console_mode:273 | Console mode error: object NoneType can't be used in 'await' expression
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\main.py", line 263, in start_console_mode
    print("=" * 60)
TypeError: object NoneType can't be used in 'await' expression
