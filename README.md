# 🎤 Real-time Voice-to-Text System

A comprehensive real-time speech-to-text system optimized for GTX 1660 Ti with advanced features including emotion detection, pause detection, filler word detection, and API integration.

## ✨ Features

- **🎯 High-Accuracy Transcription**: 95%+ accuracy using optimized Whisper models
- **😊 Emotion Detection**: 7 emotions with confidence scoring (Happy, Sad, Angry, Neutral, Excited, Frustrated, Surprised)
- **⏸️ Pause Detection**: Precise timing of speech pauses with categorization
- **🗣️ Filler Word Detection**: Identifies "um", "ah", "hmm" and other filler words
- **⚡ Real-time Processing**: <200ms latency optimized for GTX 1660 Ti
- **🎨 Color-coded Console**: Rich console interface with emotion highlighting
- **🌐 API Integration**: REST and WebSocket APIs for external applications
- **📊 Performance Monitoring**: Real-time GPU/CPU usage and latency tracking

## 🔧 System Requirements

### Hardware
- **GPU**: GTX 1660 Ti (6GB VRAM) or better
- **RAM**: 16GB minimum (32GB recommended)
- **CPU**: Intel i5/AMD Ryzen 5 or better
- **Microphone**: High-quality USB/XLR microphone

### Software
- **OS**: Windows 10/11, Linux, or macOS
- **Python**: 3.9 or higher
- **CUDA**: 11.8 or higher (for GPU acceleration)

## 🚀 Quick Start

### 1. Clone and Setup
```bash
git clone <repository-url>
cd realstt
pip install -r requirements.txt
```

### 2. Configure Environment
```bash
cp .env.example .env
# Edit .env with your settings
```

### 3. Run the System
```bash
python main.py
```

### 4. With API Server
```bash
python main.py --api
```

## 📁 Project Structure

```
realstt/
├── main.py                     # 🚀 Main entry point
├── config.py                   # ⚙️ Configuration settings
├── requirements.txt            # 📦 Dependencies
├── .env                        # 🔒 Environment variables
│
├── core/                       # 🧠 Core business logic
│   ├── voice_processor.py      # 🎤 Main voice processing engine
│   ├── emotion_detector.py     # 😊 Emotion analysis
│   ├── text_formatter.py       # 📝 Text formatting with metadata
│   └── audio_utils.py          # 🔊 Audio processing utilities
│
├── api/                        # 🌐 API layer
│   ├── rest_api.py             # 📡 FastAPI REST endpoints
│   ├── websocket_api.py        # ⚡ WebSocket real-time API
│   └── models.py               # 🏗️ API data models
│
├── ui/                         # 🖥️ User interface
│   ├── console_display.py      # 💻 Console interface
│   ├── colors.py               # 🎨 Color management
│   └── status_monitor.py       # 📊 System monitoring
│
├── utils/                      # 🛠️ Utilities
│   ├── logger.py               # 📋 Logging system
│   ├── performance.py          # ⏱️ Performance monitoring
│   └── file_handler.py         # 💾 File operations
│
├── models/                     # 🤖 AI models
│   ├── model_manager.py        # 🎯 Model management
│   └── emotion_models/         # 😊 Emotion detection models
│
├── tests/                      # 🧪 Test suite
├── data/                       # 📊 Data and outputs
├── scripts/                    # 🔧 Setup scripts
└── docs/                       # 📚 Documentation
```

## 🎛️ Configuration

### Model Settings (GTX 1660 Ti Optimized)
```python
# config.py
PRIMARY_MODEL = "medium.en"      # Best accuracy/performance balance
REALTIME_MODEL = "tiny.en"       # Fast real-time updates
BATCH_SIZE = 4                   # Optimized for 6GB VRAM
COMPUTE_TYPE = "float16"         # Memory optimization
```

### Audio Settings
```python
SAMPLE_RATE = 16000              # Optimal for speech
VAD_SENSITIVITY = 0.7            # Voice activity detection
SILENCE_THRESHOLD = 0.3          # Pause detection threshold
```

## 🌐 API Usage

### REST API Endpoints

#### Start Transcription
```bash
POST /api/v1/start-stream
```

#### Get Current Text
```bash
GET /api/v1/stream/{stream_id}/text
```

#### Stop Transcription
```bash
POST /api/v1/stream/{stream_id}/stop
```

### WebSocket API
```javascript
const ws = new WebSocket('ws://localhost:8000/ws/transcribe');
ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    console.log('Transcription:', data.text);
    console.log('Emotion:', data.emotion);
};
```

## 📊 Output Format

```
"Hello, (pause:0.3s) how are you doing today? Umm (filler) I'm doing great! (happy:85%) 
Thanks for asking. (pause:0.7s) What about you? (curious:72%)"
```

### Metadata Tags
- `(pause:0.3s)` - Pause duration in seconds
- `(happy:85%)` - Emotion with confidence percentage
- `(filler)` - Detected filler words
- Timestamps and confidence scores available via API

## 🔧 Command Line Options

```bash
python main.py --help

Options:
  --no-api          Disable API server
  --model TEXT      Override model selection
  --device TEXT     Override device (cuda/cpu)
  --debug           Enable debug mode
  --profile         Enable performance profiling
```

## 📈 Performance Targets

- **Transcription Accuracy**: >95% for clear speech
- **Emotion Detection**: >80% accuracy
- **Pause Detection**: <50ms error margin
- **End-to-end Latency**: <200ms
- **GPU Memory Usage**: <4GB VRAM
- **CPU Usage**: <50% on recommended hardware

## 🧪 Testing

```bash
# Run all tests
python -m pytest tests/

# Run specific test
python -m pytest tests/test_voice_processor.py

# Run benchmarks
python scripts/benchmark.py
```

## 🛠️ Development

### Setup Development Environment
```bash
pip install -r requirements.txt
pip install -e .
```

### Code Formatting
```bash
black .
flake8 .
```

## 📚 Documentation

- [API Documentation](docs/API.md)
- [Setup Guide](docs/SETUP.md)
- [Usage Examples](docs/USAGE.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- OpenAI Whisper for speech recognition
- RealtimeSTT for real-time processing
- Silero VAD for voice activity detection
- FastAPI for API framework

## 📞 Support

For issues and questions:
- Create an issue on GitHub
- Check the documentation in `docs/`
- Review the configuration in `config.py`

---

**Built with ❤️ for real-time voice processing**
