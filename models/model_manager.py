"""
AI Model Manager
Handles loading, caching, and optimization of AI models for the voice-to-text system
"""

import asyncio
import os
import time
from pathlib import Path
from typing import Dict, Any, Optional
import threading

import torch
import whisper
from transformers import pipeline

from config import ModelConfig, MODELS_DIR
from utils import get_logger

class ModelManager:
    """
    Manages AI models with memory optimization and caching
    Optimized for GTX 1660 Ti (6GB VRAM)
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.models = {}
        self.model_lock = threading.Lock()
        
        # Model paths
        self.models_dir = MODELS_DIR
        self.models_dir.mkdir(exist_ok=True)
        
        # Memory management
        self.max_vram_usage = ModelConfig.MAX_VRAM_USAGE
        self.current_vram_usage = 0
        
        # Model status
        self.initialization_status = {
            'whisper_primary': False,
            'whisper_realtime': False,
            'emotion_detector': False
        }
        
        self.logger.info("ModelManager initialized")
    
    async def initialize(self):
        """Initialize all required models"""
        try:
            self.logger.info("Starting model initialization...")
            
            # Check GPU availability
            self._check_gpu_status()
            
            # Load models in optimal order (largest first for memory management)
            await self._load_whisper_models()
            await self._load_emotion_model()
            
            # Verify all models loaded successfully
            self._verify_models()
            
            self.logger.info("All models initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Model initialization failed: {str(e)}", exc_info=True)
            return False
    
    def _check_gpu_status(self):
        """Check GPU status and available memory"""
        if torch.cuda.is_available():
            gpu_props = torch.cuda.get_device_properties(0)
            total_memory = gpu_props.total_memory / (1024**3)
            
            self.logger.info(f"GPU: {gpu_props.name}")
            self.logger.info(f"Total VRAM: {total_memory:.1f}GB")
            self.logger.info(f"CUDA Version: {torch.version.cuda}")
            
            # Check current memory usage
            allocated = torch.cuda.memory_allocated(0) / (1024**3)
            reserved = torch.cuda.memory_reserved(0) / (1024**3)
            
            self.logger.info(f"Current VRAM usage: {allocated:.2f}GB allocated, {reserved:.2f}GB reserved")
            self.current_vram_usage = allocated
            
        else:
            self.logger.warning("CUDA not available - using CPU mode")
            ModelConfig.DEVICE = "cpu"
    
    async def _load_whisper_models(self):
        """Load Whisper models for speech recognition"""
        try:
            self.logger.info("Loading Whisper models...")
            
            # Load primary model
            self.logger.info(f"Loading primary Whisper model: {ModelConfig.PRIMARY_MODEL}")
            start_time = time.time()
            
            primary_model = whisper.load_model(
                ModelConfig.PRIMARY_MODEL,
                device=ModelConfig.DEVICE
            )
            
            # Move to half precision if using GPU
            if ModelConfig.DEVICE == "cuda" and ModelConfig.COMPUTE_TYPE == "float16":
                primary_model = primary_model.half()
            
            self.models['whisper_primary'] = primary_model
            load_time = time.time() - start_time
            
            self.logger.info(f"Primary Whisper model loaded in {load_time:.2f}s")
            self.initialization_status['whisper_primary'] = True
            
            # Update VRAM usage
            if torch.cuda.is_available():
                current_allocated = torch.cuda.memory_allocated(0) / (1024**3)
                model_memory = current_allocated - self.current_vram_usage
                self.current_vram_usage = current_allocated
                self.logger.info(f"Primary model VRAM usage: {model_memory:.2f}GB")
            
            # Load realtime model (smaller, faster)
            self.logger.info("Loading realtime Whisper model: tiny.en")
            start_time = time.time()
            
            realtime_model = whisper.load_model("tiny.en", device=ModelConfig.DEVICE)
            
            if ModelConfig.DEVICE == "cuda" and ModelConfig.COMPUTE_TYPE == "float16":
                realtime_model = realtime_model.half()
            
            self.models['whisper_realtime'] = realtime_model
            load_time = time.time() - start_time
            
            self.logger.info(f"Realtime Whisper model loaded in {load_time:.2f}s")
            self.initialization_status['whisper_realtime'] = True
            
            # Update VRAM usage
            if torch.cuda.is_available():
                current_allocated = torch.cuda.memory_allocated(0) / (1024**3)
                model_memory = current_allocated - self.current_vram_usage
                self.current_vram_usage = current_allocated
                self.logger.info(f"Realtime model VRAM usage: {model_memory:.2f}GB")
                self.logger.info(f"Total VRAM usage: {self.current_vram_usage:.2f}GB")
            
        except Exception as e:
            self.logger.error(f"Failed to load Whisper models: {str(e)}", exc_info=True)
            raise
    
    async def _load_emotion_model(self):
        """Load emotion detection model"""
        try:
            self.logger.info("Loading emotion detection model...")
            
            # Check if custom emotion model exists
            custom_model_path = self.models_dir / "emotion_models" / "emotion_classifier.pkl"
            
            if custom_model_path.exists():
                self.logger.info("Loading custom emotion model...")
                # Load custom model (implementation depends on your specific model)
                # For now, we'll use a transformer-based approach
                pass
            
            # Use transformer-based emotion detection as fallback
            self.logger.info("Loading transformer-based emotion classifier...")
            start_time = time.time()
            
            # Use a lightweight emotion classification model
            emotion_classifier = pipeline(
                "text-classification",
                model="j-hartmann/emotion-english-distilroberta-base",
                device=0 if ModelConfig.DEVICE == "cuda" else -1,
                torch_dtype=torch.float16 if ModelConfig.COMPUTE_TYPE == "float16" else torch.float32
            )
            
            self.models['emotion_detector'] = emotion_classifier
            load_time = time.time() - start_time
            
            self.logger.info(f"Emotion model loaded in {load_time:.2f}s")
            self.initialization_status['emotion_detector'] = True
            
            # Update VRAM usage
            if torch.cuda.is_available():
                current_allocated = torch.cuda.memory_allocated(0) / (1024**3)
                model_memory = current_allocated - self.current_vram_usage
                self.current_vram_usage = current_allocated
                self.logger.info(f"Emotion model VRAM usage: {model_memory:.2f}GB")
                self.logger.info(f"Total VRAM usage: {self.current_vram_usage:.2f}GB")
            
        except Exception as e:
            self.logger.error(f"Failed to load emotion model: {str(e)}", exc_info=True)
            # Continue without emotion detection if model fails to load
            self.logger.warning("Continuing without emotion detection")
    
    def _verify_models(self):
        """Verify all models are loaded correctly"""
        required_models = ['whisper_primary', 'whisper_realtime']
        optional_models = ['emotion_detector']
        
        # Check required models
        for model_name in required_models:
            if not self.initialization_status.get(model_name, False):
                raise RuntimeError(f"Required model '{model_name}' failed to load")
        
        # Log optional model status
        for model_name in optional_models:
            status = self.initialization_status.get(model_name, False)
            self.logger.info(f"Optional model '{model_name}': {'✅ Loaded' if status else '❌ Not loaded'}")
        
        # Final memory check
        if torch.cuda.is_available():
            self.logger.info(f"Final VRAM usage: {self.current_vram_usage:.2f}GB / {self.max_vram_usage:.1f}GB limit")
            
            if self.current_vram_usage > self.max_vram_usage:
                self.logger.warning(f"VRAM usage exceeds recommended limit!")
    
    def get_model(self, model_name: str):
        """Get a loaded model by name"""
        with self.model_lock:
            if model_name not in self.models:
                raise ValueError(f"Model '{model_name}' not found or not loaded")
            return self.models[model_name]
    
    def is_model_loaded(self, model_name: str) -> bool:
        """Check if a model is loaded"""
        return self.initialization_status.get(model_name, False)
    
    def get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage statistics"""
        memory_info = {
            'current_vram_gb': 0,
            'total_vram_gb': 0,
            'usage_percentage': 0,
            'available_vram_gb': 0
        }
        
        if torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated(0) / (1024**3)
            reserved = torch.cuda.memory_reserved(0) / (1024**3)
            total = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            
            memory_info.update({
                'current_vram_gb': allocated,
                'reserved_vram_gb': reserved,
                'total_vram_gb': total,
                'usage_percentage': (allocated / total) * 100,
                'available_vram_gb': total - allocated
            })
        
        return memory_info
    
    def optimize_memory(self):
        """Optimize GPU memory usage"""
        if torch.cuda.is_available():
            try:
                # Clear cache
                torch.cuda.empty_cache()
                
                # Force garbage collection
                import gc
                gc.collect()
                
                self.logger.info("GPU memory optimized")
                
                # Log new memory usage
                memory_info = self.get_memory_usage()
                self.logger.info(f"Memory after optimization: {memory_info['current_vram_gb']:.2f}GB")
                
            except Exception as e:
                self.logger.error(f"Error optimizing memory: {str(e)}")
    
    def unload_model(self, model_name: str):
        """Unload a specific model to free memory"""
        with self.model_lock:
            if model_name in self.models:
                del self.models[model_name]
                self.initialization_status[model_name] = False
                
                # Optimize memory after unloading
                self.optimize_memory()
                
                self.logger.info(f"Model '{model_name}' unloaded")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about loaded models"""
        return {
            'loaded_models': list(self.models.keys()),
            'initialization_status': self.initialization_status.copy(),
            'memory_usage': self.get_memory_usage(),
            'device': ModelConfig.DEVICE,
            'compute_type': ModelConfig.COMPUTE_TYPE
        }
    
    def shutdown(self):
        """Shutdown model manager and free all resources"""
        self.logger.info("Shutting down ModelManager...")
        
        with self.model_lock:
            # Unload all models
            for model_name in list(self.models.keys()):
                del self.models[model_name]
            
            self.models.clear()
            
            # Reset status
            for key in self.initialization_status:
                self.initialization_status[key] = False
        
        # Final memory cleanup
        self.optimize_memory()
        
        self.logger.info("ModelManager shutdown complete")
