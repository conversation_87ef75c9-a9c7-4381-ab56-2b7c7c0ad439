#!/usr/bin/env python3
"""
Clean Typing Mode Voice-to-Text
Shows only transcribed text in a typing style without any status indicators
"""

import asyncio
import signal
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.voice_processor import VoiceProcessor
from models.model_manager import ModelManager
from ui.console_display import ConsoleDisplay
from ui.status_monitor import StatusMonitor
from utils.performance import PerformanceMonitor
from utils import get_logger

class TypingModeSTT:
    """Clean typing-style voice-to-text interface"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Initialize components with clean mode
        self.console_display = ConsoleDisplay(clean_mode=True)
        self.status_monitor = StatusMonitor()
        self.performance_monitor = PerformanceMonitor()
        self.model_manager = ModelManager()
        
        # Initialize voice processor
        self.voice_processor = VoiceProcessor(
            model_manager=self.model_manager,
            console_display=self.console_display,
            status_monitor=self.status_monitor,
            performance_monitor=self.performance_monitor
        )
        
        # State
        self.is_running = False
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        self.logger.info("TypingModeSTT initialized")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.logger.info(f"Received signal {signum}, shutting down...")
        self.is_running = False
    
    async def initialize(self):
        """Initialize all components"""
        try:
            self.logger.info("Initializing typing mode STT...")
            
            # Initialize model manager
            if not await self.model_manager.initialize():
                self.logger.error("Failed to initialize model manager")
                return False
            
            # Initialize voice processor
            if not await self.voice_processor.initialize():
                self.logger.error("Failed to initialize voice processor")
                return False
            
            # Start console display
            self.console_display.start_display()
            
            # Start performance monitoring
            self.performance_monitor.start()
            
            self.logger.info("Typing mode STT initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize: {str(e)}", exc_info=True)
            return False
    
    async def run(self):
        """Run the typing mode STT system"""
        if not await self.initialize():
            self.logger.error("Initialization failed")
            return
        
        try:
            self.is_running = True
            self.logger.info("Starting typing mode STT...")
            
            # Clear screen and start clean
            os.system('cls' if os.name == 'nt' else 'clear')
            
            # Start voice processing
            await self.voice_processor.start()
            
        except KeyboardInterrupt:
            self.logger.info("Received keyboard interrupt")
        except Exception as e:
            self.logger.error(f"Error in main loop: {str(e)}", exc_info=True)
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Shutdown all components"""
        try:
            self.logger.info("Shutting down typing mode STT...")
            self.is_running = False
            
            # Stop voice processor
            if self.voice_processor:
                self.voice_processor.stop()
            
            # Stop console display
            if self.console_display:
                self.console_display.stop_display()
            
            # Stop performance monitor
            if self.performance_monitor:
                self.performance_monitor.stop()
            
            self.logger.info("Typing mode STT shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {str(e)}")

async def main():
    """Main entry point"""
    try:
        # Create and run typing mode STT
        stt_system = TypingModeSTT()
        await stt_system.run()
        
    except Exception as e:
        print(f"Fatal error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    # Hide cursor for clean display
    print("\033[?25l", end='')  # Hide cursor
    
    try:
        asyncio.run(main())
    finally:
        # Show cursor on exit
        print("\033[?25h", end='')  # Show cursor
        print()  # New line before exit
