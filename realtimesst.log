2025-09-08 16:29:45.363 - RealTimeSTT: realtimestt - INFO - Starting RealTimeSTT
2025-09-08 16:29:45.376 - RealTimeSTT: realtimestt - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-09-08 16:29:45.381 - RealTimeSTT: realtimestt - INFO - Initializing faster_whisper realtime transcription model tiny.en, default device: cuda, compute type: float16, device index: 0, download root: None
2025-09-08 16:29:58.201 - RealTimeSTT: realtimestt - ERROR - Error initializing faster_whisper realtime transcription model: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 746, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 647, in __init__
    self.model = ctranslate2.models.Whisper(
RuntimeError: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
2025-09-08 16:47:09.498 - RealTimeSTT: realtimestt - INFO - Starting RealTimeSTT
2025-09-08 16:47:09.505 - RealTimeSTT: realtimestt - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-09-08 16:47:09.510 - RealTimeSTT: realtimestt - INFO - Initializing faster_whisper realtime transcription model tiny.en, default device: cuda, compute type: float16, device index: 0, download root: None
2025-09-08 16:47:09.787 - RealTimeSTT: realtimestt - ERROR - Error initializing faster_whisper realtime transcription model: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 746, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 647, in __init__
    self.model = ctranslate2.models.Whisper(
RuntimeError: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
2025-09-08 17:22:50.951 - RealTimeSTT: root - INFO - Starting RealTimeSTT
2025-09-08 17:22:50.961 - RealTimeSTT: root - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-09-08 17:22:50.966 - RealTimeSTT: root - INFO - Initializing faster_whisper realtime transcription model tiny.en
2025-09-08 17:22:51.083 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 12737MB
2025-09-08 17:22:51.631 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "GET /api/models/Systran/faster-whisper-tiny.en/revision/main HTTP/1.1" 200 947
2025-09-08 17:22:51.660 - RealTimeSTT: root - ERROR - Error initializing faster_whisper realtime transcription model: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 672, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 145, in __init__
    self.model = ctranslate2.models.Whisper(
RuntimeError: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
2025-09-08 17:22:51.663 - RealTimeSTT: core.voice_processor - [31mERROR[0m - Failed to initialize recorder: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 672, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 145, in __init__
    self.model = ctranslate2.models.Whisper(
RuntimeError: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
2025-09-08 17:22:51.665 - RealTimeSTT: core.voice_processor - [31mERROR[0m - Failed to initialize voice processor: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 88, in initialize
    await self._initialize_recorder()
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 672, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 145, in __init__
    self.model = ctranslate2.models.Whisper(
RuntimeError: CUDA failed with error CUDA driver version is insufficient for CUDA runtime version
2025-09-08 17:22:51.666 - RealTimeSTT: __main__ - [32mINFO[0m - System shutdown initiated
2025-09-08 17:22:51.667 - RealTimeSTT: ui.status_monitor - [32mINFO[0m - Stopping status monitoring...
2025-09-08 17:22:52.753 - RealTimeSTT: ui.status_monitor - [32mINFO[0m - Status monitoring stopped
2025-09-08 17:22:52.754 - RealTimeSTT: utils.performance - [32mINFO[0m - Stopping performance monitoring...
2025-09-08 17:22:56.086 - RealTimeSTT: utils.performance - [32mINFO[0m - Performance monitoring stopped
2025-09-08 17:22:56.086 - RealTimeSTT: models.model_manager - [32mINFO[0m - Shutting down ModelManager...
2025-09-08 17:22:56.275 - RealTimeSTT: models.model_manager - [32mINFO[0m - GPU memory optimized
2025-09-08 17:22:56.276 - RealTimeSTT: models.model_manager - [32mINFO[0m - Memory after optimization: 0.00GB
2025-09-08 17:22:56.277 - RealTimeSTT: models.model_manager - [32mINFO[0m - ModelManager shutdown complete
2025-09-08 17:22:56.278 - RealTimeSTT: __main__ - [32mINFO[0m - System shutdown completed
2025-09-08 17:24:11.782 - RealTimeSTT: root - INFO - Starting RealTimeSTT
2025-09-08 17:24:11.827 - RealTimeSTT: root - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-09-08 17:24:11.836 - RealTimeSTT: root - INFO - Initializing faster_whisper realtime transcription model tiny.en
2025-09-08 17:24:12.114 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "GET /api/models/Systran/faster-whisper-tiny.en/revision/main HTTP/1.1" 200 947
2025-09-08 17:24:12.159 - RealTimeSTT: root - ERROR - Error initializing faster_whisper realtime transcription model: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 672, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 145, in __init__
    self.model = ctranslate2.models.Whisper(
TypeError: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
2025-09-08 17:24:12.162 - RealTimeSTT: core.voice_processor - [31mERROR[0m - Failed to initialize recorder: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 672, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 145, in __init__
    self.model = ctranslate2.models.Whisper(
TypeError: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
2025-09-08 17:24:12.163 - RealTimeSTT: core.voice_processor - [31mERROR[0m - Failed to initialize voice processor: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 88, in initialize
    await self._initialize_recorder()
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 672, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 145, in __init__
    self.model = ctranslate2.models.Whisper(
TypeError: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
2025-09-08 17:24:12.164 - RealTimeSTT: __main__ - [32mINFO[0m - System shutdown initiated
2025-09-08 17:24:12.165 - RealTimeSTT: ui.status_monitor - [32mINFO[0m - Stopping status monitoring...
2025-09-08 17:24:13.090 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 13023MB
2025-09-08 17:24:13.214 - RealTimeSTT: ui.status_monitor - [32mINFO[0m - Status monitoring stopped
2025-09-08 17:24:13.215 - RealTimeSTT: utils.performance - [32mINFO[0m - Stopping performance monitoring...
2025-09-08 17:24:18.101 - RealTimeSTT: utils.performance - [32mINFO[0m - Performance monitoring stopped
2025-09-08 17:24:18.101 - RealTimeSTT: models.model_manager - [32mINFO[0m - Shutting down ModelManager...
2025-09-08 17:24:18.482 - RealTimeSTT: models.model_manager - [32mINFO[0m - GPU memory optimized
2025-09-08 17:24:18.483 - RealTimeSTT: models.model_manager - [32mINFO[0m - Memory after optimization: 0.00GB
2025-09-08 17:24:18.483 - RealTimeSTT: models.model_manager - [32mINFO[0m - ModelManager shutdown complete
2025-09-08 17:24:18.484 - RealTimeSTT: __main__ - [32mINFO[0m - System shutdown completed
2025-09-08 18:21:35.296 - RealTimeSTT: root - INFO - Starting RealTimeSTT
2025-09-08 18:21:35.302 - RealTimeSTT: root - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-09-08 18:21:35.306 - RealTimeSTT: root - INFO - Initializing faster_whisper realtime transcription model tiny.en
2025-09-08 18:21:35.559 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "GET /api/models/Systran/faster-whisper-tiny.en/revision/main HTTP/1.1" 200 947
2025-09-08 18:21:35.565 - RealTimeSTT: root - ERROR - Error initializing faster_whisper realtime transcription model: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 672, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 145, in __init__
    self.model = ctranslate2.models.Whisper(
TypeError: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
2025-09-08 18:21:35.594 - RealTimeSTT: core.voice_processor - [31mERROR[0m - Failed to initialize recorder: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 672, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 145, in __init__
    self.model = ctranslate2.models.Whisper(
TypeError: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
2025-09-08 18:21:35.597 - RealTimeSTT: core.voice_processor - [31mERROR[0m - Failed to initialize voice processor: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 88, in initialize
    await self._initialize_recorder()
  File "C:\Users\<USER>\Desktop\realstt\core\voice_processor.py", line 131, in _initialize_recorder
    self.recorder = AudioToTextRecorder(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\RealtimeSTT\audio_recorder.py", line 672, in __init__
    self.realtime_model_type = faster_whisper.WhisperModel(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\faster_whisper\transcribe.py", line 145, in __init__
    self.model = ctranslate2.models.Whisper(
TypeError: __init__(): incompatible constructor arguments. The following argument types are supported:
    1. ctranslate2._ext.Whisper(model_path: str, device: str = 'cpu', *, device_index: Union[int, List[int]] = 0, compute_type: Union[str, Dict[str, str]] = 'default', inter_threads: int = 1, intra_threads: int = 0, max_queued_batches: int = 0, flash_attention: bool = False, tensor_parallel: bool = False, files: object = None)

Invoked with: 'C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--Systran--faster-whisper-tiny.en\\snapshots\\0d3d19a32d3338f10357c0889762bd8d64bbdeba'; kwargs: device='cpu', device_index=None, compute_type='float32', intra_threads=0, inter_threads=1, files=None
2025-09-08 18:21:35.599 - RealTimeSTT: __main__ - [32mINFO[0m - System shutdown initiated
2025-09-08 18:21:35.599 - RealTimeSTT: ui.status_monitor - [32mINFO[0m - Stopping status monitoring...
2025-09-08 18:21:35.865 - RealTimeSTT: ui.status_monitor - [32mINFO[0m - Status monitoring stopped
2025-09-08 18:21:35.865 - RealTimeSTT: utils.performance - [32mINFO[0m - Stopping performance monitoring...
2025-09-08 18:21:38.511 - RealTimeSTT: utils.performance - [32mINFO[0m - Performance monitoring stopped
2025-09-08 18:21:38.511 - RealTimeSTT: models.model_manager - [32mINFO[0m - Shutting down ModelManager...
2025-09-08 18:21:38.735 - RealTimeSTT: models.model_manager - [32mINFO[0m - GPU memory optimized
2025-09-08 18:21:38.736 - RealTimeSTT: models.model_manager - [32mINFO[0m - Memory after optimization: 0.00GB
2025-09-08 18:21:38.736 - RealTimeSTT: models.model_manager - [32mINFO[0m - ModelManager shutdown complete
2025-09-08 18:21:38.736 - RealTimeSTT: __main__ - [32mINFO[0m - System shutdown completed
2025-09-08 18:22:35.893 - RealTimeSTT: root - INFO - Starting RealTimeSTT
2025-09-08 18:22:35.899 - RealTimeSTT: root - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-09-08 18:22:35.902 - RealTimeSTT: root - INFO - Initializing faster_whisper realtime transcription model tiny.en
2025-09-08 18:22:36.153 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "GET /api/models/Systran/faster-whisper-tiny.en/revision/main HTTP/1.1" 200 947
2025-09-08 18:22:36.487 - RealTimeSTT: root - DEBUG - Faster_whisper realtime speech to text transcription model initialized successfully
2025-09-08 18:22:36.487 - RealTimeSTT: root - INFO - Initializing WebRTC voice with Sensitivity 2
2025-09-08 18:22:36.487 - RealTimeSTT: root - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-09-08 18:22:37.870 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 13303MB
2025-09-08 18:22:38.145 - RealTimeSTT: root - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-09-08 18:22:38.146 - RealTimeSTT: root - DEBUG - Starting realtime worker
2025-09-08 18:22:38.146 - RealTimeSTT: root - DEBUG - Waiting for main transcription model to start
2025-09-08 18:22:42.985 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14528MB
2025-09-08 18:22:43.022 - RealTimeSTT: root - DEBUG - Main transcription model ready
2025-09-08 18:22:43.023 - RealTimeSTT: root - DEBUG - RealtimeSTT initialization completed successfully
2025-09-08 18:22:43.024 - RealTimeSTT: core.voice_processor - [32mINFO[0m - RealtimeSTT recorder configured successfully
2025-09-08 18:22:43.025 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Initializing emotion detection models...
2025-09-08 18:22:43.025 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Loading text emotion classifier...
2025-09-08 18:22:43.280 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "HEAD /j-hartmann/emotion-english-distilroberta-base/resolve/main/config.json HTTP/1.1" 307 0
2025-09-08 18:22:43.320 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "HEAD /api/resolve-cache/models/j-hartmann/emotion-english-distilroberta-base/0e1cd914e3d46199ed785853e12b57304e04178b/config.json HTTP/1.1" 200 0
2025-09-08 18:22:43.584 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "HEAD /j-hartmann/emotion-english-distilroberta-base/resolve/main/model.safetensors HTTP/1.1" 404 0
2025-09-08 18:22:44.205 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Text emotion classifier loaded successfully
2025-09-08 18:22:44.205 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Custom audio model not found, creating feature-based classifier...
2025-09-08 18:22:44.206 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Creating feature-based audio emotion classifier...
2025-09-08 18:22:44.206 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Emotion detection models initialized successfully
2025-09-08 18:22:44.206 - RealTimeSTT: core.text_formatter - [32mINFO[0m - TextFormatter ready for processing
2025-09-08 18:22:44.207 - RealTimeSTT: core.voice_processor - [32mINFO[0m - Voice processor initialization completed
2025-09-08 18:22:44.207 - RealTimeSTT: ui.status_monitor - DEBUG - Status callback added
2025-09-08 18:22:44.207 - RealTimeSTT: __main__ - DEBUG - System callbacks configured
2025-09-08 18:22:44.208 - RealTimeSTT: __main__ - [32mINFO[0m - System initialization completed
2025-09-08 18:22:44.208 - RealTimeSTT: __main__ - [31mERROR[0m - System error: 'VoiceToTextSystem' object has no attribute 'start_console_mode'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\main.py", line 283, in main
    await system.start_console_mode()
AttributeError: 'VoiceToTextSystem' object has no attribute 'start_console_mode'
2025-09-08 18:22:44.237 - RealTimeSTT: __main__ - [32mINFO[0m - System shutdown initiated
2025-09-08 18:22:44.237 - RealTimeSTT: ui.status_monitor - [32mINFO[0m - Stopping status monitoring...
2025-09-08 18:22:44.772 - RealTimeSTT: ui.status_monitor - [32mINFO[0m - Status monitoring stopped
2025-09-08 18:22:44.772 - RealTimeSTT: utils.performance - [32mINFO[0m - Stopping performance monitoring...
2025-09-08 18:22:48.000 - RealTimeSTT: utils.performance - [32mINFO[0m - Performance monitoring stopped
2025-09-08 18:22:48.001 - RealTimeSTT: models.model_manager - [32mINFO[0m - Shutting down ModelManager...
2025-09-08 18:22:48.394 - RealTimeSTT: models.model_manager - [32mINFO[0m - GPU memory optimized
2025-09-08 18:22:48.395 - RealTimeSTT: models.model_manager - [32mINFO[0m - Memory after optimization: 0.15GB
2025-09-08 18:22:48.395 - RealTimeSTT: models.model_manager - [32mINFO[0m - ModelManager shutdown complete
2025-09-08 18:22:48.396 - RealTimeSTT: __main__ - [32mINFO[0m - System shutdown completed
2025-09-08 18:23:55.450 - RealTimeSTT: root - DEBUG - Receive from stdout pipe
2025-09-08 18:29:56.695 - RealTimeSTT: root - INFO - Starting RealTimeSTT
2025-09-08 18:29:56.700 - RealTimeSTT: root - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-09-08 18:29:56.703 - RealTimeSTT: root - INFO - Initializing faster_whisper realtime transcription model tiny.en
2025-09-08 18:29:56.956 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "GET /api/models/Systran/faster-whisper-tiny.en/revision/main HTTP/1.1" 200 947
2025-09-08 18:29:57.227 - RealTimeSTT: root - DEBUG - Faster_whisper realtime speech to text transcription model initialized successfully
2025-09-08 18:29:57.227 - RealTimeSTT: root - INFO - Initializing WebRTC voice with Sensitivity 2
2025-09-08 18:29:57.227 - RealTimeSTT: root - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-09-08 18:29:58.663 - RealTimeSTT: root - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-09-08 18:29:58.664 - RealTimeSTT: root - DEBUG - Starting realtime worker
2025-09-08 18:29:58.664 - RealTimeSTT: root - DEBUG - Waiting for main transcription model to start
2025-09-08 18:29:59.982 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 13176MB
2025-09-08 18:30:03.774 - RealTimeSTT: root - DEBUG - Main transcription model ready
2025-09-08 18:30:03.775 - RealTimeSTT: root - DEBUG - RealtimeSTT initialization completed successfully
2025-09-08 18:30:03.775 - RealTimeSTT: core.voice_processor - [32mINFO[0m - RealtimeSTT recorder configured successfully
2025-09-08 18:30:03.775 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Initializing emotion detection models...
2025-09-08 18:30:03.776 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Loading text emotion classifier...
2025-09-08 18:30:04.080 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "HEAD /j-hartmann/emotion-english-distilroberta-base/resolve/main/config.json HTTP/1.1" 307 0
2025-09-08 18:30:04.120 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "HEAD /api/resolve-cache/models/j-hartmann/emotion-english-distilroberta-base/0e1cd914e3d46199ed785853e12b57304e04178b/config.json HTTP/1.1" 200 0
2025-09-08 18:30:04.375 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "HEAD /j-hartmann/emotion-english-distilroberta-base/resolve/main/model.safetensors HTTP/1.1" 404 0
2025-09-08 18:30:04.892 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Text emotion classifier loaded successfully
2025-09-08 18:30:04.893 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Custom audio model not found, creating feature-based classifier...
2025-09-08 18:30:04.894 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Creating feature-based audio emotion classifier...
2025-09-08 18:30:04.894 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Emotion detection models initialized successfully
2025-09-08 18:30:04.894 - RealTimeSTT: core.text_formatter - [32mINFO[0m - TextFormatter ready for processing
2025-09-08 18:30:04.895 - RealTimeSTT: core.voice_processor - [32mINFO[0m - Voice processor initialization completed
2025-09-08 18:30:04.895 - RealTimeSTT: ui.status_monitor - DEBUG - Status callback added
2025-09-08 18:30:04.895 - RealTimeSTT: __main__ - DEBUG - System callbacks configured
2025-09-08 18:30:04.895 - RealTimeSTT: __main__ - [32mINFO[0m - System initialization completed
2025-09-08 18:30:04.897 - RealTimeSTT: __main__ - [31mERROR[0m - Console mode error: 'VoiceProcessor' object has no attribute 'start_listening'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\main.py", line 259, in start_console_mode
    await self.voice_processor.start_listening()
AttributeError: 'VoiceProcessor' object has no attribute 'start_listening'
2025-09-08 18:30:04.899 - RealTimeSTT: __main__ - [32mINFO[0m - System shutdown initiated
2025-09-08 18:30:04.899 - RealTimeSTT: ui.status_monitor - [32mINFO[0m - Stopping status monitoring...
2025-09-08 18:30:05.083 - RealTimeSTT: ui.status_monitor - [32mINFO[0m - Status monitoring stopped
2025-09-08 18:30:05.083 - RealTimeSTT: utils.performance - [32mINFO[0m - Stopping performance monitoring...
2025-09-08 18:30:05.103 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14333MB
2025-09-08 18:30:10.088 - RealTimeSTT: utils.performance - [32mINFO[0m - Performance monitoring stopped
2025-09-08 18:30:10.088 - RealTimeSTT: models.model_manager - [32mINFO[0m - Shutting down ModelManager...
2025-09-08 18:30:10.261 - RealTimeSTT: models.model_manager - [32mINFO[0m - GPU memory optimized
2025-09-08 18:30:10.262 - RealTimeSTT: models.model_manager - [32mINFO[0m - Memory after optimization: 0.15GB
2025-09-08 18:30:10.263 - RealTimeSTT: models.model_manager - [32mINFO[0m - ModelManager shutdown complete
2025-09-08 18:30:10.264 - RealTimeSTT: __main__ - [32mINFO[0m - System shutdown completed
2025-09-08 18:30:43.966 - RealTimeSTT: root - DEBUG - Receive from stdout pipe
2025-09-08 18:31:59.668 - RealTimeSTT: root - INFO - Starting RealTimeSTT
2025-09-08 18:31:59.672 - RealTimeSTT: root - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-09-08 18:31:59.677 - RealTimeSTT: root - INFO - Initializing faster_whisper realtime transcription model tiny.en
2025-09-08 18:31:59.988 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "GET /api/models/Systran/faster-whisper-tiny.en/revision/main HTTP/1.1" 200 947
2025-09-08 18:32:00.317 - RealTimeSTT: root - DEBUG - Faster_whisper realtime speech to text transcription model initialized successfully
2025-09-08 18:32:00.318 - RealTimeSTT: root - INFO - Initializing WebRTC voice with Sensitivity 2
2025-09-08 18:32:00.318 - RealTimeSTT: root - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-09-08 18:32:01.898 - RealTimeSTT: root - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-09-08 18:32:01.899 - RealTimeSTT: root - DEBUG - Starting realtime worker
2025-09-08 18:32:01.899 - RealTimeSTT: root - DEBUG - Waiting for main transcription model to start
2025-09-08 18:32:03.521 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 12993MB
2025-09-08 18:32:08.059 - RealTimeSTT: root - DEBUG - Main transcription model ready
2025-09-08 18:32:08.060 - RealTimeSTT: root - DEBUG - RealtimeSTT initialization completed successfully
2025-09-08 18:32:08.060 - RealTimeSTT: core.voice_processor - [32mINFO[0m - RealtimeSTT recorder configured successfully
2025-09-08 18:32:08.061 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Initializing emotion detection models...
2025-09-08 18:32:08.061 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Loading text emotion classifier...
2025-09-08 18:32:08.310 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "HEAD /j-hartmann/emotion-english-distilroberta-base/resolve/main/config.json HTTP/1.1" 307 0
2025-09-08 18:32:08.348 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "HEAD /api/resolve-cache/models/j-hartmann/emotion-english-distilroberta-base/0e1cd914e3d46199ed785853e12b57304e04178b/config.json HTTP/1.1" 200 0
2025-09-08 18:32:08.646 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14131MB
2025-09-08 18:32:08.994 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "HEAD /j-hartmann/emotion-english-distilroberta-base/resolve/main/model.safetensors HTTP/1.1" 404 0
2025-09-08 18:32:09.518 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Text emotion classifier loaded successfully
2025-09-08 18:32:09.519 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Custom audio model not found, creating feature-based classifier...
2025-09-08 18:32:09.519 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Creating feature-based audio emotion classifier...
2025-09-08 18:32:09.519 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Emotion detection models initialized successfully
2025-09-08 18:32:09.520 - RealTimeSTT: core.text_formatter - [32mINFO[0m - TextFormatter ready for processing
2025-09-08 18:32:09.520 - RealTimeSTT: core.voice_processor - [32mINFO[0m - Voice processor initialization completed
2025-09-08 18:32:09.520 - RealTimeSTT: ui.status_monitor - DEBUG - Status callback added
2025-09-08 18:32:09.520 - RealTimeSTT: __main__ - DEBUG - System callbacks configured
2025-09-08 18:32:09.520 - RealTimeSTT: __main__ - [32mINFO[0m - System initialization completed
2025-09-08 18:32:09.522 - RealTimeSTT: core.voice_processor - [32mINFO[0m - Starting voice processor...
2025-09-08 18:32:09.522 - RealTimeSTT: core.voice_processor - [32mINFO[0m - Voice processing loop started
2025-09-08 18:32:09.522 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:32:09.522 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:32:10.179 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:32:13.767 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14181MB
2025-09-08 18:32:18.896 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14228MB
2025-09-08 18:32:24.013 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14263MB
2025-09-08 18:32:29.131 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14284MB
2025-09-08 18:32:34.251 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14349MB
2025-09-08 18:32:39.375 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14342MB
2025-09-08 18:32:44.492 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14156MB
2025-09-08 18:32:46.738 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:32:46.738 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:32:46.738 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:32:46.738 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:32:46.738 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:32:46.738 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:32:46.878 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 9216
2025-09-08 18:32:46.878 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.576
2025-09-08 18:32:46.923 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:32:47.346 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.198005 < -1.000000)
2025-09-08 18:32:47.346 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.745417 > 0.600000)
2025-09-08 18:32:47.347 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:32:47.448 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 18432
2025-09-08 18:32:47.448 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.152
2025-09-08 18:32:47.492 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:32:47.879 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.115881 < -1.000000)
2025-09-08 18:32:47.879 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.814844 > 0.600000)
2025-09-08 18:32:47.880 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:32:47.888 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:32:47.888 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:32:47.888 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:32:47.888 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:32:47.909 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:32:47.910 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:32:47.911 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:32:47.957 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:32:47.987 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 0
2025-09-08 18:32:47.987 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.000
2025-09-08 18:32:48.044 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:32:49.616 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14131MB
2025-09-08 18:32:50.592 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 2.68 seconds
2025-09-08 18:32:50.592 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Yeah. Yeah....
2025-09-08 18:32:52.480 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:32:52.481 - RealTimeSTT: core.emotion_detector - DEBUG - Emotion detected: neutral (1.00)
2025-09-08 18:32:52.481 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:32:52.482 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:32:52.483 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Yeah. Yeah....
2025-09-08 18:32:52.483 - RealTimeSTT: __main__ - [31mERROR[0m - Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:32:52.484 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 1.890s
2025-09-08 18:32:52.506 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:32:52.506 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:32:52.507 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:32:54.733 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14693MB
2025-09-08 18:32:59.861 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14686MB
2025-09-08 18:33:00.758 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:33:00.758 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:33:00.758 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:33:00.758 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:33:00.758 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:33:00.758 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:33:00.867 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:33:00.867 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:33:00.911 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:33:01.286 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.353916 < -1.000000)
2025-09-08 18:33:01.286 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.692335 > 0.600000)
2025-09-08 18:33:01.286 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:33:01.391 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 16384
2025-09-08 18:33:01.391 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.024
2025-09-08 18:33:01.438 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:33:01.803 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.352535 < -1.000000)
2025-09-08 18:33:01.803 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.805396 > 0.600000)
2025-09-08 18:33:01.803 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:33:01.908 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:33:01.908 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:33:01.908 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:33:01.908 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:33:01.915 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 0
2025-09-08 18:33:01.915 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.000
2025-09-08 18:33:01.915 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:33:01.919 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:33:01.930 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:33:01.958 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:33:01.968 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:33:04.401 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 2.48 seconds
2025-09-08 18:33:04.401 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Thank you very much....
2025-09-08 18:33:04.456 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:33:04.457 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:33:04.459 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:33:04.459 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:33:04.460 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Thank you very much....
2025-09-08 18:33:04.460 - RealTimeSTT: __main__ - [31mERROR[0m - Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:33:04.461 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 0.058s
2025-09-08 18:33:04.480 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:33:04.480 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:33:04.481 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:33:04.658 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:33:04.658 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:33:04.658 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:33:04.658 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:33:04.658 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:33:04.659 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:33:04.771 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:33:04.771 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:33:04.818 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:33:04.974 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14699MB
2025-09-08 18:33:05.169 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.363258 < -1.000000)
2025-09-08 18:33:05.169 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.734236 > 0.600000)
2025-09-08 18:33:05.170 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:33:05.274 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 16384
2025-09-08 18:33:05.274 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.024
2025-09-08 18:33:05.316 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:33:05.677 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.111586 < -1.000000)
2025-09-08 18:33:05.677 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.766064 > 0.600000)
2025-09-08 18:33:05.677 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:33:05.784 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 24576
2025-09-08 18:33:05.784 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.536
2025-09-08 18:33:05.809 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:33:05.809 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:33:05.809 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:33:05.815 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:33:05.816 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:33:05.833 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:33:05.846 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:33:05.914 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:33:05.914 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:33:06.327 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.200932 < -1.000000)
2025-09-08 18:33:06.327 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.777357 > 0.600000)
2025-09-08 18:33:06.327 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:33:10.103 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14788MB
2025-09-08 18:33:12.514 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 6.70 seconds
2025-09-08 18:33:12.533 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:33:12.533 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:33:12.534 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:33:15.219 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14671MB
2025-09-08 18:33:20.336 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14662MB
2025-09-08 18:33:25.460 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14673MB
2025-09-08 18:33:30.580 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14703MB
2025-09-08 18:33:35.705 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14710MB
2025-09-08 18:33:40.828 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14708MB
2025-09-08 18:33:45.942 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14671MB
2025-09-08 18:33:51.071 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14666MB
2025-09-08 18:33:56.189 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14684MB
2025-09-08 18:34:01.307 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14671MB
2025-09-08 18:34:06.431 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14674MB
2025-09-08 18:34:10.637 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:34:10.638 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:34:10.638 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:34:10.638 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:34:10.638 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:34:10.638 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:34:10.774 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 9216
2025-09-08 18:34:10.774 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.576
2025-09-08 18:34:10.814 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:34:11.131 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.336310 < -1.000000)
2025-09-08 18:34:11.131 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.626116 > 0.600000)
2025-09-08 18:34:11.132 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:34:11.238 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 16384
2025-09-08 18:34:11.238 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.024
2025-09-08 18:34:11.279 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:34:11.546 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14673MB
2025-09-08 18:34:11.607 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.251897 < -1.000000)
2025-09-08 18:34:11.607 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.714643 > 0.600000)
2025-09-08 18:34:11.607 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:34:11.711 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 23552
2025-09-08 18:34:11.711 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.472
2025-09-08 18:34:11.756 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:34:11.798 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:34:11.798 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:34:11.798 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:34:11.804 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:34:11.806 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:34:11.834 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:34:11.885 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:34:11.885 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:34:12.217 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.220507 < -1.000000)
2025-09-08 18:34:12.217 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.759629 > 0.600000)
2025-09-08 18:34:12.217 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:34:14.369 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 2.56 seconds
2025-09-08 18:34:14.369 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Thank you very much....
2025-09-08 18:34:14.424 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:34:14.425 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:34:14.426 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:34:14.427 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:34:14.428 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Thank you very much....
2025-09-08 18:34:14.428 - RealTimeSTT: __main__ - [31mERROR[0m - Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:34:14.429 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 0.058s
2025-09-08 18:34:14.452 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:34:14.452 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:34:14.453 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:34:16.665 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14665MB
2025-09-08 18:34:21.781 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14666MB
2025-09-08 18:34:26.901 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14670MB
2025-09-08 18:34:32.022 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14667MB
2025-09-08 18:34:36.557 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:34:36.557 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:34:36.557 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:34:36.558 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:34:36.558 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:34:36.558 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:34:36.672 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:34:36.672 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:34:36.713 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:34:37.066 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.393129 < -1.000000)
2025-09-08 18:34:37.066 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.625987 > 0.600000)
2025-09-08 18:34:37.066 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:34:37.134 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14676MB
2025-09-08 18:34:37.176 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 16384
2025-09-08 18:34:37.176 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.024
2025-09-08 18:34:37.220 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:34:37.581 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.230448 < -1.000000)
2025-09-08 18:34:37.582 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.807192 > 0.600000)
2025-09-08 18:34:37.582 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:34:37.684 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 24576
2025-09-08 18:34:37.684 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.536
2025-09-08 18:34:37.718 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:34:37.718 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:34:37.718 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:34:37.718 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:34:37.728 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:34:37.791 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:34:37.792 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:34:37.821 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:34:37.838 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:34:38.183 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.238118 < -1.000000)
2025-09-08 18:34:38.184 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.827378 > 0.600000)
2025-09-08 18:34:38.184 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:34:40.367 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 2.57 seconds
2025-09-08 18:34:40.368 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Thank you very much....
2025-09-08 18:34:40.422 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:34:40.422 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:34:40.423 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:34:40.424 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:34:40.425 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Thank you very much....
2025-09-08 18:34:40.425 - RealTimeSTT: __main__ - [31mERROR[0m - Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:34:40.426 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 0.057s
2025-09-08 18:34:40.438 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:34:40.438 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:34:40.440 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:34:42.253 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14680MB
2025-09-08 18:34:47.368 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14693MB
2025-09-08 18:34:52.482 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14708MB
2025-09-08 18:34:57.608 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14699MB
2025-09-08 18:35:02.732 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14702MB
2025-09-08 18:35:07.849 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14691MB
2025-09-08 18:35:12.966 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14692MB
2025-09-08 18:35:15.596 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:35:15.597 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:35:15.597 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:35:15.597 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:35:15.597 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:35:15.650 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:35:15.710 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:35:15.710 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:35:15.751 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:35:16.115 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.312272 < -1.000000)
2025-09-08 18:35:16.354 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.2 (-1.312272 < -1.000000)
2025-09-08 18:35:16.576 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.4 (-1.219288 < -1.000000)
2025-09-08 18:35:16.758 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:35:16.758 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:35:16.758 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:35:16.773 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:35:16.776 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.6 (-1.238856 < -1.000000)
2025-09-08 18:35:16.798 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:35:16.800 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:35:16.817 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:35:16.829 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:35:17.021 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.8 (-1.213682 < -1.000000)
2025-09-08 18:35:17.393 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 1.0 (-3.296736 < -1.000000)
2025-09-08 18:35:17.394 - RealTimeSTT: faster_whisper - DEBUG - Reset prompt. prompt_reset_on_temperature threshold is met 1.000000 > 0.500000
2025-09-08 18:35:17.394 - RealTimeSTT: root - DEBUG - Realtime text detected:  you
2025-09-08 18:35:18.090 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14915MB
2025-09-08 18:35:19.564 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 2.76 seconds
2025-09-08 18:35:19.564 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: You're welcome, Amy....
2025-09-08 18:35:19.618 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:35:19.619 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:35:19.620 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:35:19.621 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:35:19.622 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: You're welcome, Amy....
2025-09-08 18:35:19.622 - RealTimeSTT: __main__ - [31mERROR[0m - Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:35:19.622 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 0.057s
2025-09-08 18:35:19.645 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:35:19.645 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:35:19.646 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:35:23.203 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14699MB
2025-09-08 18:35:28.325 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14708MB
2025-09-08 18:35:33.451 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14730MB
2025-09-08 18:35:38.565 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14755MB
2025-09-08 18:35:43.685 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14730MB
2025-09-08 18:35:48.810 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14739MB
2025-09-08 18:35:53.930 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14723MB
2025-09-08 18:35:59.048 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14723MB
2025-09-08 18:36:04.164 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14722MB
2025-09-08 18:36:09.291 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14728MB
2025-09-08 18:36:13.396 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:36:13.397 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:36:13.397 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:36:13.397 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:36:13.397 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:36:13.397 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:36:13.530 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 9216
2025-09-08 18:36:13.530 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.576
2025-09-08 18:36:13.571 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:36:13.952 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.300720 < -1.000000)
2025-09-08 18:36:13.952 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.615110 > 0.600000)
2025-09-08 18:36:13.953 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:36:14.055 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 17408
2025-09-08 18:36:14.055 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.088
2025-09-08 18:36:14.102 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:36:14.414 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14740MB
2025-09-08 18:36:14.458 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.125222 < -1.000000)
2025-09-08 18:36:14.459 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.670875 > 0.600000)
2025-09-08 18:36:14.459 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:36:14.547 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:36:14.548 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:36:14.548 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:36:14.548 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:36:14.561 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 0
2025-09-08 18:36:14.561 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.000
2025-09-08 18:36:14.562 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:36:14.567 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:36:14.592 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:36:14.604 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:36:14.607 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:36:17.196 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 2.63 seconds
2025-09-08 18:36:17.208 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:36:17.208 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:36:17.209 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:36:19.535 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14719MB
2025-09-08 18:36:24.649 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14749MB
2025-09-08 18:36:29.767 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14789MB
2025-09-08 18:36:34.889 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14748MB
2025-09-08 18:36:36.687 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:36:36.687 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:36:36.687 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:36:36.687 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:36:36.687 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:36:36.687 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:36:36.800 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:36:36.800 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:36:36.840 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:36:37.269 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.374300 < -1.000000)
2025-09-08 18:36:37.270 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.661216 > 0.600000)
2025-09-08 18:36:37.270 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:36:37.386 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 17408
2025-09-08 18:36:37.386 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.088
2025-09-08 18:36:37.429 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:36:37.814 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.286140 < -1.000000)
2025-09-08 18:36:37.814 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.777278 > 0.600000)
2025-09-08 18:36:37.814 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:36:37.837 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:36:37.838 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:36:37.838 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:36:37.838 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:36:37.843 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:36:37.852 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:36:37.874 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:36:37.907 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:36:37.920 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 0
2025-09-08 18:36:37.920 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.000
2025-09-08 18:36:37.975 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:36:40.008 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14303MB
2025-09-08 18:36:40.378 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 2.53 seconds
2025-09-08 18:36:40.402 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:36:40.402 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:36:40.403 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:36:45.129 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14262MB
2025-09-08 18:36:48.076 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:36:48.077 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:36:48.077 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:36:48.077 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:36:48.077 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:36:48.077 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:36:48.208 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8704
2025-09-08 18:36:48.208 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.544
2025-09-08 18:36:48.249 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:36:48.646 - RealTimeSTT: root - DEBUG - Realtime text detected:  No.
2025-09-08 18:36:48.646 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:36:48.760 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 17408
2025-09-08 18:36:48.760 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.088
2025-09-08 18:36:48.806 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:36:49.184 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.113443 < -1.000000)
2025-09-08 18:36:49.238 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:36:49.238 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:36:49.238 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:36:49.238 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:36:49.296 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:36:49.298 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:36:49.298 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:36:49.327 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:36:49.451 - RealTimeSTT: root - DEBUG - Realtime text detected:  Okay
2025-09-08 18:36:50.252 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14252MB
2025-09-08 18:36:51.998 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 2.70 seconds
2025-09-08 18:36:51.998 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Hello, can you hear me?...
2025-09-08 18:36:52.053 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:36:52.054 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:36:52.055 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:36:52.055 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:36:52.056 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Hello, can you hear me?...
2025-09-08 18:36:52.056 - RealTimeSTT: __main__ - [31mERROR[0m - Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:36:52.056 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 0.057s
2025-09-08 18:36:52.082 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:36:52.082 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:36:52.083 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:36:55.378 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14205MB
2025-09-08 18:37:00.491 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14171MB
2025-09-08 18:37:05.616 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14162MB
2025-09-08 18:37:07.726 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:37:07.727 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:37:07.727 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:37:07.727 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:37:07.727 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:37:07.727 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:37:07.851 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:37:07.851 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:37:07.893 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:37:08.266 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.417073 < -1.000000)
2025-09-08 18:37:08.266 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.709104 > 0.600000)
2025-09-08 18:37:08.266 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:37:08.371 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 17408
2025-09-08 18:37:08.371 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.088
2025-09-08 18:37:08.413 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:37:08.779 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.429364 < -1.000000)
2025-09-08 18:37:08.779 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.793534 > 0.600000)
2025-09-08 18:37:08.779 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:37:08.877 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:37:08.877 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:37:08.878 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:37:08.878 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:37:08.890 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 0
2025-09-08 18:37:08.890 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.000
2025-09-08 18:37:08.932 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:37:08.937 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:37:08.938 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:37:08.946 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:37:08.953 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:37:10.733 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14396MB
2025-09-08 18:37:11.539 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 2.60 seconds
2025-09-08 18:37:11.539 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Thank you very much....
2025-09-08 18:37:11.594 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:37:11.595 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:37:11.595 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:37:11.596 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:37:11.597 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Thank you very much....
2025-09-08 18:37:11.597 - RealTimeSTT: __main__ - [31mERROR[0m - Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:37:11.597 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 0.057s
2025-09-08 18:37:11.617 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:37:11.617 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:37:11.618 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:37:15.852 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14187MB
2025-09-08 18:37:20.968 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14197MB
2025-09-08 18:37:26.092 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14200MB
2025-09-08 18:37:31.211 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14175MB
2025-09-08 18:37:36.328 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14163MB
2025-09-08 18:37:41.455 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14206MB
2025-09-08 18:37:46.573 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14199MB
2025-09-08 18:37:51.689 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14195MB
2025-09-08 18:37:56.804 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14222MB
2025-09-08 18:38:01.807 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:38:01.807 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:38:01.807 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:38:01.807 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:38:01.807 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:38:01.808 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:38:01.926 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14220MB
2025-09-08 18:38:01.937 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:38:01.937 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:38:01.976 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:38:02.332 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.321959 < -1.000000)
2025-09-08 18:38:02.332 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.601300 > 0.600000)
2025-09-08 18:38:02.332 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:38:02.446 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 16384
2025-09-08 18:38:02.446 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.024
2025-09-08 18:38:02.492 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:38:02.860 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.225636 < -1.000000)
2025-09-08 18:38:02.861 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.788315 > 0.600000)
2025-09-08 18:38:02.861 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:38:02.957 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:38:02.958 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:38:02.958 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:38:02.958 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:38:02.967 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 0
2025-09-08 18:38:02.967 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.000
2025-09-08 18:38:02.997 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:38:02.999 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:38:03.010 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:38:03.027 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:38:03.028 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:38:05.668 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 2.67 seconds
2025-09-08 18:38:05.668 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Thank you very much....
2025-09-08 18:38:05.723 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:38:05.724 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:38:05.725 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:38:05.725 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:38:05.726 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Thank you very much....
2025-09-08 18:38:05.726 - RealTimeSTT: __main__ - [31mERROR[0m - Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:38:05.727 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 0.057s
2025-09-08 18:38:05.744 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:38:05.744 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:38:05.745 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:38:07.054 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14290MB
2025-09-08 18:38:12.178 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14332MB
2025-09-08 18:38:17.294 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14297MB
2025-09-08 18:38:22.413 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14580MB
2025-09-08 18:38:27.538 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15899MB
2025-09-08 18:38:32.652 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 13943MB
2025-09-08 18:38:37.771 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15750MB
2025-09-08 18:38:42.895 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 16049MB
2025-09-08 18:38:48.097 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15796MB
2025-09-08 18:38:53.261 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15611MB
2025-09-08 18:38:58.379 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15350MB
2025-09-08 18:39:03.506 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15124MB
2025-09-08 18:39:08.635 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15348MB
2025-09-08 18:39:13.758 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14957MB
2025-09-08 18:39:14.077 - RealTimeSTT: root - INFO - Starting RealTimeSTT
2025-09-08 18:39:14.127 - RealTimeSTT: root - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-09-08 18:39:14.128 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14942MB
2025-09-08 18:39:14.131 - RealTimeSTT: root - INFO - Initializing faster_whisper realtime transcription model tiny.en
2025-09-08 18:39:14.456 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "GET /api/models/Systran/faster-whisper-tiny.en/revision/main HTTP/1.1" 200 947
2025-09-08 18:39:14.897 - RealTimeSTT: root - DEBUG - Faster_whisper realtime speech to text transcription model initialized successfully
2025-09-08 18:39:14.898 - RealTimeSTT: root - INFO - Initializing WebRTC voice with Sensitivity 2
2025-09-08 18:39:14.898 - RealTimeSTT: root - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-09-08 18:39:17.528 - RealTimeSTT: root - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-09-08 18:39:17.530 - RealTimeSTT: root - DEBUG - Starting realtime worker
2025-09-08 18:39:17.530 - RealTimeSTT: root - DEBUG - Waiting for main transcription model to start
2025-09-08 18:39:18.871 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15362MB
2025-09-08 18:39:19.241 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15175MB
2025-09-08 18:39:23.991 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15112MB
2025-09-08 18:39:24.356 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15111MB
2025-09-08 18:39:29.119 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15140MB
2025-09-08 18:39:29.470 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15144MB
2025-09-08 18:39:34.246 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15384MB
2025-09-08 18:39:34.615 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15563MB
2025-09-08 18:39:34.752 - RealTimeSTT: root - DEBUG - Main transcription model ready
2025-09-08 18:39:34.754 - RealTimeSTT: root - DEBUG - RealtimeSTT initialization completed successfully
2025-09-08 18:39:34.754 - RealTimeSTT: core.voice_processor - [32mINFO[0m - RealtimeSTT recorder configured successfully
2025-09-08 18:39:34.793 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Initializing emotion detection models...
2025-09-08 18:39:34.825 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Loading text emotion classifier...
2025-09-08 18:39:35.120 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "HEAD /j-hartmann/emotion-english-distilroberta-base/resolve/main/config.json HTTP/1.1" 307 0
2025-09-08 18:39:35.163 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "HEAD /api/resolve-cache/models/j-hartmann/emotion-english-distilroberta-base/0e1cd914e3d46199ed785853e12b57304e04178b/config.json HTTP/1.1" 200 0
2025-09-08 18:39:35.455 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "HEAD /j-hartmann/emotion-english-distilroberta-base/resolve/main/model.safetensors HTTP/1.1" 404 0
2025-09-08 18:39:38.597 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Text emotion classifier loaded successfully
2025-09-08 18:39:38.599 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Custom audio model not found, creating feature-based classifier...
2025-09-08 18:39:38.599 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Creating feature-based audio emotion classifier...
2025-09-08 18:39:38.600 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Emotion detection models initialized successfully
2025-09-08 18:39:38.601 - RealTimeSTT: core.text_formatter - [32mINFO[0m - TextFormatter ready for processing
2025-09-08 18:39:38.601 - RealTimeSTT: core.voice_processor - [32mINFO[0m - Voice processor initialization completed
2025-09-08 18:39:38.603 - RealTimeSTT: ui.status_monitor - DEBUG - Status callback added
2025-09-08 18:39:38.604 - RealTimeSTT: __main__ - DEBUG - System callbacks configured
2025-09-08 18:39:38.606 - RealTimeSTT: __main__ - [32mINFO[0m - System initialization completed
2025-09-08 18:39:38.608 - RealTimeSTT: core.voice_processor - [32mINFO[0m - Starting voice processor...
2025-09-08 18:39:38.609 - RealTimeSTT: core.voice_processor - [32mINFO[0m - Voice processing loop started
2025-09-08 18:39:38.609 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:39:38.609 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:39:39.370 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15021MB
2025-09-08 18:39:39.649 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:39:39.735 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15024MB
2025-09-08 18:39:44.494 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14686MB
2025-09-08 18:39:44.860 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14672MB
2025-09-08 18:39:45.047 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:39:45.047 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:39:45.047 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:39:45.052 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:39:45.052 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:39:45.053 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:39:45.158 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:39:45.160 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:39:45.228 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:39:45.840 - RealTimeSTT: root - DEBUG - Realtime text detected:  I'll do it again.
2025-09-08 18:39:45.841 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:39:45.947 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 21504
2025-09-08 18:39:45.948 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.344
2025-09-08 18:39:45.998 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:39:46.197 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:39:46.198 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:39:46.197 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:39:46.198 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:39:46.251 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:39:46.257 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:39:46.266 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:39:46.266 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:39:46.450 - RealTimeSTT: root - DEBUG - Realtime text detected:  Hello, can you hear me?
2025-09-08 18:39:49.117 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 2.86 seconds
2025-09-08 18:39:49.117 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Hello, can you hear me?...
2025-09-08 18:39:49.667 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15737MB
2025-09-08 18:39:50.096 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15694MB
2025-09-08 18:39:52.526 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:39:52.526 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:39:52.526 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:39:52.526 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:39:52.526 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:39:52.527 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:39:52.650 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 9216
2025-09-08 18:39:52.650 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.576
2025-09-08 18:39:52.733 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:39:53.460 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:39:53.461 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:39:53.466 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:39:53.467 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:39:53.468 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Hello, can you hear me?...
2025-09-08 18:39:53.469 - RealTimeSTT: __main__ - [31mERROR[0m - Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:39:53.470 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 4.350s
2025-09-08 18:39:53.474 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:39:53.474 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:39:53.476 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:39:54.084 - RealTimeSTT: root - DEBUG - Realtime text detected:  kind of
2025-09-08 18:39:54.092 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:39:54.092 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:39:54.094 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:39:54.089 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:39:54.093 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:39:54.127 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:39:54.151 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:39:54.172 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:39:54.186 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:39:54.793 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15670MB
2025-09-08 18:39:55.220 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15745MB
2025-09-08 18:39:57.143 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 2.99 seconds
2025-09-08 18:39:57.143 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Hello, can you hear me?...
2025-09-08 18:39:59.988 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15832MB
2025-09-08 18:40:00.335 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15845MB
2025-09-08 18:40:04.935 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:40:04.937 - RealTimeSTT: core.emotion_detector - DEBUG - Emotion detected: neutral (1.00)
2025-09-08 18:40:04.938 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:40:04.939 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:40:04.940 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Hello, can you hear me?...
2025-09-08 18:40:04.942 - RealTimeSTT: __main__ - DEBUG - Strong emotion detected: neutral
2025-09-08 18:40:04.942 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 7.796s
2025-09-08 18:40:04.996 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:40:04.996 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:40:04.999 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:40:05.114 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15843MB
2025-09-08 18:40:05.454 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15843MB
2025-09-08 18:40:10.266 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15873MB
2025-09-08 18:40:10.574 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15871MB
2025-09-08 18:40:11.146 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:40:11.146 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:40:11.146 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:40:11.147 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:40:11.147 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:40:11.147 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:40:11.274 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:40:11.275 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:40:11.345 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:40:11.818 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.349388 < -1.000000)
2025-09-08 18:40:11.818 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.653735 > 0.600000)
2025-09-08 18:40:11.818 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:40:11.932 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 19456
2025-09-08 18:40:11.932 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.216
2025-09-08 18:40:11.977 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:40:12.296 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:40:12.296 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:40:12.297 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:40:12.297 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:40:12.314 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:40:12.319 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:40:12.320 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:40:12.366 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:40:12.390 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.374054 < -1.000000)
2025-09-08 18:40:12.390 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.789881 > 0.600000)
2025-09-08 18:40:12.390 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:40:14.972 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 2.65 seconds
2025-09-08 18:40:14.986 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:40:14.986 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:40:14.999 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:40:15.395 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15638MB
2025-09-08 18:40:15.693 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15651MB
2025-09-08 18:40:18.696 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:40:18.696 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:40:18.696 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:40:18.696 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:40:18.696 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:40:18.696 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:40:18.822 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:40:18.822 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:40:18.864 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:40:19.267 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.421878 < -1.000000)
2025-09-08 18:40:19.267 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.705852 > 0.600000)
2025-09-08 18:40:19.267 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:40:19.379 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 17408
2025-09-08 18:40:19.379 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.088
2025-09-08 18:40:19.425 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:40:19.812 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.377390 < -1.000000)
2025-09-08 18:40:19.812 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.823947 > 0.600000)
2025-09-08 18:40:19.813 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:40:19.847 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:40:19.847 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:40:19.847 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:40:19.847 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:40:19.874 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:40:19.876 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:40:19.905 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:40:19.916 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:40:19.920 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 0
2025-09-08 18:40:19.920 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.000
2025-09-08 18:40:19.977 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:40:20.551 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15783MB
2025-09-08 18:40:20.812 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15792MB
2025-09-08 18:40:23.345 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 3.47 seconds
2025-09-08 18:40:23.345 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Thank you very much....
2025-09-08 18:40:23.402 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:40:23.429 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    smoothed[emotion] = alpha * current_emotions[emotion] + (1 - alpha) * historical_avg
KeyError: 'happy'
2025-09-08 18:40:23.450 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:40:23.450 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:40:23.450 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Thank you very much....
2025-09-08 18:40:23.451 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 0.105s
2025-09-08 18:40:23.470 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:40:23.470 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:40:23.472 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:40:25.669 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15433MB
2025-09-08 18:40:25.929 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15431MB
2025-09-08 18:40:30.790 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15428MB
2025-09-08 18:40:31.052 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15427MB
2025-09-08 18:40:35.919 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15422MB
2025-09-08 18:40:36.179 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15423MB
2025-09-08 18:40:41.043 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15475MB
2025-09-08 18:40:41.304 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15473MB
2025-09-08 18:40:46.164 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15471MB
2025-09-08 18:40:46.426 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15477MB
2025-09-08 18:40:51.281 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15483MB
2025-09-08 18:40:51.540 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15489MB
2025-09-08 18:40:56.396 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15502MB
2025-09-08 18:40:56.654 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15503MB
2025-09-08 18:41:01.510 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15534MB
2025-09-08 18:41:01.771 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15504MB
2025-09-08 18:41:06.635 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15567MB
2025-09-08 18:41:06.888 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15564MB
2025-09-08 18:41:11.765 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15550MB
2025-09-08 18:41:12.015 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15556MB
2025-09-08 18:41:16.881 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15540MB
2025-09-08 18:41:17.140 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15539MB
2025-09-08 18:41:22.017 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15562MB
2025-09-08 18:41:22.267 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15556MB
2025-09-08 18:41:27.141 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15548MB
2025-09-08 18:41:27.387 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15562MB
2025-09-08 18:41:32.268 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15549MB
2025-09-08 18:41:32.514 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15543MB
2025-09-08 18:41:37.384 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15338MB
2025-09-08 18:41:37.628 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15338MB
2025-09-08 18:41:42.501 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14813MB
2025-09-08 18:41:42.742 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14812MB
2025-09-08 18:41:47.624 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14804MB
2025-09-08 18:41:47.866 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14790MB
2025-09-08 18:41:52.743 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14808MB
2025-09-08 18:41:52.990 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14798MB
2025-09-08 18:41:57.864 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14822MB
2025-09-08 18:41:58.115 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14778MB
2025-09-08 18:42:02.981 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14647MB
2025-09-08 18:42:03.241 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14672MB
2025-09-08 18:42:08.099 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14677MB
2025-09-08 18:42:08.359 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14713MB
2025-09-08 18:42:13.220 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14743MB
2025-09-08 18:42:13.484 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14739MB
2025-09-08 18:42:18.344 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14762MB
2025-09-08 18:42:18.608 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14759MB
2025-09-08 18:42:23.472 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14793MB
2025-09-08 18:42:23.733 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14789MB
2025-09-08 18:42:28.590 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14810MB
2025-09-08 18:42:28.855 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14815MB
2025-09-08 18:42:33.715 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14857MB
2025-09-08 18:42:33.979 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14855MB
2025-09-08 18:42:38.832 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14879MB
2025-09-08 18:42:39.093 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14875MB
2025-09-08 18:42:43.957 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14921MB
2025-09-08 18:42:44.218 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14925MB
2025-09-08 18:42:49.078 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14925MB
2025-09-08 18:42:49.338 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14925MB
2025-09-08 18:42:54.204 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14922MB
2025-09-08 18:42:54.465 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14952MB
2025-09-08 18:42:59.323 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14957MB
2025-09-08 18:42:59.583 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14988MB
2025-09-08 18:43:04.450 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15021MB
2025-09-08 18:43:04.712 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15021MB
2025-09-08 18:43:09.572 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15399MB
2025-09-08 18:43:09.834 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15369MB
2025-09-08 18:43:14.690 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15155MB
2025-09-08 18:43:14.954 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15167MB
2025-09-08 18:43:19.806 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15149MB
2025-09-08 18:43:20.068 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15138MB
2025-09-08 18:43:24.931 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15125MB
2025-09-08 18:43:25.193 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15122MB
2025-09-08 18:43:30.058 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15156MB
2025-09-08 18:43:30.314 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15165MB
2025-09-08 18:43:35.184 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15186MB
2025-09-08 18:43:35.430 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15183MB
2025-09-08 18:43:40.308 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15204MB
2025-09-08 18:43:40.554 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15212MB
2025-09-08 18:43:44.395 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:43:44.395 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:43:44.395 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:43:44.395 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:43:44.395 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:43:44.395 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:43:44.512 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:43:44.512 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:43:44.536 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:43:44.536 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:43:44.536 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:43:44.537 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:43:44.537 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:43:44.537 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:43:44.557 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:43:44.665 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:43:44.666 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:43:44.744 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:43:45.082 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.192559 < -1.000000)
2025-09-08 18:43:45.324 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.2 (-1.139586 < -1.000000)
2025-09-08 18:43:45.435 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15402MB
2025-09-08 18:43:45.467 - RealTimeSTT: root - DEBUG - Realtime text detected:  I'm going to go back.
2025-09-08 18:43:45.468 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:43:45.547 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:43:45.547 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:43:45.547 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:43:45.547 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:43:45.579 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 23552
2025-09-08 18:43:45.580 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.472
2025-09-08 18:43:45.608 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:43:45.610 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:43:45.611 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:43:45.648 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:43:45.648 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.4 (-1.426801 < -1.000000)
2025-09-08 18:43:45.676 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:43:45.677 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15285MB
2025-09-08 18:43:45.689 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:43:45.690 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:43:45.690 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:43:45.691 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:43:45.762 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:43:45.767 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:43:45.790 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:43:45.816 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:43:47.483 - RealTimeSTT: root - DEBUG - Realtime text detected:  and to open you here to me.
2025-09-08 18:43:50.559 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High CPU usage: 100.0%
2025-09-08 18:43:50.564 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15445MB
2025-09-08 18:43:50.787 - RealTimeSTT: faster_whisper - DEBUG - Compression ratio threshold is not met with temperature 0.6 (12.816327 > 2.400000)
2025-09-08 18:43:50.804 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High CPU usage: 100.0%
2025-09-08 18:43:50.806 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15427MB
2025-09-08 18:43:51.565 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 5.80 seconds
2025-09-08 18:43:51.565 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: And open your head away....
2025-09-08 18:43:51.731 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 6.12 seconds
2025-09-08 18:43:51.732 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: And open your head away....
2025-09-08 18:43:51.742 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.8 (-1.676412 < -1.000000)
2025-09-08 18:43:52.237 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 1.0 (-1.950415 < -1.000000)
2025-09-08 18:43:52.238 - RealTimeSTT: faster_whisper - DEBUG - Reset prompt. prompt_reset_on_temperature threshold is met 1.000000 > 0.500000
2025-09-08 18:43:52.238 - RealTimeSTT: root - DEBUG - Realtime text detected:  And...
2025-09-08 18:43:55.681 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15966MB
2025-09-08 18:43:55.926 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15507MB
2025-09-08 18:43:55.967 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:43:55.969 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:43:55.976 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:43:55.977 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:43:55.978 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: And open your head away....
2025-09-08 18:43:55.978 - RealTimeSTT: __main__ - [31mERROR[0m - Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:43:55.979 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 4.412s
2025-09-08 18:43:55.997 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:43:55.997 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:43:55.999 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:43:58.816 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:43:58.817 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:43:58.822 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:43:58.822 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:43:58.823 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: And open your head away....
2025-09-08 18:43:58.824 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 7.090s
2025-09-08 18:43:58.849 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:43:58.849 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:43:58.851 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:44:00.809 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15151MB
2025-09-08 18:44:01.052 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15175MB
2025-09-08 18:44:05.934 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15165MB
2025-09-08 18:44:06.177 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15165MB
2025-09-08 18:44:11.053 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15223MB
2025-09-08 18:44:11.295 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15235MB
2025-09-08 18:44:16.171 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15260MB
2025-09-08 18:44:16.411 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15261MB
2025-09-08 18:44:21.294 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15249MB
2025-09-08 18:44:21.526 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15249MB
2025-09-08 18:44:25.685 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:44:25.685 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:44:25.686 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:44:25.686 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:44:25.686 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:44:25.686 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:44:25.796 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:44:25.796 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:44:25.848 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:44:26.439 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.360250 < -1.000000)
2025-09-08 18:44:26.439 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.644521 > 0.600000)
2025-09-08 18:44:26.440 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:44:26.440 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15235MB
2025-09-08 18:44:26.544 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 20480
2025-09-08 18:44:26.544 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.280
2025-09-08 18:44:26.589 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:44:26.641 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15319MB
2025-09-08 18:44:26.837 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:44:26.837 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:44:26.838 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:44:26.838 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:44:26.896 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:44:26.898 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:44:26.905 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:44:26.926 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:44:26.966 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.341292 < -1.000000)
2025-09-08 18:44:26.966 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.790606 > 0.600000)
2025-09-08 18:44:26.967 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:44:29.617 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 2.72 seconds
2025-09-08 18:44:29.618 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Thank you very much....
2025-09-08 18:44:31.228 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:44:31.229 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:44:31.230 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:44:31.231 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:44:31.231 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Thank you very much....
2025-09-08 18:44:31.231 - RealTimeSTT: __main__ - [31mERROR[0m - Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:44:31.232 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 1.613s
2025-09-08 18:44:31.250 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:44:31.250 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:44:31.252 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:44:31.558 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15829MB
2025-09-08 18:44:31.755 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15832MB
2025-09-08 18:44:36.705 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15716MB
2025-09-08 18:44:36.867 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15714MB
2025-09-08 18:44:41.818 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15763MB
2025-09-08 18:44:41.988 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15756MB
2025-09-08 18:44:44.486 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:44:44.486 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:44:44.486 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:44:44.486 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:44:44.486 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:44:44.486 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:44:44.600 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:44:44.600 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:44:44.648 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:44:45.634 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.290505 < -1.000000)
2025-09-08 18:44:45.634 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.739992 > 0.600000)
2025-09-08 18:44:45.635 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:44:45.645 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:44:45.645 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:44:45.646 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:44:45.646 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:44:45.700 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:44:45.701 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:44:45.705 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:44:45.731 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:44:45.746 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 0
2025-09-08 18:44:45.746 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.000
2025-09-08 18:44:45.876 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:44:46.938 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15718MB
2025-09-08 18:44:47.107 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15721MB
2025-09-08 18:44:49.100 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 3.40 seconds
2025-09-08 18:44:49.101 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Thank you very much....
2025-09-08 18:44:50.646 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:44:50.646 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:44:50.646 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:44:50.647 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:44:50.646 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:44:50.709 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:44:50.767 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:44:50.767 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:44:50.847 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:44:51.011 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:44:51.012 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:44:51.014 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:44:51.015 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:44:51.016 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Thank you very much....
2025-09-08 18:44:51.017 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 1.914s
2025-09-08 18:44:51.042 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:44:51.042 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:44:51.063 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:44:52.138 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:44:52.140 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:44:52.142 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:44:52.142 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:44:52.233 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High CPU usage: 83.3%
2025-09-08 18:44:52.233 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15846MB
2025-09-08 18:44:52.269 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:44:52.307 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:44:52.306 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High CPU usage: 84.4%
2025-09-08 18:44:52.314 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15846MB
2025-09-08 18:44:52.462 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:44:52.476 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:44:54.609 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.312288 < -1.000000)
2025-09-08 18:44:54.610 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.753318 > 0.600000)
2025-09-08 18:44:54.610 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:44:56.984 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 4.52 seconds
2025-09-08 18:44:57.003 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:44:57.003 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:44:57.007 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:44:57.356 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15409MB
2025-09-08 18:44:57.431 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15410MB
2025-09-08 18:45:02.483 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15245MB
2025-09-08 18:45:02.545 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15245MB
2025-09-08 18:45:07.611 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15113MB
2025-09-08 18:45:07.672 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15113MB
2025-09-08 18:45:12.732 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15105MB
2025-09-08 18:45:12.793 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15105MB
2025-09-08 18:45:17.859 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15096MB
2025-09-08 18:45:17.920 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15096MB
2025-09-08 18:45:18.855 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:45:18.855 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:45:18.856 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:45:18.856 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:45:18.856 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:45:18.856 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:45:18.875 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:45:18.875 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:45:18.875 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:45:18.875 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:45:18.876 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:45:18.876 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:45:18.971 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:45:18.971 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:45:19.011 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 9216
2025-09-08 18:45:19.011 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.576
2025-09-08 18:45:19.028 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:45:19.077 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:45:19.596 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.346002 < -1.000000)
2025-09-08 18:45:19.614 - RealTimeSTT: root - DEBUG - Realtime text detected:  Okay.
2025-09-08 18:45:19.614 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:45:19.721 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 20480
2025-09-08 18:45:19.721 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.280
2025-09-08 18:45:19.793 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:45:19.807 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.2 (-1.314624 < -1.000000)
2025-09-08 18:45:20.028 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:45:20.028 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:45:20.059 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:45:20.060 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:45:20.087 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:45:20.089 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:45:20.118 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:45:20.121 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.4 (-1.300227 < -1.000000)
2025-09-08 18:45:20.139 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:45:20.139 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:45:20.140 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:45:20.140 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:45:20.157 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:45:20.242 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:45:20.251 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:45:20.255 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:45:20.267 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:45:20.460 - RealTimeSTT: root - DEBUG - Realtime text detected:  Okay.
2025-09-08 18:45:20.657 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.6 (-1.295926 < -1.000000)
2025-09-08 18:45:21.474 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.8 (-1.583082 < -1.000000)
2025-09-08 18:45:21.963 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 1.0 (-2.666584 < -1.000000)
2025-09-08 18:45:21.963 - RealTimeSTT: faster_whisper - DEBUG - Reset prompt. prompt_reset_on_temperature threshold is met 1.000000 > 0.500000
2025-09-08 18:45:21.964 - RealTimeSTT: root - DEBUG - Realtime text detected:  Okay.
2025-09-08 18:45:22.987 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High CPU usage: 85.9%
2025-09-08 18:45:22.987 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15488MB
2025-09-08 18:45:23.058 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High CPU usage: 82.1%
2025-09-08 18:45:23.061 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15193MB
2025-09-08 18:45:23.953 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 3.86 seconds
2025-09-08 18:45:23.954 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Thank you....
2025-09-08 18:45:24.532 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 4.28 seconds
2025-09-08 18:45:24.532 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Thank you very much....
2025-09-08 18:45:27.498 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:45:27.499 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:45:27.502 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:45:27.503 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:45:27.503 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Thank you....
2025-09-08 18:45:27.504 - RealTimeSTT: __main__ - [31mERROR[0m - Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:45:27.504 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 3.549s
2025-09-08 18:45:27.527 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:45:27.527 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:45:27.536 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:45:28.111 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15854MB
2025-09-08 18:45:28.197 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15854MB
2025-09-08 18:45:30.073 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:45:30.074 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:45:30.074 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:45:30.075 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:45:30.075 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Thank you very much....
2025-09-08 18:45:30.075 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 5.544s
2025-09-08 18:45:30.093 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:45:30.093 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:45:30.094 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:45:33.240 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15857MB
2025-09-08 18:45:33.320 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15829MB
2025-09-08 18:45:33.846 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:45:33.846 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:45:33.846 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:45:33.846 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:45:33.846 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:45:33.895 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:45:33.895 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:45:33.895 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:45:33.895 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:45:33.895 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:45:33.895 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:45:33.896 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:45:33.983 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:45:33.983 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:45:34.030 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 9216
2025-09-08 18:45:34.030 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.576
2025-09-08 18:45:34.033 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:45:34.105 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:45:34.679 - RealTimeSTT: root - DEBUG - Realtime text detected:  I don't know.
2025-09-08 18:45:34.679 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:45:34.789 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 20480
2025-09-08 18:45:34.789 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.280
2025-09-08 18:45:34.853 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:45:35.335 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.109382 < -1.000000)
2025-09-08 18:45:35.567 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:45:35.568 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:45:35.568 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:45:35.568 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:45:35.577 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:45:35.593 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:45:35.595 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:45:35.577 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:45:35.615 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:45:35.615 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:45:35.623 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:45:35.626 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:45:35.705 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.2 (-1.024434 < -1.000000)
2025-09-08 18:45:35.731 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:45:35.735 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:45:35.744 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:45:35.766 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:45:36.630 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.4 (-1.109381 < -1.000000)
2025-09-08 18:45:37.340 - RealTimeSTT: faster_whisper - DEBUG - Reset prompt. prompt_reset_on_temperature threshold is met 0.600000 > 0.500000
2025-09-08 18:45:37.341 - RealTimeSTT: root - DEBUG - Realtime text detected:  Hello, can you hear me?
2025-09-08 18:45:37.353 - RealTimeSTT: root - DEBUG - Realtime text detected:  I don't know.
2025-09-08 18:45:38.366 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High CPU usage: 98.8%
2025-09-08 18:45:38.367 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15443MB
2025-09-08 18:45:38.446 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High CPU usage: 96.4%
2025-09-08 18:45:38.448 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15414MB
2025-09-08 18:45:41.077 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 5.34 seconds
2025-09-08 18:45:41.077 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: I don't know if you can hear me....
2025-09-08 18:45:43.489 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 16056MB
2025-09-08 18:45:43.578 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 16046MB
2025-09-08 18:45:43.613 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:45:43.614 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:45:43.615 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:45:43.615 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:45:43.616 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: I don't know if you can hear me....
2025-09-08 18:45:43.616 - RealTimeSTT: __main__ - [31mERROR[0m - Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:45:43.617 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 2.538s
2025-09-08 18:45:43.636 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:45:43.636 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:45:43.642 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:45:44.893 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 9.30 seconds
2025-09-08 18:45:44.893 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Hello, can you hear me?...
2025-09-08 18:45:46.674 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:45:46.675 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:45:46.698 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:45:46.699 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:45:46.700 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Hello, can you hear me?...
2025-09-08 18:45:46.701 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 1.806s
2025-09-08 18:45:46.713 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:45:46.713 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:45:46.748 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:45:48.613 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15778MB
2025-09-08 18:45:48.704 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15772MB
2025-09-08 18:45:53.737 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15615MB
2025-09-08 18:45:53.828 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15611MB
2025-09-08 18:45:58.850 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15709MB
2025-09-08 18:45:58.956 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15707MB
2025-09-08 18:46:00.475 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:46:00.475 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:46:00.475 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:46:00.475 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:46:00.475 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:46:00.475 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:46:00.598 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 9216
2025-09-08 18:46:00.599 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.576
2025-09-08 18:46:00.649 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:46:01.598 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.204159 < -1.000000)
2025-09-08 18:46:01.598 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.675465 > 0.600000)
2025-09-08 18:46:01.598 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:46:01.685 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:46:01.685 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:46:01.688 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:46:01.688 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:46:01.699 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 0
2025-09-08 18:46:01.699 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.000
2025-09-08 18:46:01.699 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:46:01.711 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:46:01.714 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:46:01.755 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:46:01.772 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:46:03.978 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15573MB
2025-09-08 18:46:04.083 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15717MB
2025-09-08 18:46:06.720 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 5.01 seconds
2025-09-08 18:46:06.720 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Thank you very much....
2025-09-08 18:46:08.922 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:46:08.923 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    # Apply exponential smoothing
KeyError: 'happy'
2025-09-08 18:46:08.924 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:46:08.925 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:46:08.925 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Thank you very much....
2025-09-08 18:46:08.926 - RealTimeSTT: __main__ - [31mERROR[0m - Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:46:08.926 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 2.205s
2025-09-08 18:46:08.952 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:46:08.952 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:46:08.953 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:46:09.095 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 16011MB
2025-09-08 18:46:09.201 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 16010MB
2025-09-08 18:46:14.220 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15699MB
2025-09-08 18:46:14.328 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15700MB
2025-09-08 18:46:19.333 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15682MB
2025-09-08 18:46:19.440 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15682MB
2025-09-08 18:46:24.462 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15681MB
2025-09-08 18:46:24.554 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15638MB
2025-09-08 18:46:29.579 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15345MB
2025-09-08 18:46:29.671 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15344MB
2025-09-08 18:46:34.703 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15343MB
2025-09-08 18:46:34.795 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15342MB
2025-09-08 18:46:39.824 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15019MB
2025-09-08 18:46:39.916 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15019MB
2025-09-08 18:46:44.939 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14990MB
2025-09-08 18:46:45.032 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14990MB
2025-09-08 18:46:50.055 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15085MB
2025-09-08 18:46:50.148 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15080MB
2025-09-08 18:46:55.178 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15041MB
2025-09-08 18:46:55.272 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15041MB
2025-09-08 18:47:00.307 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15033MB
2025-09-08 18:47:00.401 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15033MB
2025-09-08 18:47:05.424 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15007MB
2025-09-08 18:47:05.524 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15007MB
2025-09-08 18:47:10.549 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14964MB
2025-09-08 18:47:10.641 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14964MB
2025-09-08 18:47:15.669 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14995MB
2025-09-08 18:47:15.761 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14995MB
2025-09-08 18:47:20.788 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15028MB
2025-09-08 18:47:20.879 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15028MB
2025-09-08 18:47:25.903 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15053MB
2025-09-08 18:47:25.994 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15053MB
2025-09-08 18:47:31.031 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15025MB
2025-09-08 18:47:31.123 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15027MB
2025-09-08 18:47:36.157 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14977MB
2025-09-08 18:47:36.247 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14977MB
2025-09-08 18:47:41.273 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14993MB
2025-09-08 18:47:41.364 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14993MB
2025-09-08 18:47:46.392 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14962MB
2025-09-08 18:47:46.484 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14962MB
2025-09-08 18:47:51.519 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14987MB
2025-09-08 18:47:51.612 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14984MB
2025-09-08 18:47:56.641 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14994MB
2025-09-08 18:47:56.731 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14994MB
2025-09-08 18:48:01.764 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14952MB
2025-09-08 18:48:01.857 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14957MB
2025-09-08 18:48:06.888 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14928MB
2025-09-08 18:48:06.980 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14928MB
2025-09-08 18:48:12.010 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14907MB
2025-09-08 18:48:12.103 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14907MB
2025-09-08 18:48:17.128 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14912MB
2025-09-08 18:48:17.220 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14910MB
2025-09-08 18:48:22.253 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14848MB
2025-09-08 18:48:22.344 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14847MB
2025-09-08 18:48:27.375 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14788MB
2025-09-08 18:48:27.468 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14790MB
2025-09-08 18:48:29.514 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:48:29.514 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:48:29.515 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:48:29.515 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:48:29.515 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:48:29.515 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:48:29.645 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:48:29.645 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:48:29.688 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:48:30.088 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.404041 < -1.000000)
2025-09-08 18:48:30.088 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.665665 > 0.600000)
2025-09-08 18:48:30.088 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:48:30.196 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 17408
2025-09-08 18:48:30.196 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.088
2025-09-08 18:48:30.242 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:48:30.595 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.385383 < -1.000000)
2025-09-08 18:48:30.595 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.815277 > 0.600000)
2025-09-08 18:48:30.596 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:48:30.666 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:48:30.666 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:48:30.666 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:48:30.666 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:48:30.704 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 0
2025-09-08 18:48:30.704 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.000
2025-09-08 18:48:30.719 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:48:30.721 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:48:30.722 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:48:30.725 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:48:30.751 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:48:32.497 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14835MB
2025-09-08 18:48:32.588 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14858MB
2025-09-08 18:48:33.968 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 3.25 seconds
2025-09-08 18:48:33.968 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Thank you very much....
2025-09-08 18:48:36.368 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:48:36.369 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:48:36.374 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:48:36.375 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:48:36.375 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Thank you very much....
2025-09-08 18:48:36.375 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 2.407s
2025-09-08 18:48:36.395 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:48:36.395 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:48:36.397 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:48:37.622 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15893MB
2025-09-08 18:48:37.715 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15892MB
2025-09-08 18:48:42.743 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15796MB
2025-09-08 18:48:42.836 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15795MB
2025-09-08 18:48:47.866 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15766MB
2025-09-08 18:48:47.958 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15765MB
2025-09-08 18:48:53.002 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15814MB
2025-09-08 18:48:53.094 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15814MB
2025-09-08 18:48:58.131 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15691MB
2025-09-08 18:48:58.225 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15691MB
2025-09-08 18:49:03.259 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15766MB
2025-09-08 18:49:03.350 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15766MB
2025-09-08 18:49:08.379 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15714MB
2025-09-08 18:49:08.466 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15716MB
2025-09-08 18:49:13.502 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15695MB
2025-09-08 18:49:13.581 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15691MB
2025-09-08 18:49:18.627 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15678MB
2025-09-08 18:49:18.703 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15678MB
2025-09-08 18:49:23.750 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15663MB
2025-09-08 18:49:23.829 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15663MB
2025-09-08 18:49:28.871 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15634MB
2025-09-08 18:49:28.953 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15634MB
2025-09-08 18:49:33.444 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:49:33.445 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:49:33.445 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:49:33.445 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:49:33.445 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:49:33.445 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:49:33.558 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:49:33.559 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:49:33.609 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:49:33.996 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15647MB
2025-09-08 18:49:34.037 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.251485 < -1.000000)
2025-09-08 18:49:34.037 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.728566 > 0.600000)
2025-09-08 18:49:34.038 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:49:34.073 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15626MB
2025-09-08 18:49:34.145 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 17408
2025-09-08 18:49:34.145 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.088
2025-09-08 18:49:34.189 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:49:34.551 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.375101 < -1.000000)
2025-09-08 18:49:34.551 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.829114 > 0.600000)
2025-09-08 18:49:34.551 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:49:34.605 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:49:34.605 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:49:34.605 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:49:34.605 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:49:34.641 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:49:34.643 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:49:34.656 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 0
2025-09-08 18:49:34.656 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.000
2025-09-08 18:49:34.656 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:49:34.665 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:49:34.702 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:49:37.248 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 2.61 seconds
2025-09-08 18:49:37.248 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Thank you very much....
2025-09-08 18:49:37.304 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:49:37.305 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:49:37.305 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:49:37.306 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:49:37.307 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Thank you very much....
2025-09-08 18:49:37.307 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 0.058s
2025-09-08 18:49:37.330 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:49:37.330 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:49:37.332 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:49:39.130 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15434MB
2025-09-08 18:49:39.202 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15434MB
2025-09-08 18:49:44.261 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15412MB
2025-09-08 18:49:44.321 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15412MB
2025-09-08 18:49:49.388 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15379MB
2025-09-08 18:49:49.434 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15376MB
2025-09-08 18:49:54.509 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15382MB
2025-09-08 18:49:54.549 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15382MB
2025-09-08 18:49:59.632 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15359MB
2025-09-08 18:49:59.675 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15359MB
2025-09-08 18:50:04.755 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15346MB
2025-09-08 18:50:04.802 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15346MB
2025-09-08 18:50:09.879 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15329MB
2025-09-08 18:50:09.926 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15329MB
2025-09-08 18:50:15.005 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15302MB
2025-09-08 18:50:15.044 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15302MB
2025-09-08 18:50:18.124 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:50:18.124 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:50:18.124 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:50:18.124 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:50:18.124 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:50:18.125 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:50:18.234 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:50:18.235 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:50:18.286 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:50:18.698 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.406120 < -1.000000)
2025-09-08 18:50:18.698 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.630413 > 0.600000)
2025-09-08 18:50:18.699 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:50:18.812 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 17408
2025-09-08 18:50:18.812 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.088
2025-09-08 18:50:18.857 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:50:19.205 - RealTimeSTT: root - DEBUG - Realtime text detected:  Thank you.
2025-09-08 18:50:19.205 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:50:19.275 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:50:19.275 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:50:19.275 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:50:19.275 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:50:19.309 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 0
2025-09-08 18:50:19.309 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.000
2025-09-08 18:50:19.350 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:50:19.355 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:50:19.357 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:50:19.386 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:50:19.404 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:50:20.121 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15323MB
2025-09-08 18:50:20.167 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15318MB
2025-09-08 18:50:21.810 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 2.45 seconds
2025-09-08 18:50:21.810 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Thank you....
2025-09-08 18:50:21.865 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:50:21.865 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:50:21.866 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:50:21.867 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:50:21.868 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Thank you....
2025-09-08 18:50:21.868 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 0.057s
2025-09-08 18:50:21.893 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:50:21.893 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:50:21.895 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:50:25.246 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15278MB
2025-09-08 18:50:25.287 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15278MB
2025-09-08 18:50:30.371 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15258MB
2025-09-08 18:50:30.401 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15258MB
2025-09-08 18:50:35.500 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15241MB
2025-09-08 18:50:35.517 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15241MB
2025-09-08 18:50:40.628 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15230MB
2025-09-08 18:50:40.641 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15230MB
2025-09-08 18:50:45.758 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15211MB
2025-09-08 18:50:45.758 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15211MB
2025-09-08 18:50:48.204 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:50:48.204 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:50:48.204 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:50:48.204 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:50:48.204 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:50:48.204 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:50:48.332 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 9216
2025-09-08 18:50:48.333 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.576
2025-09-08 18:50:48.376 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:50:48.724 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:50:48.725 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:50:48.725 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:50:48.725 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:50:48.725 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:50:48.725 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:50:48.742 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.175257 < -1.000000)
2025-09-08 18:50:48.742 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.684798 > 0.600000)
2025-09-08 18:50:48.742 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:50:48.852 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 17408
2025-09-08 18:50:48.852 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.088
2025-09-08 18:50:48.852 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:50:48.853 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:50:48.898 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:50:48.900 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:50:49.356 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:50:49.357 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:50:49.357 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:50:49.357 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:50:49.391 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.048720 < -1.000000)
2025-09-08 18:50:49.391 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.673437 > 0.600000)
2025-09-08 18:50:49.392 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:50:49.402 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.022791 < -1.000000)
2025-09-08 18:50:49.402 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.616231 > 0.600000)
2025-09-08 18:50:49.402 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:50:49.409 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:50:49.411 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:50:49.414 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:50:49.440 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:50:49.517 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 19456
2025-09-08 18:50:49.517 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.216
2025-09-08 18:50:49.581 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:50:49.875 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:50:49.875 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:50:49.875 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:50:49.876 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:50:49.933 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:50:49.936 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:50:49.944 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:50:49.964 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:50:50.160 - RealTimeSTT: root - DEBUG - Realtime text detected:  Thank you.
2025-09-08 18:50:50.883 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High CPU usage: 83.3%
2025-09-08 18:50:50.885 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14579MB
2025-09-08 18:50:50.890 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High CPU usage: 85.4%
2025-09-08 18:50:50.891 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14569MB
2025-09-08 18:50:52.856 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 3.44 seconds
2025-09-08 18:50:52.856 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Thank you....
2025-09-08 18:50:52.912 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:50:52.913 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:50:52.914 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:50:52.915 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:50:52.915 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Thank you....
2025-09-08 18:50:52.916 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 0.059s
2025-09-08 18:50:52.932 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:50:52.932 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:50:52.934 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:50:53.394 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 3.46 seconds
2025-09-08 18:50:53.413 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:50:53.413 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:50:53.415 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:50:56.013 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14548MB
2025-09-08 18:50:56.013 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14548MB
2025-09-08 18:51:01.137 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14338MB
2025-09-08 18:51:01.138 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14338MB
2025-09-08 18:51:06.270 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14333MB
2025-09-08 18:51:06.270 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14333MB
2025-09-08 18:51:09.594 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:51:09.594 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:51:09.595 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:51:09.595 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:51:09.595 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:51:09.595 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:51:09.723 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 9216
2025-09-08 18:51:09.723 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.576
2025-09-08 18:51:09.768 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:51:10.169 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.214608 < -1.000000)
2025-09-08 18:51:10.376 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.2 (-1.214607 < -1.000000)
2025-09-08 18:51:10.621 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.4 (-1.330827 < -1.000000)
2025-09-08 18:51:10.745 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:51:10.745 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:51:10.745 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:51:10.746 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:51:10.815 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:51:10.817 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:51:10.831 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:51:10.874 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:51:10.895 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.6 (-1.237001 < -1.000000)
2025-09-08 18:51:11.278 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.8 (-2.175647 < -1.000000)
2025-09-08 18:51:11.398 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High CPU usage: 82.1%
2025-09-08 18:51:11.400 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14386MB
2025-09-08 18:51:11.406 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High CPU usage: 82.1%
2025-09-08 18:51:11.407 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14386MB
2025-09-08 18:51:11.688 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 1.0 (-2.594617 < -1.000000)
2025-09-08 18:51:11.688 - RealTimeSTT: faster_whisper - DEBUG - Reset prompt. prompt_reset_on_temperature threshold is met 1.000000 > 0.500000
2025-09-08 18:51:11.688 - RealTimeSTT: root - DEBUG - Realtime text detected:  on.
2025-09-08 18:51:13.630 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 2.81 seconds
2025-09-08 18:51:13.645 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:51:13.645 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:51:13.646 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:51:14.714 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:51:14.714 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:51:14.714 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:51:14.715 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:51:14.715 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:51:14.715 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:51:14.845 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 9216
2025-09-08 18:51:14.845 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.576
2025-09-08 18:51:14.892 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:51:15.319 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.395228 < -1.000000)
2025-09-08 18:51:15.319 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.687626 > 0.600000)
2025-09-08 18:51:15.319 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:51:15.428 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 18432
2025-09-08 18:51:15.429 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.152
2025-09-08 18:51:15.473 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:51:15.865 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:51:15.865 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:51:15.866 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:51:15.866 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:51:15.871 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.275652 < -1.000000)
2025-09-08 18:51:15.871 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.808675 > 0.600000)
2025-09-08 18:51:15.871 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:51:15.927 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:51:15.929 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:51:15.929 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:51:15.994 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:51:16.526 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14470MB
2025-09-08 18:51:16.527 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14470MB
2025-09-08 18:51:18.779 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 2.85 seconds
2025-09-08 18:51:18.780 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Thank you very much....
2025-09-08 18:51:21.647 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15688MB
2025-09-08 18:51:21.647 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15688MB
2025-09-08 18:51:22.285 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:51:22.286 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:51:22.291 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:51:22.292 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:51:22.292 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Thank you very much....
2025-09-08 18:51:22.293 - RealTimeSTT: __main__ - [31mERROR[0m - Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:51:22.293 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 3.512s
2025-09-08 18:51:22.314 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:51:22.314 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:51:22.317 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:51:24.434 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:51:24.434 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:51:24.434 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:51:24.437 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:51:24.437 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:51:24.437 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:51:24.554 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:51:24.554 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:51:24.603 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:51:24.804 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:51:24.804 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:51:24.804 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:51:24.804 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:51:24.804 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:51:24.868 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:51:24.939 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 9216
2025-09-08 18:51:24.939 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.576
2025-09-08 18:51:24.997 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:51:25.597 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:51:25.597 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:51:25.597 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:51:25.656 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:51:25.658 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:51:25.671 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:51:25.696 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:51:25.697 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:51:25.934 - RealTimeSTT: faster_whisper - DEBUG - Compression ratio threshold is not met with temperature 0.0 (3.107143 > 2.400000)
2025-09-08 18:51:25.970 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:51:25.970 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:51:25.971 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:51:25.995 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:51:26.057 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:51:26.059 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:51:26.073 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:51:26.088 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:51:26.837 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High CPU usage: 100.0%
2025-09-08 18:51:26.840 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15225MB
2025-09-08 18:51:26.839 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High CPU usage: 100.0%
2025-09-08 18:51:26.847 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15222MB
2025-09-08 18:51:27.001 - RealTimeSTT: root - DEBUG - Realtime text detected:  one, two, three.  One, two.
2025-09-08 18:51:30.568 - RealTimeSTT: faster_whisper - DEBUG - Compression ratio threshold is not met with temperature 0.0 (9.507042 > 2.400000)
2025-09-08 18:51:31.051 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 4.99 seconds
2025-09-08 18:51:31.065 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:51:31.065 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:51:31.068 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:51:31.535 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 5.88 seconds
2025-09-08 18:51:31.535 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: One, two, three....
2025-09-08 18:51:31.604 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:51:31.605 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:51:31.607 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:51:31.608 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:51:31.608 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: One, two, three....
2025-09-08 18:51:31.608 - RealTimeSTT: __main__ - [31mERROR[0m - Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:51:31.609 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 0.072s
2025-09-08 18:51:31.631 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:51:31.631 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:51:31.634 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:51:31.967 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High CPU usage: 83.3%
2025-09-08 18:51:31.968 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15201MB
2025-09-08 18:51:31.982 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High CPU usage: 80.2%
2025-09-08 18:51:31.984 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15201MB
2025-09-08 18:51:33.119 - RealTimeSTT: faster_whisper - DEBUG - Compression ratio threshold is not met with temperature 0.2 (18.520833 > 2.400000)
2025-09-08 18:51:33.534 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.4 (-1.402585 < -1.000000)
2025-09-08 18:51:33.534 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.700454 > 0.600000)
2025-09-08 18:51:33.535 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:51:34.555 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:51:34.555 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:51:34.555 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:51:34.556 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:51:34.555 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:51:34.556 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:51:34.662 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:51:34.662 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:51:34.728 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:51:35.324 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.315873 < -1.000000)
2025-09-08 18:51:35.324 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.837936 > 0.600000)
2025-09-08 18:51:35.325 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:51:35.431 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 20480
2025-09-08 18:51:35.431 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.280
2025-09-08 18:51:35.484 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:51:35.705 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:51:35.705 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:51:35.705 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:51:35.706 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:51:35.737 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:51:35.738 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:51:35.739 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:51:35.764 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:51:35.999 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.263839 < -1.000000)
2025-09-08 18:51:35.999 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.866845 > 0.600000)
2025-09-08 18:51:36.000 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:51:37.095 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15290MB
2025-09-08 18:51:37.112 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15289MB
2025-09-08 18:51:38.759 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 3.02 seconds
2025-09-08 18:51:38.759 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Thank you....
2025-09-08 18:51:38.814 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:51:38.815 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:51:38.817 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:51:38.817 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:51:38.818 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Thank you....
2025-09-08 18:51:38.818 - RealTimeSTT: __main__ - [31mERROR[0m - Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:51:38.819 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 0.058s
2025-09-08 18:51:38.845 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:51:38.845 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:51:38.846 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:51:42.213 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14966MB
2025-09-08 18:51:42.228 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14966MB
2025-09-08 18:51:47.332 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14890MB
2025-09-08 18:51:47.348 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14891MB
2025-09-08 18:51:51.434 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:51:51.434 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:51:51.434 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:51:51.434 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:51:51.435 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:51:51.435 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:51:51.550 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:51:51.550 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:51:51.574 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:51:51.574 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:51:51.575 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:51:51.575 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:51:51.575 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:51:51.575 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:51:51.601 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:51:51.694 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:51:51.694 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:51:51.748 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:51:52.131 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.309852 < -1.000000)
2025-09-08 18:51:52.131 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.706185 > 0.600000)
2025-09-08 18:51:52.132 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:51:52.242 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 19456
2025-09-08 18:51:52.242 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.216
2025-09-08 18:51:52.243 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.434415 < -1.000000)
2025-09-08 18:51:52.243 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.740068 > 0.600000)
2025-09-08 18:51:52.243 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:51:52.295 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:51:52.348 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 19456
2025-09-08 18:51:52.348 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.216
2025-09-08 18:51:52.416 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:51:52.463 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High CPU usage: 88.9%
2025-09-08 18:51:52.464 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14934MB
2025-09-08 18:51:52.483 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High CPU usage: 88.9%
2025-09-08 18:51:52.484 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 14977MB
2025-09-08 18:51:52.585 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:51:52.585 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:51:52.586 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:51:52.586 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:51:52.642 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:51:52.644 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:51:52.645 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:51:52.645 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:51:52.726 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:51:52.727 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:51:52.727 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:51:52.750 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:51:52.754 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:51:52.765 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:51:52.817 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:51:52.817 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:51:52.874 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.273726 < -1.000000)
2025-09-08 18:51:52.875 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.840876 > 0.600000)
2025-09-08 18:51:52.875 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:51:53.108 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.403872 < -1.000000)
2025-09-08 18:51:53.108 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.863320 > 0.600000)
2025-09-08 18:51:53.109 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:51:56.504 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 3.86 seconds
2025-09-08 18:51:56.504 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Thank you....
2025-09-08 18:51:56.635 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 3.88 seconds
2025-09-08 18:51:56.635 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Thank you....
2025-09-08 18:51:57.608 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15142MB
2025-09-08 18:51:57.608 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15142MB
2025-09-08 18:51:58.324 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:51:58.325 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:51:58.328 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:51:58.330 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:51:58.330 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Thank you....
2025-09-08 18:51:58.330 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 1.825s
2025-09-08 18:51:58.357 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:51:58.357 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:51:58.359 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:52:00.678 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:52:00.679 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:52:00.680 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:52:00.681 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:52:00.681 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Thank you....
2025-09-08 18:52:00.681 - RealTimeSTT: __main__ - [31mERROR[0m - Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:52:00.682 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 4.046s
2025-09-08 18:52:00.698 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:52:00.698 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:52:00.711 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:52:02.727 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15762MB
2025-09-08 18:52:02.727 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15762MB
2025-09-08 18:52:04.314 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:52:04.314 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:52:04.314 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:52:04.314 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:52:04.314 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:52:04.314 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:52:04.448 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 9216
2025-09-08 18:52:04.448 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.576
2025-09-08 18:52:04.525 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:52:05.022 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.357601 < -1.000000)
2025-09-08 18:52:05.022 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.689072 > 0.600000)
2025-09-08 18:52:05.022 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:52:05.139 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 19456
2025-09-08 18:52:05.140 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.216
2025-09-08 18:52:05.190 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:52:05.464 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:52:05.465 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:52:05.465 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:52:05.465 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:52:05.570 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:52:05.572 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:52:05.594 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:52:05.600 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:52:05.617 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.339978 < -1.000000)
2025-09-08 18:52:05.618 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.793481 > 0.600000)
2025-09-08 18:52:05.618 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:52:07.853 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15868MB
2025-09-08 18:52:07.854 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15868MB
2025-09-08 18:52:08.643 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 3.07 seconds
2025-09-08 18:52:08.643 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Thank you very much....
2025-09-08 18:52:08.699 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:52:08.700 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:52:08.701 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:52:08.701 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:52:08.702 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Thank you very much....
2025-09-08 18:52:08.702 - RealTimeSTT: __main__ - [31mERROR[0m - Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:52:08.702 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 0.058s
2025-09-08 18:52:08.713 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:52:08.713 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:52:08.714 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:52:12.976 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15205MB
2025-09-08 18:52:12.977 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15205MB
2025-09-08 18:52:18.104 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15191MB
2025-09-08 18:52:18.104 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15191MB
2025-09-08 18:52:23.218 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15160MB
2025-09-08 18:52:23.221 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15160MB
2025-09-08 18:52:28.340 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15207MB
2025-09-08 18:52:28.341 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15207MB
2025-09-08 18:52:33.467 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15298MB
2025-09-08 18:52:33.467 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15298MB
2025-09-08 18:52:38.593 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15250MB
2025-09-08 18:52:38.593 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15250MB
2025-09-08 18:52:43.724 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15232MB
2025-09-08 18:52:43.725 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15232MB
2025-09-08 18:52:48.854 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15218MB
2025-09-08 18:52:48.854 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15218MB
2025-09-08 18:52:53.977 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15209MB
2025-09-08 18:52:53.977 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15209MB
2025-09-08 18:52:59.091 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15251MB
2025-09-08 18:52:59.092 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15251MB
2025-09-08 18:53:04.213 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15261MB
2025-09-08 18:53:04.214 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15261MB
2025-09-08 18:53:09.339 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15275MB
2025-09-08 18:53:09.339 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15275MB
2025-09-08 18:53:14.465 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15287MB
2025-09-08 18:53:14.465 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15287MB
2025-09-08 18:53:19.594 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15281MB
2025-09-08 18:53:19.594 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15281MB
2025-09-08 18:53:24.716 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15269MB
2025-09-08 18:53:24.716 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15269MB
2025-09-08 18:53:29.836 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15293MB
2025-09-08 18:53:29.837 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15293MB
2025-09-08 18:53:34.960 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15381MB
2025-09-08 18:53:34.960 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15381MB
2025-09-08 18:53:40.083 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15376MB
2025-09-08 18:53:40.084 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15376MB
2025-09-08 18:53:45.203 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15354MB
2025-09-08 18:53:45.204 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15354MB
2025-09-08 18:53:50.326 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15319MB
2025-09-08 18:53:50.326 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15319MB
2025-09-08 18:53:55.453 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15321MB
2025-09-08 18:53:55.453 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15321MB
2025-09-08 18:54:00.580 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15241MB
2025-09-08 18:54:00.580 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15241MB
2025-09-08 18:54:05.706 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15237MB
2025-09-08 18:54:05.707 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15237MB
2025-09-08 18:54:10.824 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15247MB
2025-09-08 18:54:10.827 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15247MB
2025-09-08 18:54:15.936 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15365MB
2025-09-08 18:54:15.951 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15364MB
2025-09-08 18:54:21.057 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15391MB
2025-09-08 18:54:21.072 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15391MB
2025-09-08 18:54:26.170 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15382MB
2025-09-08 18:54:26.186 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15382MB
2025-09-08 18:54:31.285 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15369MB
2025-09-08 18:54:31.301 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15369MB
2025-09-08 18:54:36.408 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15369MB
2025-09-08 18:54:36.423 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15368MB
2025-09-08 18:54:41.521 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15232MB
2025-09-08 18:54:41.545 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15232MB
2025-09-08 18:54:46.646 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15239MB
2025-09-08 18:54:46.676 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15239MB
2025-09-08 18:54:51.760 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15247MB
2025-09-08 18:54:51.792 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15247MB
2025-09-08 18:54:56.882 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15265MB
2025-09-08 18:54:56.909 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15265MB
2025-09-08 18:55:02.008 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15276MB
2025-09-08 18:55:02.023 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15276MB
2025-09-08 18:55:07.126 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15309MB
2025-09-08 18:55:07.142 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15309MB
2025-09-08 18:55:12.248 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15338MB
2025-09-08 18:55:12.262 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15337MB
2025-09-08 18:55:17.368 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15298MB
2025-09-08 18:55:17.385 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15298MB
2025-09-08 18:55:22.488 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15213MB
2025-09-08 18:55:22.503 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 15213MB
2025-09-08 18:55:24.957 - RealTimeSTT: root - DEBUG - Receive from stdout pipe
2025-09-08 18:55:27.603 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9758MB
2025-09-08 18:55:32.729 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9541MB
2025-09-08 18:55:37.842 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9542MB
2025-09-08 18:55:42.963 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9544MB
2025-09-08 18:55:48.087 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9541MB
2025-09-08 18:55:53.212 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9563MB
2025-09-08 18:55:58.327 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9587MB
2025-09-08 18:56:03.452 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9590MB
2025-09-08 18:56:08.569 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9605MB
2025-09-08 18:56:13.680 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9577MB
2025-09-08 18:56:18.805 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9628MB
2025-09-08 18:56:23.926 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9639MB
2025-09-08 18:56:29.052 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9626MB
2025-09-08 18:56:34.173 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9602MB
2025-09-08 18:56:39.297 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9579MB
2025-09-08 18:56:44.416 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9587MB
2025-09-08 18:56:47.192 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:56:47.192 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:56:47.193 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:56:47.193 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:56:47.193 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:56:47.193 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:56:47.304 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:56:47.304 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:56:47.355 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:56:47.737 - RealTimeSTT: root - DEBUG - Realtime text detected:  Good luck.
2025-09-08 18:56:47.738 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:56:47.847 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 17408
2025-09-08 18:56:47.847 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.088
2025-09-08 18:56:47.897 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:56:48.259 - RealTimeSTT: root - DEBUG - Realtime text detected:  Okay, let's run it.
2025-09-08 18:56:48.259 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying real-time text: 'NoneType' object has no attribute 'get'
2025-09-08 18:56:48.344 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:56:48.344 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:56:48.344 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:56:48.344 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:56:48.368 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 0
2025-09-08 18:56:48.368 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.000
2025-09-08 18:56:48.399 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:56:48.400 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:56:48.401 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:56:48.403 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:56:48.411 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:56:49.542 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9707MB
2025-09-08 18:56:51.010 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 2.61 seconds
2025-09-08 18:56:51.010 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Have a good one....
2025-09-08 18:56:51.071 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:56:51.072 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:56:51.073 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:56:51.073 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:56:51.074 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Have a good one....
2025-09-08 18:56:51.074 - RealTimeSTT: __main__ - [31mERROR[0m - Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:56:51.074 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 0.063s
2025-09-08 18:56:51.095 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:56:51.095 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:56:51.096 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:56:54.660 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9535MB
2025-09-08 18:56:59.783 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9505MB
2025-09-08 18:57:04.909 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9558MB
2025-09-08 18:57:07.864 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:57:07.865 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:57:07.865 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:57:07.866 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:57:07.867 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:57:07.866 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:57:07.982 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:57:07.982 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:57:08.024 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:57:08.343 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.004614 < -1.000000)
2025-09-08 18:57:08.344 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.620040 > 0.600000)
2025-09-08 18:57:08.344 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:57:08.456 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 16384
2025-09-08 18:57:08.456 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.024
2025-09-08 18:57:08.498 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:57:08.836 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.016162 < -1.000000)
2025-09-08 18:57:08.836 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.745836 > 0.600000)
2025-09-08 18:57:08.836 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:57:08.946 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 23552
2025-09-08 18:57:08.946 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.472
2025-09-08 18:57:08.989 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:57:09.014 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:57:09.014 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:57:09.014 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:57:09.054 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:57:09.055 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:57:09.056 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:57:09.123 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:57:09.123 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:57:09.415 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.103987 < -1.000000)
2025-09-08 18:57:09.415 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.802809 > 0.600000)
2025-09-08 18:57:09.415 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:57:10.029 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9585MB
2025-09-08 18:57:11.974 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 2.92 seconds
2025-09-08 18:57:11.974 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Thank you....
2025-09-08 18:57:12.028 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error in text emotion detection: list indices must be integers or slices, not str
2025-09-08 18:57:12.029 - RealTimeSTT: core.emotion_detector - [31mERROR[0m - Error detecting emotion: 'happy'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 262, in detect_emotion
    smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
  File "C:\Users\<USER>\Desktop\realstt\core\emotion_detector.py", line 399, in _apply_temporal_smoothing
    combined[emotion] = text_weight * text_score + audio_weight * audio_score
KeyError: 'happy'
2025-09-08 18:57:12.030 - RealTimeSTT: core.text_formatter - [31mERROR[0m - Error adding emotion tag: argument of type 'EmotionResult' is not iterable
2025-09-08 18:57:12.030 - RealTimeSTT: ui.console_display - [31mERROR[0m - Error displaying final text: 'EmotionResult' object has no attribute 'get'
2025-09-08 18:57:12.031 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Thank you....
2025-09-08 18:57:12.031 - RealTimeSTT: __main__ - [31mERROR[0m - Error handling emotion: 'EmotionResult' object is not subscriptable
2025-09-08 18:57:12.032 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 0.056s
2025-09-08 18:57:12.053 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:57:12.053 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:57:12.054 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:57:15.153 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9618MB
2025-09-08 18:57:15.155 - RealTimeSTT: utils.performance - [32mINFO[0m - Performance summary - Latency: 0.2ms avg, CPU: 14.8%, Memory: 14028MB, GPU Memory: 1850MB, Score: 100.0/100
2025-09-08 18:57:20.271 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9579MB
2025-09-08 18:57:22.274 - RealTimeSTT: root - INFO - State changed from 'listening' to 'inactive'
2025-09-08 18:57:22.522 - RealTimeSTT: __main__ - [32mINFO[0m - System shutdown initiated
2025-09-08 18:57:22.523 - RealTimeSTT: core.voice_processor - [32mINFO[0m - Stopping voice processor...
2025-09-08 18:57:22.524 - RealTimeSTT: root - DEBUG - Finishing recording thread
2025-09-08 18:57:22.535 - RealTimeSTT: root - DEBUG - Terminating reader process
2025-09-08 18:57:23.651 - RealTimeSTT: root - DEBUG - Terminating transcription process
2025-09-08 18:57:23.658 - RealTimeSTT: root - DEBUG - Finishing realtime thread
2025-09-08 18:57:24.219 - RealTimeSTT: core.voice_processor - [32mINFO[0m - Voice processor stopped
2025-09-08 18:57:24.220 - RealTimeSTT: ui.status_monitor - [32mINFO[0m - Stopping status monitoring...
2025-09-08 18:57:24.706 - RealTimeSTT: ui.status_monitor - [32mINFO[0m - Status monitoring stopped
2025-09-08 18:57:24.707 - RealTimeSTT: utils.performance - [32mINFO[0m - Stopping performance monitoring...
2025-09-08 18:57:25.279 - RealTimeSTT: utils.performance - [32mINFO[0m - Performance monitoring stopped
2025-09-08 18:57:25.279 - RealTimeSTT: models.model_manager - [32mINFO[0m - Shutting down ModelManager...
2025-09-08 18:57:25.518 - RealTimeSTT: models.model_manager - [32mINFO[0m - GPU memory optimized
2025-09-08 18:57:25.520 - RealTimeSTT: models.model_manager - [32mINFO[0m - Memory after optimization: 0.15GB
2025-09-08 18:57:25.520 - RealTimeSTT: models.model_manager - [32mINFO[0m - ModelManager shutdown complete
2025-09-08 18:57:25.521 - RealTimeSTT: __main__ - [32mINFO[0m - System shutdown completed
2025-09-08 18:57:25.522 - RealTimeSTT: ui.console_display - [32mINFO[0m - Starting console display...
2025-09-08 18:57:25.553 - RealTimeSTT: ui.console_display - [32mINFO[0m - Console display started
2025-09-08 18:57:25.554 - RealTimeSTT: __main__ - [31mERROR[0m - Console mode error: object NoneType can't be used in 'await' expression
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\realstt\main.py", line 263, in start_console_mode
    print("=" * 60)
TypeError: object NoneType can't be used in 'await' expression
2025-09-08 18:58:25.812 - RealTimeSTT: root - INFO - Starting RealTimeSTT
2025-09-08 18:58:25.816 - RealTimeSTT: root - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-09-08 18:58:25.824 - RealTimeSTT: root - INFO - Initializing faster_whisper realtime transcription model tiny.en
2025-09-08 18:58:26.072 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "GET /api/models/Systran/faster-whisper-tiny.en/revision/main HTTP/1.1" 200 947
2025-09-08 18:58:26.326 - RealTimeSTT: root - DEBUG - Faster_whisper realtime speech to text transcription model initialized successfully
2025-09-08 18:58:26.326 - RealTimeSTT: root - INFO - Initializing WebRTC voice with Sensitivity 2
2025-09-08 18:58:26.326 - RealTimeSTT: root - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-09-08 18:58:28.000 - RealTimeSTT: root - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-09-08 18:58:28.001 - RealTimeSTT: root - DEBUG - Starting realtime worker
2025-09-08 18:58:28.001 - RealTimeSTT: root - DEBUG - Waiting for main transcription model to start
2025-09-08 18:58:29.487 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9856MB
2025-09-08 18:58:31.715 - RealTimeSTT: root - DEBUG - Main transcription model ready
2025-09-08 18:58:31.716 - RealTimeSTT: root - DEBUG - RealtimeSTT initialization completed successfully
2025-09-08 18:58:31.716 - RealTimeSTT: core.voice_processor - [32mINFO[0m - RealtimeSTT recorder configured successfully
2025-09-08 18:58:31.717 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Initializing emotion detection models...
2025-09-08 18:58:31.718 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Loading text emotion classifier...
2025-09-08 18:58:31.961 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "HEAD /j-hartmann/emotion-english-distilroberta-base/resolve/main/config.json HTTP/1.1" 307 0
2025-09-08 18:58:31.999 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "HEAD /api/resolve-cache/models/j-hartmann/emotion-english-distilroberta-base/0e1cd914e3d46199ed785853e12b57304e04178b/config.json HTTP/1.1" 200 0
2025-09-08 18:58:32.252 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "HEAD /j-hartmann/emotion-english-distilroberta-base/resolve/main/model.safetensors HTTP/1.1" 404 0
2025-09-08 18:58:32.732 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Text emotion classifier loaded successfully
2025-09-08 18:58:32.733 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Custom audio model not found, creating feature-based classifier...
2025-09-08 18:58:32.733 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Creating feature-based audio emotion classifier...
2025-09-08 18:58:32.734 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Emotion detection models initialized successfully
2025-09-08 18:58:32.734 - RealTimeSTT: core.text_formatter - [32mINFO[0m - TextFormatter ready for processing
2025-09-08 18:58:32.734 - RealTimeSTT: core.voice_processor - [32mINFO[0m - Voice processor initialization completed
2025-09-08 18:58:32.735 - RealTimeSTT: ui.status_monitor - DEBUG - Status callback added
2025-09-08 18:58:32.735 - RealTimeSTT: __main__ - DEBUG - System callbacks configured
2025-09-08 18:58:32.735 - RealTimeSTT: __main__ - [32mINFO[0m - System initialization completed
2025-09-08 18:58:32.749 - RealTimeSTT: core.voice_processor - [32mINFO[0m - Starting voice processor...
2025-09-08 18:58:32.749 - RealTimeSTT: core.voice_processor - [32mINFO[0m - Voice processing loop started
2025-09-08 18:58:34.605 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 10920MB
2025-09-08 18:58:39.726 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 10921MB
2025-09-08 18:58:44.840 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 10945MB
2025-09-08 18:58:47.208 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:58:47.252 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:58:48.549 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:58:48.549 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:58:49.965 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 11249MB
2025-09-08 18:58:51.732 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Hello, can I hear you?...
2025-09-08 18:58:53.489 - RealTimeSTT: core.emotion_detector - DEBUG - Emotion detected: neutral (0.00)
2025-09-08 18:58:53.490 - RealTimeSTT: __main__ - [32mINFO[0m - High-confidence transcription: Hello, can I hear you?...
2025-09-08 18:58:53.490 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 1.758s
2025-09-08 18:58:55.094 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 11542MB
2025-09-08 18:59:00.212 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 11542MB
2025-09-08 18:59:05.336 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 11744MB
2025-09-08 18:59:07.886 - RealTimeSTT: __main__ - [32mINFO[0m - System shutdown initiated
2025-09-08 18:59:07.886 - RealTimeSTT: core.voice_processor - [32mINFO[0m - Stopping voice processor...
2025-09-08 18:59:08.568 - RealTimeSTT: core.voice_processor - [32mINFO[0m - Voice processor stopped
2025-09-08 18:59:08.569 - RealTimeSTT: ui.status_monitor - [32mINFO[0m - Stopping status monitoring...
2025-09-08 18:59:09.678 - RealTimeSTT: ui.status_monitor - [32mINFO[0m - Status monitoring stopped
2025-09-08 18:59:09.679 - RealTimeSTT: utils.performance - [32mINFO[0m - Stopping performance monitoring...
2025-09-08 18:59:10.350 - RealTimeSTT: utils.performance - [32mINFO[0m - Performance monitoring stopped
2025-09-08 18:59:10.350 - RealTimeSTT: models.model_manager - [32mINFO[0m - Shutting down ModelManager...
2025-09-08 18:59:10.510 - RealTimeSTT: models.model_manager - [32mINFO[0m - GPU memory optimized
2025-09-08 18:59:10.511 - RealTimeSTT: models.model_manager - [32mINFO[0m - Memory after optimization: 0.15GB
2025-09-08 18:59:10.512 - RealTimeSTT: models.model_manager - [32mINFO[0m - ModelManager shutdown complete
2025-09-08 18:59:10.513 - RealTimeSTT: __main__ - [32mINFO[0m - System shutdown completed
2025-09-08 18:59:39.428 - RealTimeSTT: root - INFO - Starting RealTimeSTT
2025-09-08 18:59:39.432 - RealTimeSTT: root - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-09-08 18:59:39.436 - RealTimeSTT: root - INFO - Initializing faster_whisper realtime transcription model tiny.en
2025-09-08 18:59:40.079 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "GET /api/models/Systran/faster-whisper-tiny.en/revision/main HTTP/1.1" 200 947
2025-09-08 18:59:40.307 - RealTimeSTT: root - DEBUG - Faster_whisper realtime speech to text transcription model initialized successfully
2025-09-08 18:59:40.308 - RealTimeSTT: root - INFO - Initializing WebRTC voice with Sensitivity 2
2025-09-08 18:59:40.308 - RealTimeSTT: root - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-09-08 18:59:41.313 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 9781MB
2025-09-08 18:59:41.836 - RealTimeSTT: root - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-09-08 18:59:41.837 - RealTimeSTT: root - DEBUG - Starting realtime worker
2025-09-08 18:59:41.837 - RealTimeSTT: root - DEBUG - Waiting for main transcription model to start
2025-09-08 18:59:45.056 - RealTimeSTT: root - DEBUG - Main transcription model ready
2025-09-08 18:59:45.057 - RealTimeSTT: root - DEBUG - RealtimeSTT initialization completed successfully
2025-09-08 18:59:45.057 - RealTimeSTT: core.voice_processor - [32mINFO[0m - RealtimeSTT recorder configured successfully
2025-09-08 18:59:45.057 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Initializing emotion detection models...
2025-09-08 18:59:45.058 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Loading text emotion classifier...
2025-09-08 18:59:45.309 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "HEAD /j-hartmann/emotion-english-distilroberta-base/resolve/main/config.json HTTP/1.1" 307 0
2025-09-08 18:59:45.351 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "HEAD /api/resolve-cache/models/j-hartmann/emotion-english-distilroberta-base/0e1cd914e3d46199ed785853e12b57304e04178b/config.json HTTP/1.1" 200 0
2025-09-08 18:59:45.603 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "HEAD /j-hartmann/emotion-english-distilroberta-base/resolve/main/model.safetensors HTTP/1.1" 404 0
2025-09-08 18:59:46.061 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Text emotion classifier loaded successfully
2025-09-08 18:59:46.061 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Custom audio model not found, creating feature-based classifier...
2025-09-08 18:59:46.062 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Creating feature-based audio emotion classifier...
2025-09-08 18:59:46.062 - RealTimeSTT: core.emotion_detector - [32mINFO[0m - Emotion detection models initialized successfully
2025-09-08 18:59:46.062 - RealTimeSTT: core.text_formatter - [32mINFO[0m - TextFormatter ready for processing
2025-09-08 18:59:46.063 - RealTimeSTT: core.voice_processor - [32mINFO[0m - Voice processor initialization completed
2025-09-08 18:59:46.063 - RealTimeSTT: ui.status_monitor - DEBUG - Status callback added
2025-09-08 18:59:46.063 - RealTimeSTT: main - DEBUG - System callbacks configured
2025-09-08 18:59:46.064 - RealTimeSTT: main - [32mINFO[0m - System initialization completed
2025-09-08 18:59:46.080 - RealTimeSTT: core.voice_processor - [32mINFO[0m - Starting voice processor...
2025-09-08 18:59:46.080 - RealTimeSTT: core.voice_processor - [32mINFO[0m - Voice processing loop started
2025-09-08 18:59:46.080 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:59:46.080 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:59:46.380 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:59:46.434 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 11037MB
2025-09-08 18:59:48.915 - RealTimeSTT: root - INFO - voice activity detected
2025-09-08 18:59:48.916 - RealTimeSTT: root - INFO - recording started
2025-09-08 18:59:48.916 - RealTimeSTT: root - INFO - State changed from 'listening' to 'recording'
2025-09-08 18:59:48.917 - RealTimeSTT: root - DEBUG - Waiting for recording stop
2025-09-08 18:59:48.917 - RealTimeSTT: core.voice_processor - DEBUG - Recording started
2025-09-08 18:59:48.919 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = True
2025-09-08 18:59:49.038 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 8192
2025-09-08 18:59:49.039 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:00.512
2025-09-08 18:59:49.109 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:59:49.563 - RealTimeSTT: faster_whisper - DEBUG - Log probability threshold is not met with temperature 0.0 (-1.114606 < -1.000000)
2025-09-08 18:59:49.563 - RealTimeSTT: faster_whisper - DEBUG - No speech threshold is met (0.620902 > 0.600000)
2025-09-08 18:59:49.564 - RealTimeSTT: root - DEBUG - Realtime text detected: 
2025-09-08 18:59:49.678 - RealTimeSTT: root - DEBUG - Current realtime buffer size: 18432
2025-09-08 18:59:49.679 - RealTimeSTT: faster_whisper - INFO - Processing audio with duration 00:01.152
2025-09-08 18:59:49.722 - RealTimeSTT: faster_whisper - DEBUG - Processing segment at 00:00.000
2025-09-08 18:59:50.064 - RealTimeSTT: root - INFO - recording stopped
2025-09-08 18:59:50.065 - RealTimeSTT: core.voice_processor - DEBUG - Recording stopped
2025-09-08 18:59:50.065 - RealTimeSTT: ui.status_monitor - DEBUG - Status updated: recording = False
2025-09-08 18:59:50.065 - RealTimeSTT: root - INFO - State changed from 'recording' to 'inactive'
2025-09-08 18:59:50.075 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'transcribing'
2025-09-08 18:59:50.077 - RealTimeSTT: root - DEBUG - Adding transcription request, no early transcription started
2025-09-08 18:59:50.082 - RealTimeSTT: root - DEBUG - Realtime text detected:  Can you hear it?
2025-09-08 18:59:50.090 - RealTimeSTT: root - DEBUG - Receive from parent_transcription_pipe after sendiung transcription request, transcribe_count: 1
2025-09-08 18:59:50.123 - RealTimeSTT: root - INFO - State changed from 'transcribing' to 'inactive'
2025-09-08 18:59:51.558 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 11011MB
2025-09-08 18:59:52.693 - RealTimeSTT: root - DEBUG - Model small.en completed transcription in 2.62 seconds
2025-09-08 18:59:52.693 - RealTimeSTT: core.voice_processor - DEBUG - Processing final transcription: Can you hear me?...
2025-09-08 18:59:53.597 - RealTimeSTT: core.emotion_detector - DEBUG - Emotion detected: neutral (0.00)
2025-09-08 18:59:53.598 - RealTimeSTT: main - [32mINFO[0m - High-confidence transcription: Can you hear me?...
2025-09-08 18:59:53.599 - RealTimeSTT: core.voice_processor - DEBUG - Final transcription processed in 0.905s
2025-09-08 18:59:53.618 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 18:59:53.618 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 18:59:53.620 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 18:59:56.676 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 11539MB
2025-09-08 19:00:01.797 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 11596MB
2025-09-08 19:00:06.915 - RealTimeSTT: utils.performance - [33mWARNING[0m - Performance alert: High memory usage: 11714MB
2025-09-08 19:00:08.338 - RealTimeSTT: root - INFO - KeyboardInterrupt in wait_audio, shutting down
2025-09-08 19:00:08.339 - RealTimeSTT: root - DEBUG - Finishing recording thread
2025-09-08 19:00:08.354 - RealTimeSTT: root - DEBUG - Terminating reader process
2025-09-08 19:00:09.045 - RealTimeSTT: root - DEBUG - Terminating transcription process
2025-09-08 19:00:09.067 - RealTimeSTT: root - DEBUG - Finishing realtime thread
2025-09-08 19:00:09.173 - RealTimeSTT: root - INFO - KeyboardInterrupt in text() method
2025-09-08 19:00:09.175 - RealTimeSTT: main - [32mINFO[0m - System shutdown initiated
2025-09-08 19:00:09.175 - RealTimeSTT: core.voice_processor - [32mINFO[0m - Stopping voice processor...
2025-09-08 19:00:09.176 - RealTimeSTT: core.voice_processor - [32mINFO[0m - Voice processor stopped
2025-09-08 19:00:09.176 - RealTimeSTT: ui.status_monitor - [32mINFO[0m - Stopping status monitoring...
2025-09-08 19:00:09.773 - RealTimeSTT: ui.status_monitor - [32mINFO[0m - Status monitoring stopped
2025-09-08 19:00:09.774 - RealTimeSTT: utils.performance - [32mINFO[0m - Stopping performance monitoring...
2025-09-08 19:00:11.923 - RealTimeSTT: utils.performance - [32mINFO[0m - Performance monitoring stopped
2025-09-08 19:00:11.924 - RealTimeSTT: models.model_manager - [32mINFO[0m - Shutting down ModelManager...
2025-09-08 19:00:12.099 - RealTimeSTT: models.model_manager - [32mINFO[0m - GPU memory optimized
2025-09-08 19:00:12.100 - RealTimeSTT: models.model_manager - [32mINFO[0m - Memory after optimization: 0.15GB
2025-09-08 19:00:12.101 - RealTimeSTT: models.model_manager - [32mINFO[0m - ModelManager shutdown complete
2025-09-08 19:00:12.102 - RealTimeSTT: main - [32mINFO[0m - System shutdown completed
2025-09-08 19:01:20.093 - RealTimeSTT: root - INFO - Starting RealTimeSTT
2025-09-08 19:01:20.105 - RealTimeSTT: root - INFO - Initializing audio recording (creating pyAudio input stream, sample rate: 16000 buffer size: 512
2025-09-08 19:01:20.109 - RealTimeSTT: root - INFO - Initializing faster_whisper realtime transcription model tiny.en
2025-09-08 19:01:20.113 - RealTimeSTT: urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): huggingface.co:443
2025-09-08 19:01:20.454 - RealTimeSTT: urllib3.connectionpool - DEBUG - https://huggingface.co:443 "GET /api/models/Systran/faster-whisper-tiny.en/revision/main HTTP/1.1" 200 947
2025-09-08 19:01:20.678 - RealTimeSTT: root - DEBUG - Faster_whisper realtime speech to text transcription model initialized successfully
2025-09-08 19:01:20.678 - RealTimeSTT: root - INFO - Initializing WebRTC voice with Sensitivity 3
2025-09-08 19:01:20.678 - RealTimeSTT: root - DEBUG - WebRTC VAD voice activity detection engine initialized successfully
2025-09-08 19:01:22.129 - RealTimeSTT: root - DEBUG - Silero VAD voice activity detection engine initialized successfully
2025-09-08 19:01:22.131 - RealTimeSTT: root - DEBUG - Starting realtime worker
2025-09-08 19:01:22.131 - RealTimeSTT: root - DEBUG - Waiting for main transcription model to start
2025-09-08 19:03:37.493 - RealTimeSTT: root - DEBUG - Main transcription model ready
2025-09-08 19:03:37.528 - RealTimeSTT: root - DEBUG - RealtimeSTT initialization completed successfully
2025-09-08 19:03:38.104 - RealTimeSTT: root - INFO - Setting listen time
2025-09-08 19:03:38.104 - RealTimeSTT: root - INFO - State changed from 'inactive' to 'listening'
2025-09-08 19:03:39.177 - RealTimeSTT: root - DEBUG - Waiting for recording start
2025-09-08 19:09:46.810 - RealTimeSTT: root - INFO - KeyboardInterrupt in wait_audio, shutting down
2025-09-08 19:09:46.818 - RealTimeSTT: root - DEBUG - Finishing recording thread
2025-09-08 19:09:46.830 - RealTimeSTT: root - DEBUG - Terminating reader process
2025-09-08 19:09:50.019 - RealTimeSTT: root - DEBUG - Terminating transcription process
2025-09-08 19:09:50.020 - RealTimeSTT: root - DEBUG - Finishing realtime thread
2025-09-08 19:09:50.954 - RealTimeSTT: root - INFO - KeyboardInterrupt in text() method
