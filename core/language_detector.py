"""
Multi-language Support & Language Detection
Implements automatic language detection, multi-language transcription,
and dynamic language switching capabilities
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import threading
import time
from collections import deque, Counter
import re

from utils import get_logger

class LanguageDetector:
    """Automatic language detection from audio and text features"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Supported languages with their characteristics
        self.supported_languages = {
            'en': {
                'name': 'English',
                'whisper_code': 'en',
                'common_words': ['the', 'and', 'to', 'of', 'a', 'in', 'is', 'it', 'you', 'that'],
                'character_patterns': r'[a-zA-Z\s\.,!?;:\'"()-]',
                'vowel_ratio_range': (0.35, 0.45),
                'avg_word_length': 4.5
            },
            'es': {
                'name': 'Spanish',
                'whisper_code': 'es',
                'common_words': ['el', 'la', 'de', 'que', 'y', 'a', 'en', 'un', 'es', 'se'],
                'character_patterns': r'[a-zA-ZáéíóúüñÁÉÍÓÚÜÑ\s\.,!?;:\'"()-]',
                'vowel_ratio_range': (0.45, 0.55),
                'avg_word_length': 5.2
            },
            'fr': {
                'name': 'French',
                'whisper_code': 'fr',
                'common_words': ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir'],
                'character_patterns': r'[a-zA-ZàâäéèêëïîôöùûüÿçÀÂÄÉÈÊËÏÎÔÖÙÛÜŸÇ\s\.,!?;:\'"()-]',
                'vowel_ratio_range': (0.40, 0.50),
                'avg_word_length': 5.8
            },
            'de': {
                'name': 'German',
                'whisper_code': 'de',
                'common_words': ['der', 'die', 'und', 'in', 'den', 'von', 'zu', 'das', 'mit', 'sich'],
                'character_patterns': r'[a-zA-ZäöüßÄÖÜ\s\.,!?;:\'"()-]',
                'vowel_ratio_range': (0.35, 0.45),
                'avg_word_length': 6.1
            },
            'it': {
                'name': 'Italian',
                'whisper_code': 'it',
                'common_words': ['il', 'di', 'che', 'e', 'la', 'per', 'un', 'in', 'con', 'non'],
                'character_patterns': r'[a-zA-ZàèéìíîòóùúÀÈÉÌÍÎÒÓÙÚ\s\.,!?;:\'"()-]',
                'vowel_ratio_range': (0.45, 0.55),
                'avg_word_length': 5.0
            },
            'pt': {
                'name': 'Portuguese',
                'whisper_code': 'pt',
                'common_words': ['o', 'de', 'a', 'e', 'do', 'da', 'em', 'um', 'para', 'é'],
                'character_patterns': r'[a-zA-ZáàâãéêíóôõúçÁÀÂÃÉÊÍÓÔÕÚÇ\s\.,!?;:\'"()-]',
                'vowel_ratio_range': (0.45, 0.55),
                'avg_word_length': 4.8
            },
            'ru': {
                'name': 'Russian',
                'whisper_code': 'ru',
                'common_words': ['в', 'и', 'не', 'на', 'я', 'быть', 'тот', 'он', 'с', 'а'],
                'character_patterns': r'[а-яёА-ЯЁ\s\.,!?;:\'"()-]',
                'vowel_ratio_range': (0.40, 0.50),
                'avg_word_length': 5.5
            },
            'zh': {
                'name': 'Chinese',
                'whisper_code': 'zh',
                'common_words': ['的', '一', '是', '在', '不', '了', '有', '和', '人', '这'],
                'character_patterns': r'[\u4e00-\u9fff\s\.,!?;:\'"()-]',
                'vowel_ratio_range': (0.0, 0.0),  # Not applicable for Chinese
                'avg_word_length': 1.5
            },
            'ja': {
                'name': 'Japanese',
                'whisper_code': 'ja',
                'common_words': ['の', 'に', 'は', 'を', 'た', 'が', 'で', 'て', 'と', 'し'],
                'character_patterns': r'[\u3040-\u309f\u30a0-\u30ff\u4e00-\u9fff\s\.,!?;:\'"()-]',
                'vowel_ratio_range': (0.0, 0.0),  # Not applicable for Japanese
                'avg_word_length': 2.0
            },
            'ko': {
                'name': 'Korean',
                'whisper_code': 'ko',
                'common_words': ['이', '의', '가', '을', '는', '에', '한', '하', '으로', '로'],
                'character_patterns': r'[\uac00-\ud7af\s\.,!?;:\'"()-]',
                'vowel_ratio_range': (0.0, 0.0),  # Not applicable for Korean
                'avg_word_length': 2.5
            }
        }
        
        # Detection history for smoothing
        self.detection_history = deque(maxlen=10)
        self.confidence_threshold = 0.6
        
        # Statistics
        self.detections_count = 0
        self.detection_times = deque(maxlen=100)
        
        self.logger.info(f"LanguageDetector initialized with {len(self.supported_languages)} languages")
    
    def detect_language_from_text(self, text: str) -> Tuple[str, float]:
        """Detect language from transcribed text"""
        try:
            start_time = time.time()
            
            if not text or len(text.strip()) < 10:
                return 'en', 0.0  # Default to English for short text
            
            text = text.lower().strip()
            scores = {}
            
            for lang_code, lang_info in self.supported_languages.items():
                score = self._calculate_text_language_score(text, lang_info)
                scores[lang_code] = score
            
            # Find best match
            best_language = max(scores, key=scores.get)
            confidence = scores[best_language]
            
            # Apply confidence threshold
            if confidence < self.confidence_threshold:
                best_language = 'en'  # Default to English
                confidence = 0.5
            
            # Update statistics
            detection_time = time.time() - start_time
            self.detection_times.append(detection_time)
            self.detections_count += 1
            
            # Add to history for smoothing
            self.detection_history.append((best_language, confidence))
            
            self.logger.debug(f"Language detected: {best_language} (confidence: {confidence:.3f})")
            
            return best_language, confidence
            
        except Exception as e:
            self.logger.error(f"Error in language detection: {str(e)}")
            return 'en', 0.0
    
    def _calculate_text_language_score(self, text: str, lang_info: Dict) -> float:
        """Calculate language score based on text characteristics"""
        score = 0.0
        
        # Character pattern matching
        pattern_matches = len(re.findall(lang_info['character_patterns'], text))
        total_chars = len(text)
        if total_chars > 0:
            char_score = pattern_matches / total_chars
            score += char_score * 0.4
        
        # Common words matching
        words = text.split()
        if words:
            common_word_count = sum(1 for word in words if word in lang_info['common_words'])
            word_score = common_word_count / len(words)
            score += word_score * 0.4
        
        # Vowel ratio (for Latin-based languages)
        if lang_info['vowel_ratio_range'][0] > 0:
            vowel_ratio = self._calculate_vowel_ratio(text)
            min_ratio, max_ratio = lang_info['vowel_ratio_range']
            if min_ratio <= vowel_ratio <= max_ratio:
                ratio_score = 1.0 - abs(vowel_ratio - (min_ratio + max_ratio) / 2) / ((max_ratio - min_ratio) / 2)
                score += ratio_score * 0.1
        
        # Average word length
        if words:
            avg_word_len = sum(len(word) for word in words) / len(words)
            expected_len = lang_info['avg_word_length']
            len_score = 1.0 - min(abs(avg_word_len - expected_len) / expected_len, 1.0)
            score += len_score * 0.1
        
        return min(score, 1.0)
    
    def _calculate_vowel_ratio(self, text: str) -> float:
        """Calculate ratio of vowels to total letters"""
        vowels = 'aeiouáéíóúàèìòùâêîôûäëïöüãõ'
        letters = re.findall(r'[a-zA-ZáéíóúàèìòùâêîôûäëïöüãõÁÉÍÓÚÀÈÌÒÙÂÊÎÔÛÄËÏÖÜÃÕ]', text.lower())
        
        if not letters:
            return 0.0
        
        vowel_count = sum(1 for char in letters if char in vowels)
        return vowel_count / len(letters)
    
    def get_smoothed_language(self) -> Tuple[str, float]:
        """Get language with temporal smoothing"""
        if not self.detection_history:
            return 'en', 0.0
        
        # Count language occurrences
        language_counts = Counter(lang for lang, _ in self.detection_history)
        
        # Get most common language
        most_common_lang = language_counts.most_common(1)[0][0]
        
        # Calculate average confidence for the most common language
        confidences = [conf for lang, conf in self.detection_history if lang == most_common_lang]
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0
        
        return most_common_lang, avg_confidence
    
    def get_supported_languages(self) -> Dict[str, str]:
        """Get list of supported languages"""
        return {code: info['name'] for code, info in self.supported_languages.items()}
    
    def get_detection_statistics(self) -> Dict[str, Any]:
        """Get language detection statistics"""
        if not self.detection_times:
            return {}
        
        times = list(self.detection_times)
        return {
            'total_detections': self.detections_count,
            'avg_detection_time_ms': np.mean(times) * 1000,
            'max_detection_time_ms': np.max(times) * 1000,
            'min_detection_time_ms': np.min(times) * 1000,
            'recent_languages': [lang for lang, _ in list(self.detection_history)[-5:]],
            'supported_languages': len(self.supported_languages)
        }
    
    def reset_history(self):
        """Reset detection history"""
        self.detection_history.clear()
        self.logger.info("Language detection history reset")

class MultiLanguageManager:
    """Manages multi-language transcription and model switching"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Components
        self.language_detector = LanguageDetector()
        
        # Current language state
        self.current_language = 'en'
        self.language_confidence = 1.0
        self.auto_detect_enabled = True
        
        # Language switching parameters
        self.min_confidence_for_switch = 0.8
        self.min_detections_for_switch = 3
        self.switch_cooldown = 5.0  # seconds
        self.last_switch_time = 0
        
        # Language-specific models
        self.language_models = {
            'en': 'medium.en',
            'es': 'medium',
            'fr': 'medium',
            'de': 'medium',
            'it': 'medium',
            'pt': 'medium',
            'ru': 'medium',
            'zh': 'medium',
            'ja': 'medium',
            'ko': 'medium'
        }
        
        # Statistics
        self.language_switches = 0
        self.transcription_counts = Counter()
        
        # Thread safety
        self.language_lock = threading.Lock()
        
        self.logger.info("MultiLanguageManager initialized")
    
    def detect_and_update_language(self, text: str) -> Tuple[str, bool]:
        """Detect language and update current language if needed"""
        try:
            with self.language_lock:
                # Detect language
                detected_lang, confidence = self.language_detector.detect_language_from_text(text)
                
                # Check if we should switch languages
                should_switch = self._should_switch_language(detected_lang, confidence)
                
                if should_switch:
                    old_language = self.current_language
                    self.current_language = detected_lang
                    self.language_confidence = confidence
                    self.last_switch_time = time.time()
                    self.language_switches += 1
                    
                    self.logger.info(f"Language switched from {old_language} to {detected_lang} (confidence: {confidence:.3f})")
                    return detected_lang, True
                
                return self.current_language, False
                
        except Exception as e:
            self.logger.error(f"Error in language detection and update: {str(e)}")
            return self.current_language, False
    
    def _should_switch_language(self, detected_lang: str, confidence: float) -> bool:
        """Determine if language should be switched"""
        if not self.auto_detect_enabled:
            return False
        
        if detected_lang == self.current_language:
            return False
        
        if confidence < self.min_confidence_for_switch:
            return False
        
        # Check cooldown period
        if time.time() - self.last_switch_time < self.switch_cooldown:
            return False
        
        # Check if we have enough consistent detections
        smoothed_lang, smoothed_conf = self.language_detector.get_smoothed_language()
        if smoothed_lang != detected_lang or smoothed_conf < self.min_confidence_for_switch:
            return False
        
        return True
    
    def get_model_for_language(self, language: str) -> str:
        """Get appropriate Whisper model for language"""
        return self.language_models.get(language, 'medium')
    
    def set_language(self, language: str, disable_auto_detect: bool = False):
        """Manually set language"""
        with self.language_lock:
            if language in self.language_detector.supported_languages:
                self.current_language = language
                self.language_confidence = 1.0
                self.auto_detect_enabled = not disable_auto_detect
                
                if disable_auto_detect:
                    self.language_detector.reset_history()
                
                self.logger.info(f"Language manually set to {language} (auto-detect: {self.auto_detect_enabled})")
            else:
                self.logger.warning(f"Unsupported language: {language}")
    
    def enable_auto_detection(self, enabled: bool = True):
        """Enable or disable automatic language detection"""
        with self.language_lock:
            self.auto_detect_enabled = enabled
            if enabled:
                self.language_detector.reset_history()
            
            self.logger.info(f"Auto language detection {'enabled' if enabled else 'disabled'}")
    
    def get_current_language_info(self) -> Dict[str, Any]:
        """Get current language information"""
        with self.language_lock:
            lang_info = self.language_detector.supported_languages.get(self.current_language, {})
            
            return {
                'code': self.current_language,
                'name': lang_info.get('name', 'Unknown'),
                'confidence': self.language_confidence,
                'auto_detect_enabled': self.auto_detect_enabled,
                'model': self.get_model_for_language(self.current_language),
                'whisper_code': lang_info.get('whisper_code', self.current_language)
            }
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get multi-language processing statistics"""
        with self.language_lock:
            detector_stats = self.language_detector.get_detection_statistics()
            
            return {
                'current_language': self.current_language,
                'language_switches': self.language_switches,
                'transcription_counts': dict(self.transcription_counts),
                'auto_detect_enabled': self.auto_detect_enabled,
                'detection_stats': detector_stats,
                'supported_languages': self.language_detector.get_supported_languages()
            }
    
    def record_transcription(self, language: str):
        """Record a transcription for statistics"""
        with self.language_lock:
            self.transcription_counts[language] += 1
    
    def reset_statistics(self):
        """Reset all statistics"""
        with self.language_lock:
            self.language_switches = 0
            self.transcription_counts.clear()
            self.language_detector.reset_history()
        
        self.logger.info("Multi-language statistics reset")

if __name__ == "__main__":
    # Test multi-language system
    manager = MultiLanguageManager()
    
    # Test texts in different languages
    test_texts = [
        ("Hello, how are you doing today?", "en"),
        ("Hola, ¿cómo estás hoy?", "es"),
        ("Bonjour, comment allez-vous aujourd'hui?", "fr"),
        ("Hallo, wie geht es dir heute?", "de"),
        ("Ciao, come stai oggi?", "it"),
        ("Olá, como você está hoje?", "pt"),
        ("Привет, как дела сегодня?", "ru"),
        ("你好，你今天怎么样？", "zh"),
        ("こんにちは、今日はいかがですか？", "ja"),
        ("안녕하세요, 오늘 어떻게 지내세요?", "ko")
    ]
    
    print("Testing multi-language detection:")
    for text, expected_lang in test_texts:
        detected_lang, switched = manager.detect_and_update_language(text)
        print(f"Text: {text[:30]}...")
        print(f"Expected: {expected_lang}, Detected: {detected_lang}, Switched: {switched}")
        print()
    
    # Get statistics
    stats = manager.get_statistics()
    print(f"Statistics: {stats}")
    
    print("Multi-language system test completed!")
