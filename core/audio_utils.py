"""
Audio Processing Utilities
Provides audio preprocessing, format conversion, and analysis functions
"""

import numpy as np
import librosa
import sounddevice as sd
from typing import Tuple, Optional, Dict, Any
import threading
import time

from config import AudioConfig
from utils import get_logger

class AudioUtils:
    """Utility class for audio processing operations"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.sample_rate = AudioConfig.SAMPLE_RATE
        self.channels = AudioConfig.CHANNELS
        
        # Audio analysis cache
        self._analysis_cache = {}
        self._cache_lock = threading.Lock()
        
        self.logger.info("AudioUtils initialized")
    
    def preprocess_audio(self, audio_data: np.ndarray, 
                        sample_rate: int = None) -> np.ndarray:
        """
        Preprocess audio data for optimal speech recognition
        
        Args:
            audio_data: Raw audio data
            sample_rate: Sample rate of the audio
            
        Returns:
            Preprocessed audio data
        """
        try:
            if sample_rate is None:
                sample_rate = self.sample_rate
            
            # Convert to float32 if needed
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
            
            # Normalize audio
            audio_data = self.normalize_audio(audio_data)
            
            # Resample if needed
            if sample_rate != self.sample_rate:
                audio_data = librosa.resample(
                    audio_data, 
                    orig_sr=sample_rate, 
                    target_sr=self.sample_rate
                )
            
            # Apply noise reduction
            audio_data = self.reduce_noise(audio_data)
            
            # Apply pre-emphasis filter
            audio_data = self.apply_preemphasis(audio_data)
            
            return audio_data
            
        except Exception as e:
            self.logger.error(f"Error preprocessing audio: {str(e)}")
            return audio_data
    
    def normalize_audio(self, audio_data: np.ndarray, 
                       target_level: float = 0.7) -> np.ndarray:
        """
        Normalize audio to target level
        
        Args:
            audio_data: Input audio data
            target_level: Target RMS level (0.0 to 1.0)
            
        Returns:
            Normalized audio data
        """
        try:
            # Calculate RMS
            rms = np.sqrt(np.mean(audio_data ** 2))
            
            if rms > 0:
                # Calculate scaling factor
                scale_factor = target_level / rms
                
                # Apply scaling with clipping protection
                normalized = audio_data * scale_factor
                normalized = np.clip(normalized, -1.0, 1.0)
                
                return normalized
            
            return audio_data
            
        except Exception as e:
            self.logger.error(f"Error normalizing audio: {str(e)}")
            return audio_data
    
    def reduce_noise(self, audio_data: np.ndarray, 
                    noise_factor: float = 0.1) -> np.ndarray:
        """
        Simple noise reduction using spectral subtraction
        
        Args:
            audio_data: Input audio data
            noise_factor: Noise reduction factor
            
        Returns:
            Noise-reduced audio data
        """
        try:
            # Estimate noise from first 0.5 seconds
            noise_sample_length = int(0.5 * self.sample_rate)
            if len(audio_data) > noise_sample_length:
                noise_sample = audio_data[:noise_sample_length]
                noise_power = np.mean(noise_sample ** 2)
                
                # Apply simple noise gate
                threshold = noise_power * (1 + noise_factor)
                mask = np.abs(audio_data) > np.sqrt(threshold)
                
                # Smooth the mask to avoid artifacts
                mask = self._smooth_mask(mask)
                
                return audio_data * mask
            
            return audio_data
            
        except Exception as e:
            self.logger.error(f"Error reducing noise: {str(e)}")
            return audio_data
    
    def _smooth_mask(self, mask: np.ndarray, 
                    window_size: int = 256) -> np.ndarray:
        """Smooth binary mask to reduce artifacts"""
        try:
            # Apply moving average to smooth the mask
            kernel = np.ones(window_size) / window_size
            smoothed = np.convolve(mask.astype(float), kernel, mode='same')
            return smoothed
        except:
            return mask.astype(float)
    
    def apply_preemphasis(self, audio_data: np.ndarray, 
                         coeff: float = 0.97) -> np.ndarray:
        """
        Apply pre-emphasis filter to enhance high frequencies
        
        Args:
            audio_data: Input audio data
            coeff: Pre-emphasis coefficient
            
        Returns:
            Pre-emphasized audio data
        """
        try:
            if len(audio_data) > 1:
                return np.append(audio_data[0], audio_data[1:] - coeff * audio_data[:-1])
            return audio_data
        except Exception as e:
            self.logger.error(f"Error applying pre-emphasis: {str(e)}")
            return audio_data
    
    def detect_voice_activity(self, audio_data: np.ndarray, 
                            frame_length: int = 2048,
                            hop_length: int = 512) -> np.ndarray:
        """
        Simple voice activity detection based on energy and spectral features
        
        Args:
            audio_data: Input audio data
            frame_length: Frame length for analysis
            hop_length: Hop length between frames
            
        Returns:
            Boolean array indicating voice activity
        """
        try:
            # Calculate short-time energy
            energy = librosa.feature.rms(
                y=audio_data, 
                frame_length=frame_length, 
                hop_length=hop_length
            )[0]
            
            # Calculate spectral centroid
            spectral_centroid = librosa.feature.spectral_centroid(
                y=audio_data, 
                sr=self.sample_rate,
                hop_length=hop_length
            )[0]
            
            # Calculate zero crossing rate
            zcr = librosa.feature.zero_crossing_rate(
                audio_data, 
                frame_length=frame_length, 
                hop_length=hop_length
            )[0]
            
            # Combine features for VAD decision
            energy_threshold = np.percentile(energy, 30)
            centroid_threshold = np.percentile(spectral_centroid, 30)
            zcr_threshold = np.percentile(zcr, 70)
            
            vad = (energy > energy_threshold) & \
                  (spectral_centroid > centroid_threshold) & \
                  (zcr < zcr_threshold)
            
            return vad
            
        except Exception as e:
            self.logger.error(f"Error in voice activity detection: {str(e)}")
            return np.ones(len(audio_data) // hop_length, dtype=bool)
    
    def analyze_audio_features(self, audio_data: np.ndarray) -> Dict[str, Any]:
        """
        Extract comprehensive audio features for emotion detection
        
        Args:
            audio_data: Input audio data
            
        Returns:
            Dictionary of audio features
        """
        try:
            features = {}
            
            # Basic statistics
            features['rms_energy'] = np.sqrt(np.mean(audio_data ** 2))
            features['zero_crossing_rate'] = np.mean(librosa.feature.zero_crossing_rate(audio_data)[0])
            
            # Spectral features
            spectral_centroids = librosa.feature.spectral_centroid(audio_data, sr=self.sample_rate)[0]
            features['spectral_centroid_mean'] = np.mean(spectral_centroids)
            features['spectral_centroid_std'] = np.std(spectral_centroids)
            
            spectral_rolloff = librosa.feature.spectral_rolloff(audio_data, sr=self.sample_rate)[0]
            features['spectral_rolloff_mean'] = np.mean(spectral_rolloff)
            
            spectral_bandwidth = librosa.feature.spectral_bandwidth(audio_data, sr=self.sample_rate)[0]
            features['spectral_bandwidth_mean'] = np.mean(spectral_bandwidth)
            
            # MFCC features
            mfccs = librosa.feature.mfcc(audio_data, sr=self.sample_rate, n_mfcc=13)
            for i in range(13):
                features[f'mfcc_{i}_mean'] = np.mean(mfccs[i])
                features[f'mfcc_{i}_std'] = np.std(mfccs[i])
            
            # Pitch features
            pitches, magnitudes = librosa.piptrack(audio_data, sr=self.sample_rate)
            pitch_values = []
            for t in range(pitches.shape[1]):
                index = magnitudes[:, t].argmax()
                pitch = pitches[index, t]
                if pitch > 0:
                    pitch_values.append(pitch)
            
            if pitch_values:
                features['pitch_mean'] = np.mean(pitch_values)
                features['pitch_std'] = np.std(pitch_values)
                features['pitch_min'] = np.min(pitch_values)
                features['pitch_max'] = np.max(pitch_values)
            else:
                features['pitch_mean'] = 0
                features['pitch_std'] = 0
                features['pitch_min'] = 0
                features['pitch_max'] = 0
            
            # Tempo
            tempo, _ = librosa.beat.beat_track(audio_data, sr=self.sample_rate)
            features['tempo'] = tempo
            
            return features
            
        except Exception as e:
            self.logger.error(f"Error analyzing audio features: {str(e)}")
            return {}
    
    def convert_format(self, audio_data: np.ndarray, 
                      target_format: str = "float32") -> np.ndarray:
        """
        Convert audio data to target format
        
        Args:
            audio_data: Input audio data
            target_format: Target format (float32, int16, etc.)
            
        Returns:
            Converted audio data
        """
        try:
            if target_format == "float32":
                if audio_data.dtype == np.int16:
                    return audio_data.astype(np.float32) / 32768.0
                elif audio_data.dtype == np.int32:
                    return audio_data.astype(np.float32) / 2147483648.0
                else:
                    return audio_data.astype(np.float32)
            
            elif target_format == "int16":
                if audio_data.dtype == np.float32 or audio_data.dtype == np.float64:
                    return (audio_data * 32767).astype(np.int16)
                else:
                    return audio_data.astype(np.int16)
            
            return audio_data
            
        except Exception as e:
            self.logger.error(f"Error converting audio format: {str(e)}")
            return audio_data
    
    def get_audio_devices(self) -> Dict[str, Any]:
        """Get available audio input devices"""
        try:
            devices = sd.query_devices()
            input_devices = []
            
            for i, device in enumerate(devices):
                if device['max_input_channels'] > 0:
                    input_devices.append({
                        'index': i,
                        'name': device['name'],
                        'channels': device['max_input_channels'],
                        'sample_rate': device['default_samplerate']
                    })
            
            return {
                'default_input': sd.default.device[0],
                'devices': input_devices
            }
            
        except Exception as e:
            self.logger.error(f"Error getting audio devices: {str(e)}")
            return {'default_input': None, 'devices': []}
    
    def test_audio_input(self, duration: float = 2.0, 
                        device: Optional[int] = None) -> Dict[str, Any]:
        """
        Test audio input and return statistics
        
        Args:
            duration: Test duration in seconds
            device: Audio device index (None for default)
            
        Returns:
            Dictionary with test results
        """
        try:
            self.logger.info(f"Testing audio input for {duration} seconds...")
            
            # Record test audio
            audio_data = sd.rec(
                int(duration * self.sample_rate),
                samplerate=self.sample_rate,
                channels=self.channels,
                device=device,
                dtype=np.float32
            )
            sd.wait()  # Wait for recording to complete
            
            # Analyze the recorded audio
            audio_data = audio_data.flatten()
            
            # Calculate statistics
            rms_level = np.sqrt(np.mean(audio_data ** 2))
            peak_level = np.max(np.abs(audio_data))
            snr_estimate = 20 * np.log10(rms_level / (np.std(audio_data[:int(0.1 * self.sample_rate)]) + 1e-10))
            
            # Voice activity detection
            vad_result = self.detect_voice_activity(audio_data)
            voice_activity_ratio = np.mean(vad_result)
            
            results = {
                'duration': duration,
                'sample_rate': self.sample_rate,
                'rms_level': float(rms_level),
                'peak_level': float(peak_level),
                'snr_estimate': float(snr_estimate),
                'voice_activity_ratio': float(voice_activity_ratio),
                'clipping_detected': peak_level > 0.95,
                'signal_detected': rms_level > 0.001,
                'recommended_gain': 1.0 / (peak_level + 0.1) if peak_level > 0 else 1.0
            }
            
            self.logger.info(f"Audio test completed: RMS={rms_level:.4f}, Peak={peak_level:.4f}")
            return results
            
        except Exception as e:
            self.logger.error(f"Error testing audio input: {str(e)}")
            return {
                'error': str(e),
                'duration': duration,
                'sample_rate': self.sample_rate
            }
