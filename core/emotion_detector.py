"""
Comprehensive Emotion Detection System
Supports 7 emotions with confidence scoring from text and audio features
"""

import asyncio
import time
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import threading
import pickle
import json
from pathlib import Path

import torch
from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
import librosa
from sklearn.preprocessing import StandardScaler

from config import EmotionConfig, MODELS_DIR
from utils import get_logger

@dataclass
class EmotionResult:
    """Data class for emotion detection results"""
    primary_emotion: str
    confidence: float
    all_emotions: Dict[str, float]
    processing_time: float
    source: str  # 'text', 'audio', or 'combined'
    timestamp: float

class EmotionDetector:
    """
    Advanced emotion detection from text and audio features
    Supports 7 emotions: neutral, happy, sad, angry, excited, frustrated, surprised
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Emotion categories
        self.emotions = EmotionConfig.EMOTIONS
        self.emotion_mapping = {
            # Map model outputs to our standard emotions
            'joy': 'happy',
            'happiness': 'happy',
            'sadness': 'sad',
            'anger': 'angry',
            'fear': 'surprised',
            'surprise': 'surprised',
            'disgust': 'frustrated',
            'neutral': 'neutral'
        }
        
        # Models
        self.text_classifier = None
        self.audio_classifier = None
        self.audio_scaler = None
        
        # Configuration
        self.min_confidence = EmotionConfig.MIN_CONFIDENCE_THRESHOLD
        self.window_size = EmotionConfig.EMOTION_WINDOW_SIZE
        
        # Caching and smoothing
        self.emotion_history = []
        self.history_lock = threading.Lock()
        self.max_history = 10
        
        # Performance tracking
        self.detection_count = 0
        self.total_processing_time = 0
        
        self.logger.info("EmotionDetector initialized")
    
    async def initialize(self):
        """Initialize emotion detection models"""
        try:
            self.logger.info("Initializing emotion detection models...")
            
            # Initialize text-based emotion detection
            await self._initialize_text_classifier()
            
            # Initialize audio-based emotion detection
            await self._initialize_audio_classifier()
            
            self.logger.info("Emotion detection models initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize emotion detector: {str(e)}", exc_info=True)
            return False
    
    async def _initialize_text_classifier(self):
        """Initialize text-based emotion classifier"""
        try:
            self.logger.info("Loading text emotion classifier...")
            
            # Use a pre-trained emotion classification model
            model_name = "j-hartmann/emotion-english-distilroberta-base"
            
            self.text_classifier = pipeline(
                "text-classification",
                model=model_name,
                device=0 if torch.cuda.is_available() else -1,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                return_all_scores=True
            )
            
            self.logger.info("Text emotion classifier loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to load text classifier: {str(e)}")
            # Create a fallback simple classifier
            self.text_classifier = self._create_fallback_text_classifier()
    
    def _create_fallback_text_classifier(self):
        """Create a simple fallback text classifier"""
        self.logger.info("Creating fallback text emotion classifier...")
        
        # Simple keyword-based emotion detection
        emotion_keywords = {
            'happy': ['happy', 'joy', 'great', 'wonderful', 'excellent', 'amazing', 'fantastic', 'good', 'love', 'excited'],
            'sad': ['sad', 'unhappy', 'depressed', 'down', 'terrible', 'awful', 'bad', 'disappointed', 'upset'],
            'angry': ['angry', 'mad', 'furious', 'annoyed', 'irritated', 'frustrated', 'hate', 'disgusted'],
            'excited': ['excited', 'thrilled', 'enthusiastic', 'energetic', 'pumped', 'eager'],
            'surprised': ['surprised', 'shocked', 'amazed', 'astonished', 'wow', 'incredible'],
            'frustrated': ['frustrated', 'annoyed', 'bothered', 'irritated', 'stressed'],
            'neutral': []  # Default fallback
        }
        
        def classify_text(text):
            text_lower = text.lower()
            scores = {}
            
            for emotion, keywords in emotion_keywords.items():
                score = sum(1 for keyword in keywords if keyword in text_lower)
                scores[emotion] = score
            
            # Normalize scores
            total_score = sum(scores.values())
            if total_score > 0:
                scores = {k: v / total_score for k, v in scores.items()}
            else:
                scores = {emotion: 1.0 / len(self.emotions) for emotion in self.emotions}
            
            # Convert to expected format
            return [{'label': emotion, 'score': score} for emotion, score in scores.items()]
        
        return classify_text
    
    async def _initialize_audio_classifier(self):
        """Initialize audio-based emotion classifier"""
        try:
            # Check if custom audio model exists
            model_path = EmotionConfig.EMOTION_MODEL_PATH
            config_path = EmotionConfig.EMOTION_CONFIG_PATH
            
            if model_path.exists() and config_path.exists():
                self.logger.info("Loading custom audio emotion classifier...")
                
                # Load custom model
                with open(model_path, 'rb') as f:
                    self.audio_classifier = pickle.load(f)
                
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    self.audio_scaler = StandardScaler()
                    if 'scaler_params' in config:
                        self.audio_scaler.mean_ = np.array(config['scaler_params']['mean'])
                        self.audio_scaler.scale_ = np.array(config['scaler_params']['scale'])
                
                self.logger.info("Custom audio emotion classifier loaded")
            else:
                self.logger.info("Custom audio model not found, creating feature-based classifier...")
                self.audio_classifier = self._create_audio_feature_classifier()
                
        except Exception as e:
            self.logger.error(f"Failed to initialize audio classifier: {str(e)}")
            self.audio_classifier = self._create_audio_feature_classifier()
    
    def _create_audio_feature_classifier(self):
        """Create a feature-based audio emotion classifier"""
        self.logger.info("Creating feature-based audio emotion classifier...")
        
        def classify_audio_features(features):
            """Simple rule-based audio emotion classification"""
            scores = {emotion: 0.0 for emotion in self.emotions}
            
            # Extract key features
            pitch_mean = features.get('pitch_mean', 0)
            pitch_std = features.get('pitch_std', 0)
            energy = features.get('rms_energy', 0)
            spectral_centroid = features.get('spectral_centroid_mean', 0)
            tempo = features.get('tempo', 0)
            zcr = features.get('zero_crossing_rate', 0)
            
            # Rule-based classification
            # Happy: Higher pitch, higher energy, stable pitch
            if pitch_mean > 150 and energy > 0.1 and pitch_std < 50:
                scores['happy'] += 0.3
            
            # Sad: Lower pitch, lower energy
            if pitch_mean < 120 and energy < 0.05:
                scores['sad'] += 0.3
            
            # Angry: Higher energy, variable pitch, higher tempo
            if energy > 0.15 and pitch_std > 60 and tempo > 120:
                scores['angry'] += 0.3
            
            # Excited: High energy, high pitch variation, fast tempo
            if energy > 0.2 and pitch_std > 70 and tempo > 140:
                scores['excited'] += 0.3
            
            # Surprised: Sudden pitch changes, higher spectral centroid
            if pitch_std > 80 and spectral_centroid > 2000:
                scores['surprised'] += 0.3
            
            # Frustrated: Moderate energy, irregular patterns
            if 0.08 < energy < 0.15 and zcr > 0.1:
                scores['frustrated'] += 0.3
            
            # Neutral: Default for balanced features
            scores['neutral'] = max(0.1, 1.0 - sum(scores.values()))
            
            # Normalize scores
            total = sum(scores.values())
            if total > 0:
                scores = {k: v / total for k, v in scores.items()}
            
            return scores
        
        return classify_audio_features
    
    async def detect_emotion(self, text: str, audio_features: Optional[Dict] = None) -> EmotionResult:
        """
        Detect emotion from text and optionally audio features
        
        Args:
            text: Input text to analyze
            audio_features: Optional audio features dictionary
            
        Returns:
            EmotionResult with detected emotion and confidence
        """
        start_time = time.time()
        
        try:
            # Detect emotion from text
            text_emotions = await self._detect_text_emotion(text)
            
            # Detect emotion from audio if features provided
            audio_emotions = {}
            if audio_features and self.audio_classifier:
                audio_emotions = await self._detect_audio_emotion(audio_features)
            
            # Combine results
            combined_emotions = self._combine_emotion_results(text_emotions, audio_emotions)
            
            # Apply temporal smoothing
            smoothed_emotions = self._apply_temporal_smoothing(combined_emotions)
            
            # Determine primary emotion
            primary_emotion = max(smoothed_emotions.items(), key=lambda x: x[1])
            
            # Create result
            result = EmotionResult(
                primary_emotion=primary_emotion[0],
                confidence=primary_emotion[1],
                all_emotions=smoothed_emotions,
                processing_time=time.time() - start_time,
                source='combined' if audio_emotions else 'text',
                timestamp=time.time()
            )
            
            # Update statistics
            self.detection_count += 1
            self.total_processing_time += result.processing_time
            
            # Store in history for smoothing
            with self.history_lock:
                self.emotion_history.append(result)
                if len(self.emotion_history) > self.max_history:
                    self.emotion_history.pop(0)
            
            self.logger.debug(f"Emotion detected: {result.primary_emotion} ({result.confidence:.2f})")
            return result
            
        except Exception as e:
            self.logger.error(f"Error detecting emotion: {str(e)}", exc_info=True)
            
            # Return neutral emotion as fallback
            return EmotionResult(
                primary_emotion='neutral',
                confidence=0.5,
                all_emotions={'neutral': 1.0},
                processing_time=time.time() - start_time,
                source='fallback',
                timestamp=time.time()
            )
    
    async def _detect_text_emotion(self, text: str) -> Dict[str, float]:
        """Detect emotion from text"""
        try:
            if not text.strip():
                return {'neutral': 1.0}
            
            # Get predictions from text classifier
            if callable(self.text_classifier):
                # Fallback classifier
                predictions = self.text_classifier(text)
            else:
                # Transformer model
                raw_predictions = self.text_classifier(text)
                # Handle different return formats
                if isinstance(raw_predictions, list) and len(raw_predictions) > 0:
                    if isinstance(raw_predictions[0], list):
                        predictions = raw_predictions[0]  # Nested list format
                    else:
                        predictions = raw_predictions  # Direct list format
                else:
                    predictions = raw_predictions
            
            # Convert to our emotion format
            emotions = {}

            # Handle different prediction formats
            if not isinstance(predictions, list):
                predictions = [predictions]

            for pred in predictions:
                try:
                    if isinstance(pred, dict) and 'label' in pred and 'score' in pred:
                        label = pred['label'].lower()
                        score = pred['score']
                    else:
                        # Skip invalid predictions
                        continue

                    # Map to our emotion categories
                    mapped_emotion = self.emotion_mapping.get(label, label)
                    if mapped_emotion in self.emotions:
                        emotions[mapped_emotion] = emotions.get(mapped_emotion, 0) + score
                except (KeyError, TypeError) as e:
                    self.logger.debug(f"Skipping invalid prediction: {pred}")
                    continue
            
            # Ensure all emotions are represented
            for emotion in self.emotions:
                if emotion not in emotions:
                    emotions[emotion] = 0.0
            
            # Normalize
            total = sum(emotions.values())
            if total > 0:
                emotions = {k: v / total for k, v in emotions.items()}
            
            return emotions
            
        except Exception as e:
            self.logger.error(f"Error in text emotion detection: {str(e)}")
            return {'neutral': 1.0}
    
    async def _detect_audio_emotion(self, audio_features: Dict) -> Dict[str, float]:
        """Detect emotion from audio features"""
        try:
            if not self.audio_classifier:
                return {}
            
            # Get emotion scores from audio classifier
            emotions = self.audio_classifier(audio_features)
            
            # Ensure all emotions are represented
            for emotion in self.emotions:
                if emotion not in emotions:
                    emotions[emotion] = 0.0
            
            return emotions
            
        except Exception as e:
            self.logger.error(f"Error in audio emotion detection: {str(e)}")
            return {}
    
    def _combine_emotion_results(self, text_emotions: Dict[str, float], 
                               audio_emotions: Dict[str, float]) -> Dict[str, float]:
        """Combine text and audio emotion results"""
        if not audio_emotions:
            return text_emotions
        
        # Weight combination (text: 0.7, audio: 0.3)
        text_weight = 0.7
        audio_weight = 0.3
        
        combined = {}
        for emotion in self.emotions:
            text_score = text_emotions.get(emotion, 0.0)
            audio_score = audio_emotions.get(emotion, 0.0)
            combined[emotion] = text_weight * text_score + audio_weight * audio_score
        
        return combined
    
    def _apply_temporal_smoothing(self, current_emotions: Dict[str, float]) -> Dict[str, float]:
        """Apply temporal smoothing to reduce emotion jitter"""
        if not self.emotion_history:
            return current_emotions
        
        # Get recent emotion history
        with self.history_lock:
            recent_history = self.emotion_history[-3:]  # Last 3 detections
        
        # Apply exponential smoothing
        smoothed = current_emotions.copy()
        alpha = 0.6  # Smoothing factor
        
        for emotion in self.emotions:
            if recent_history:
                # Calculate weighted average with recent history
                historical_avg = np.mean([h.all_emotions.get(emotion, 0) for h in recent_history])
                current_value = current_emotions.get(emotion, 0.0)  # Use get() to avoid KeyError
                smoothed[emotion] = alpha * current_value + (1 - alpha) * historical_avg
            else:
                smoothed[emotion] = current_emotions.get(emotion, 0.0)
        
        return smoothed
    
    def get_emotion_statistics(self) -> Dict[str, Any]:
        """Get emotion detection statistics"""
        with self.history_lock:
            if not self.emotion_history:
                return {}
            
            # Calculate emotion distribution
            emotion_counts = {emotion: 0 for emotion in self.emotions}
            total_confidence = 0
            
            for result in self.emotion_history:
                emotion_counts[result.primary_emotion] += 1
                total_confidence += result.confidence
            
            total_detections = len(self.emotion_history)
            avg_confidence = total_confidence / total_detections if total_detections > 0 else 0
            avg_processing_time = self.total_processing_time / self.detection_count if self.detection_count > 0 else 0
            
            return {
                'total_detections': self.detection_count,
                'average_confidence': avg_confidence,
                'average_processing_time_ms': avg_processing_time * 1000,
                'emotion_distribution': {k: v / total_detections for k, v in emotion_counts.items()},
                'recent_emotions': [
                    {
                        'emotion': r.primary_emotion,
                        'confidence': r.confidence,
                        'timestamp': r.timestamp
                    }
                    for r in self.emotion_history[-10:]
                ]
            }
    
    def reset_history(self):
        """Reset emotion history"""
        with self.history_lock:
            self.emotion_history.clear()
        
        self.detection_count = 0
        self.total_processing_time = 0
        
        self.logger.info("Emotion detection history reset")
    
    def set_sensitivity(self, min_confidence: float):
        """Set minimum confidence threshold"""
        self.min_confidence = max(0.0, min(1.0, min_confidence))
        self.logger.info(f"Emotion detection sensitivity set to {self.min_confidence}")
    
    def is_available(self) -> bool:
        """Check if emotion detection is available"""
        return self.text_classifier is not None

    def save_custom_model(self, model_data: Any, config: Dict[str, Any]):
        """Save a custom emotion detection model"""
        try:
            # Ensure models directory exists
            EmotionConfig.EMOTION_MODEL_PATH.parent.mkdir(parents=True, exist_ok=True)

            # Save model
            with open(EmotionConfig.EMOTION_MODEL_PATH, 'wb') as f:
                pickle.dump(model_data, f)

            # Save config
            with open(EmotionConfig.EMOTION_CONFIG_PATH, 'w') as f:
                json.dump(config, f, indent=2)

            self.logger.info("Custom emotion model saved successfully")

        except Exception as e:
            self.logger.error(f"Error saving custom model: {str(e)}")

    def shutdown(self):
        """Shutdown emotion detector and free resources"""
        self.logger.info("Shutting down EmotionDetector...")

        # Clear models
        self.text_classifier = None
        self.audio_classifier = None
        self.audio_scaler = None

        # Clear history
        with self.history_lock:
            self.emotion_history.clear()

        self.logger.info("EmotionDetector shutdown complete")
