"""
Core Voice Processing Engine
Optimized for GTX 1660 Ti with RealtimeSTT integration

This module provides the main voice processing functionality including:
- Real-time speech-to-text conversion
- Voice activity detection
- Audio stream management
- Integration with emotion detection and text formatting
"""

import asyncio
import time
import threading
from typing import Optional, Callable, Dict, Any
from dataclasses import dataclass

import torch
from RealtimeSTT import AudioToTextRecorder

from config import ModelConfig, AudioConfig, PerformanceConfig
from utils import get_logger
from .audio_utils import AudioUtils
from .emotion_detector import EmotionDetector
from .text_formatter import TextFormatter

@dataclass
class TranscriptionResult:
    """Data class for transcription results"""
    text: str
    confidence: float
    timestamp: float
    is_final: bool
    processing_time: float
    emotion: Optional[Dict[str, float]] = None
    pauses: Optional[Dict[str, float]] = None
    filler_words: Optional[list] = None

class VoiceProcessor:
    """
    Main voice processing engine using RealtimeSTT
    Optimized for GTX 1660 Ti performance
    """
    
    def __init__(self, model_manager, console_display, status_monitor, performance_monitor):
        self.logger = get_logger(__name__)
        self.model_manager = model_manager
        self.console_display = console_display
        self.status_monitor = status_monitor
        self.performance_monitor = performance_monitor
        
        # Core components
        self.audio_utils = AudioUtils()
        self.emotion_detector = EmotionDetector()
        self.text_formatter = TextFormatter()
        
        # RealtimeSTT recorder
        self.recorder: Optional[AudioToTextRecorder] = None
        
        # State management
        self.is_running = False
        self.is_recording = False
        self.current_session_id = None
        self.pause_start_time = None
        self.last_speech_time = None
        
        # Performance tracking
        self.transcription_count = 0
        self.total_processing_time = 0
        self.average_latency = 0
        
        # Callbacks
        self.on_transcription_callback: Optional[Callable] = None
        self.on_emotion_callback: Optional[Callable] = None
        self.on_status_callback: Optional[Callable] = None
        
        self.logger.info("VoiceProcessor initialized")
    
    async def initialize(self):
        """Initialize the voice processor with optimal settings"""
        try:
            self.logger.info("Initializing voice processor...")
            
            # Check GPU availability and memory
            await self._check_gpu_resources()
            
            # Initialize RealtimeSTT with optimal configuration
            await self._initialize_recorder()
            
            # Initialize emotion detector
            await self.emotion_detector.initialize()
            
            # Initialize text formatter
            self.text_formatter.initialize()
            
            self.logger.info("Voice processor initialization completed")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize voice processor: {str(e)}", exc_info=True)
            return False
    
    async def _check_gpu_resources(self):
        """Check GPU resources and optimize settings"""
        if torch.cuda.is_available():
            gpu_props = torch.cuda.get_device_properties(0)
            total_memory = gpu_props.total_memory / (1024**3)  # GB
            
            self.logger.info(f"GPU: {gpu_props.name}")
            self.logger.info(f"Total VRAM: {total_memory:.1f}GB")
            
            # Adjust settings based on available memory
            if total_memory < 6:
                self.logger.warning("Less than 6GB VRAM available. Using conservative settings.")
                ModelConfig.BATCH_SIZE = 2
                ModelConfig.PRIMARY_MODEL = "small.en"
            
            # Monitor initial memory usage
            allocated = torch.cuda.memory_allocated(0) / (1024**3)
            self.logger.info(f"Initial GPU memory usage: {allocated:.2f}GB")
        else:
            self.logger.warning("CUDA not available. Using CPU mode.")
            ModelConfig.DEVICE = "cpu"
    
    async def _initialize_recorder(self):
        """Initialize RealtimeSTT recorder with optimal configuration"""
        try:
            self.logger.info("Configuring RealtimeSTT recorder...")
            
            # Optimal configuration for GTX 1660 Ti
            self.recorder = AudioToTextRecorder(
                # Primary model settings
                model=ModelConfig.PRIMARY_MODEL,
                # Use CPU for RealtimeSTT to avoid CUDA version conflicts
                device="cpu",
                compute_type="float32",
                
                # Real-time processing
                enable_realtime_transcription=True,
                use_main_model_for_realtime=False,
                realtime_model_type="tiny.en",
                realtime_processing_pause=0.1,
                
                # Accuracy optimizations
                beam_size=ModelConfig.BEAM_SIZE,
                beam_size_realtime=3,
                language=ModelConfig.LANGUAGE,
                
                # Voice Activity Detection
                silero_sensitivity=AudioConfig.VAD_SENSITIVITY,
                silero_use_onnx=True,
                silero_deactivity_detection=True,
                webrtc_sensitivity=2,
                
                # Timing settings
                post_speech_silence_duration=AudioConfig.SILENCE_THRESHOLD,
                min_gap_between_recordings=0.5,
                min_length_of_recording=AudioConfig.MIN_RECORDING_LENGTH,
                pre_recording_buffer_duration=AudioConfig.PRE_RECORDING_BUFFER,
                
                # Performance optimizations
                early_transcription_on_silence=200,
                allowed_latency_limit=30,
                handle_buffer_overflow=True,
                
                # Quality settings
                ensure_sentence_starting_uppercase=True,
                ensure_sentence_ends_with_period=True,
                
                # Callbacks
                on_recording_start=self._on_recording_start,
                on_recording_stop=self._on_recording_stop,
                on_realtime_transcription_update=self._on_realtime_update,
                
                # Debug settings
                print_transcription_time=False,
                debug_mode=False
            )
            
            self.logger.info("RealtimeSTT recorder configured successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize recorder: {str(e)}", exc_info=True)
            raise
    
    def _on_recording_start(self):
        """Callback when recording starts"""
        self.is_recording = True
        self.last_speech_time = time.time()
        self.pause_start_time = None
        
        self.logger.debug("Recording started")
        self.status_monitor.update_status("recording", True)
        
        if self.console_display:
            self.console_display.show_recording_status(True)
    
    def _on_recording_stop(self):
        """Callback when recording stops"""
        self.is_recording = False
        
        self.logger.debug("Recording stopped")
        self.status_monitor.update_status("recording", False)
        
        if self.console_display:
            self.console_display.show_recording_status(False)
    
    def _on_speech_detected(self):
        """Callback when speech is detected"""
        self.pause_start_time = None
        self.last_speech_time = time.time()
        
        self.logger.debug("Speech detected")
        self.status_monitor.update_status("speech_detected", True)
    
    def _on_speech_ended(self):
        """Callback when speech ends"""
        self.pause_start_time = time.time()
        
        self.logger.debug("Speech ended")
        self.status_monitor.update_status("speech_detected", False)
    
    def _on_realtime_update(self, text: str):
        """Handle real-time transcription updates"""
        if not text.strip():
            return
        
        start_time = time.time()
        
        try:
            # Calculate pause information
            pause_info = self._calculate_pause_info()
            
            # Process text with formatter (basic formatting for real-time)
            formatted_text = self.text_formatter.format_realtime_text(
                text, pause_info
            )
            
            # Create transcription result
            result = TranscriptionResult(
                text=formatted_text,
                confidence=0.8,  # Estimated for real-time
                timestamp=time.time(),
                is_final=False,
                processing_time=time.time() - start_time,
                pauses=pause_info
            )
            
            # Display in console
            if self.console_display:
                self.console_display.display_realtime_text(result)
            
            # Call callback if set
            if self.on_transcription_callback:
                self.on_transcription_callback(result)
            
            # Update performance metrics
            self.performance_monitor.record_latency(result.processing_time * 1000)  # ms
            
        except Exception as e:
            self.logger.error(f"Error processing real-time update: {str(e)}", exc_info=True)
    
    def _calculate_pause_info(self) -> Dict[str, float]:
        """Calculate pause information"""
        pause_info = {}
        
        if self.pause_start_time:
            pause_duration = time.time() - self.pause_start_time
            
            if pause_duration >= AudioConfig.SHORT_PAUSE_MIN:
                if pause_duration <= AudioConfig.SHORT_PAUSE_MAX:
                    pause_info["type"] = "short"
                elif pause_duration <= AudioConfig.MEDIUM_PAUSE_MAX:
                    pause_info["type"] = "medium"
                else:
                    pause_info["type"] = "long"
                
                pause_info["duration"] = pause_duration
        
        return pause_info
    
    async def start(self):
        """Start the voice processing system"""
        if self.is_running:
            self.logger.warning("Voice processor is already running")
            return
        
        try:
            self.logger.info("Starting voice processor...")
            self.is_running = True
            
            # Start the main processing loop
            await self._main_processing_loop()
            
        except Exception as e:
            self.logger.error(f"Error starting voice processor: {str(e)}", exc_info=True)
            self.is_running = False
            raise
    
    async def _main_processing_loop(self):
        """Main processing loop for voice-to-text"""
        self.logger.info("Voice processing loop started")
        
        try:
            while self.is_running:
                # Get final transcription from recorder
                final_text = self.recorder.text()
                
                if final_text and final_text.strip():
                    await self._process_final_transcription(final_text)
                
                # Small delay to prevent excessive CPU usage
                await asyncio.sleep(0.01)
                
        except Exception as e:
            self.logger.error(f"Error in main processing loop: {str(e)}", exc_info=True)
            raise
    
    async def _process_final_transcription(self, text: str):
        """Process final high-quality transcription"""
        start_time = time.time()
        
        try:
            self.logger.debug(f"Processing final transcription: {text[:50]}...")
            
            # Detect emotion from text and audio
            emotion_result = await self.emotion_detector.detect_emotion(text)
            
            # Format text with all metadata
            formatted_text = self.text_formatter.format_final_text(
                text, 
                emotion_result,
                self._calculate_pause_info()
            )
            
            # Create final transcription result
            result = TranscriptionResult(
                text=formatted_text,
                confidence=0.95,  # High confidence for final result
                timestamp=time.time(),
                is_final=True,
                processing_time=time.time() - start_time,
                emotion=emotion_result,
                pauses=self._calculate_pause_info()
            )
            
            # Display final result
            if self.console_display:
                self.console_display.display_final_text(result)
            
            # Call callbacks
            if self.on_transcription_callback:
                self.on_transcription_callback(result)
            
            if self.on_emotion_callback and emotion_result:
                self.on_emotion_callback(emotion_result)
            
            # Update statistics
            self.transcription_count += 1
            self.total_processing_time += result.processing_time
            self.average_latency = (self.total_processing_time / self.transcription_count) * 1000
            
            # Update performance monitor
            self.performance_monitor.record_transcription(result)
            
            self.logger.debug(f"Final transcription processed in {result.processing_time:.3f}s")
            
        except Exception as e:
            self.logger.error(f"Error processing final transcription: {str(e)}", exc_info=True)
    
    def stop(self):
        """Stop the voice processing system"""
        if not self.is_running:
            return
        
        self.logger.info("Stopping voice processor...")
        self.is_running = False
        
        if self.recorder:
            try:
                self.recorder.shutdown()
            except Exception as e:
                self.logger.error(f"Error shutting down recorder: {str(e)}")
        
        self.logger.info("Voice processor stopped")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get processing statistics"""
        return {
            "transcription_count": self.transcription_count,
            "average_latency_ms": self.average_latency,
            "total_processing_time": self.total_processing_time,
            "is_running": self.is_running,
            "is_recording": self.is_recording
        }
    
    def set_callbacks(self, 
                     transcription_callback: Optional[Callable] = None,
                     emotion_callback: Optional[Callable] = None,
                     status_callback: Optional[Callable] = None):
        """Set callback functions for events"""
        self.on_transcription_callback = transcription_callback
        self.on_emotion_callback = emotion_callback
        self.on_status_callback = status_callback
