"""
Advanced Audio Processing
Implements sophisticated audio preprocessing including noise reduction, echo cancellation,
automatic gain control, and multi-microphone support
"""

import numpy as np
import scipy.signal
from scipy.fft import fft, ifft
from typing import Dict, List, Optional, Tuple, Any
import threading
import time
from collections import deque

from config import AudioConfig
from utils import get_logger

class NoiseReducer:
    """Advanced noise reduction using spectral subtraction and Wiener filtering"""
    
    def __init__(self, sample_rate: int = 16000):
        self.sample_rate = sample_rate
        self.logger = get_logger(__name__)
        
        # Noise profile
        self.noise_profile = None
        self.noise_estimation_frames = 10
        self.noise_frames_collected = 0
        
        # Spectral subtraction parameters
        self.alpha = 2.0  # Over-subtraction factor
        self.beta = 0.01  # Spectral floor factor
        self.frame_size = 512
        self.hop_size = 256
        
        # Wiener filter parameters
        self.noise_power_estimate = None
        self.smoothing_factor = 0.98
        
        self.logger.info("NoiseReducer initialized")
    
    def estimate_noise_profile(self, audio: np.ndarray) -> bool:
        """Estimate noise profile from audio segment"""
        try:
            # Convert to frequency domain
            frames = self._frame_audio(audio)
            
            if self.noise_profile is None:
                self.noise_profile = np.zeros(self.frame_size // 2 + 1)
            
            # Accumulate noise spectrum
            for frame in frames:
                spectrum = np.abs(fft(frame * np.hanning(len(frame))))[:self.frame_size // 2 + 1]
                self.noise_profile += spectrum
                self.noise_frames_collected += 1
                
                if self.noise_frames_collected >= self.noise_estimation_frames:
                    break
            
            # Average the noise profile
            if self.noise_frames_collected > 0:
                self.noise_profile /= self.noise_frames_collected
                self.logger.debug(f"Noise profile updated with {self.noise_frames_collected} frames")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error estimating noise profile: {str(e)}")
            return False
    
    def reduce_noise(self, audio: np.ndarray) -> np.ndarray:
        """Apply noise reduction to audio"""
        try:
            if self.noise_profile is None:
                self.logger.warning("No noise profile available, skipping noise reduction")
                return audio
            
            # Frame the audio
            frames = self._frame_audio(audio)
            processed_frames = []
            
            for frame in frames:
                # Apply window
                windowed_frame = frame * np.hanning(len(frame))
                
                # FFT
                spectrum = fft(windowed_frame)
                magnitude = np.abs(spectrum)
                phase = np.angle(spectrum)
                
                # Spectral subtraction
                magnitude_half = magnitude[:self.frame_size // 2 + 1]
                
                # Calculate subtraction factor
                subtraction_factor = self.alpha * (self.noise_profile / (magnitude_half + 1e-10))
                subtraction_factor = np.minimum(subtraction_factor, 1.0 - self.beta)
                
                # Apply subtraction
                processed_magnitude = magnitude_half * (1.0 - subtraction_factor)
                processed_magnitude = np.maximum(processed_magnitude, self.beta * magnitude_half)
                
                # Reconstruct full spectrum
                full_magnitude = np.concatenate([processed_magnitude, processed_magnitude[-2:0:-1]])
                processed_spectrum = full_magnitude * np.exp(1j * phase)
                
                # IFFT
                processed_frame = np.real(ifft(processed_spectrum))
                processed_frames.append(processed_frame)
            
            # Overlap-add reconstruction
            return self._overlap_add(processed_frames)
            
        except Exception as e:
            self.logger.error(f"Error in noise reduction: {str(e)}")
            return audio
    
    def _frame_audio(self, audio: np.ndarray) -> List[np.ndarray]:
        """Split audio into overlapping frames"""
        frames = []
        for i in range(0, len(audio) - self.frame_size + 1, self.hop_size):
            frame = audio[i:i + self.frame_size]
            if len(frame) == self.frame_size:
                frames.append(frame)
        return frames
    
    def _overlap_add(self, frames: List[np.ndarray]) -> np.ndarray:
        """Reconstruct audio from overlapping frames"""
        if not frames:
            return np.array([])
        
        output_length = (len(frames) - 1) * self.hop_size + self.frame_size
        output = np.zeros(output_length)
        
        for i, frame in enumerate(frames):
            start = i * self.hop_size
            end = start + self.frame_size
            output[start:end] += frame
        
        return output

class EchoCanceller:
    """Acoustic echo cancellation using adaptive filtering"""
    
    def __init__(self, filter_length: int = 512):
        self.filter_length = filter_length
        self.logger = get_logger(__name__)
        
        # Adaptive filter coefficients
        self.filter_coeffs = np.zeros(filter_length)
        self.step_size = 0.01
        
        # Input buffers
        self.reference_buffer = deque(maxlen=filter_length)
        self.error_buffer = deque(maxlen=100)
        
        self.logger.info("EchoCanceller initialized")
    
    def process(self, input_signal: np.ndarray, reference_signal: np.ndarray) -> np.ndarray:
        """Process audio to cancel echo"""
        try:
            output = np.zeros_like(input_signal)
            
            for i, (input_sample, ref_sample) in enumerate(zip(input_signal, reference_signal)):
                # Update reference buffer
                self.reference_buffer.append(ref_sample)
                
                if len(self.reference_buffer) == self.filter_length:
                    # Convert to numpy array for processing
                    ref_array = np.array(self.reference_buffer)
                    
                    # Estimate echo
                    echo_estimate = np.dot(self.filter_coeffs, ref_array)
                    
                    # Calculate error (desired signal)
                    error = input_sample - echo_estimate
                    output[i] = error
                    
                    # Update filter coefficients (LMS algorithm)
                    self.filter_coeffs += self.step_size * error * ref_array
                    
                    # Store error for convergence monitoring
                    self.error_buffer.append(error ** 2)
                else:
                    output[i] = input_sample
            
            return output
            
        except Exception as e:
            self.logger.error(f"Error in echo cancellation: {str(e)}")
            return input_signal

class AutomaticGainControl:
    """Automatic gain control to maintain consistent audio levels"""
    
    def __init__(self, target_level: float = 0.1, attack_time: float = 0.01, release_time: float = 0.1):
        self.target_level = target_level
        self.attack_time = attack_time
        self.release_time = release_time
        self.logger = get_logger(__name__)
        
        # AGC state
        self.current_gain = 1.0
        self.envelope = 0.0
        
        # Time constants (converted to coefficients)
        self.attack_coeff = np.exp(-1.0 / (attack_time * AudioConfig.SAMPLE_RATE))
        self.release_coeff = np.exp(-1.0 / (release_time * AudioConfig.SAMPLE_RATE))
        
        self.logger.info("AutomaticGainControl initialized")
    
    def process(self, audio: np.ndarray) -> np.ndarray:
        """Apply automatic gain control"""
        try:
            output = np.zeros_like(audio)
            
            for i, sample in enumerate(audio):
                # Calculate envelope
                abs_sample = abs(sample)
                if abs_sample > self.envelope:
                    self.envelope = self.attack_coeff * self.envelope + (1 - self.attack_coeff) * abs_sample
                else:
                    self.envelope = self.release_coeff * self.envelope + (1 - self.release_coeff) * abs_sample
                
                # Calculate desired gain
                if self.envelope > 1e-10:
                    desired_gain = self.target_level / self.envelope
                else:
                    desired_gain = 1.0
                
                # Smooth gain changes
                if desired_gain < self.current_gain:
                    self.current_gain = self.attack_coeff * self.current_gain + (1 - self.attack_coeff) * desired_gain
                else:
                    self.current_gain = self.release_coeff * self.current_gain + (1 - self.release_coeff) * desired_gain
                
                # Apply gain with limiting
                self.current_gain = np.clip(self.current_gain, 0.1, 10.0)
                output[i] = sample * self.current_gain
            
            return output
            
        except Exception as e:
            self.logger.error(f"Error in AGC: {str(e)}")
            return audio

class MultiMicrophoneProcessor:
    """Multi-microphone processing for beamforming and spatial filtering"""
    
    def __init__(self, num_mics: int = 2, mic_spacing: float = 0.1):
        self.num_mics = num_mics
        self.mic_spacing = mic_spacing  # meters
        self.logger = get_logger(__name__)
        
        # Beamforming parameters
        self.sound_speed = 343.0  # m/s
        self.target_angle = 0.0  # degrees (front)
        
        # Delay-and-sum beamformer
        self.delays = self._calculate_delays()
        
        # Adaptive beamformer state
        self.correlation_matrix = None
        self.steering_vector = None
        
        self.logger.info(f"MultiMicrophoneProcessor initialized for {num_mics} microphones")
    
    def _calculate_delays(self) -> np.ndarray:
        """Calculate delays for delay-and-sum beamforming"""
        delays = np.zeros(self.num_mics)
        
        for i in range(self.num_mics):
            # Calculate delay relative to first microphone
            distance_diff = i * self.mic_spacing * np.sin(np.radians(self.target_angle))
            delay_samples = distance_diff / self.sound_speed * AudioConfig.SAMPLE_RATE
            delays[i] = delay_samples
        
        return delays
    
    def delay_and_sum(self, multi_channel_audio: np.ndarray) -> np.ndarray:
        """Apply delay-and-sum beamforming"""
        try:
            if multi_channel_audio.shape[1] != self.num_mics:
                self.logger.warning(f"Expected {self.num_mics} channels, got {multi_channel_audio.shape[1]}")
                return multi_channel_audio[:, 0]  # Return first channel
            
            output = np.zeros(multi_channel_audio.shape[0])
            
            for mic_idx in range(self.num_mics):
                delay_samples = int(self.delays[mic_idx])
                
                if delay_samples > 0:
                    # Positive delay - pad beginning
                    delayed_signal = np.pad(multi_channel_audio[:, mic_idx], (delay_samples, 0), mode='constant')[:len(output)]
                elif delay_samples < 0:
                    # Negative delay - trim beginning
                    delayed_signal = multi_channel_audio[-delay_samples:, mic_idx]
                    if len(delayed_signal) < len(output):
                        delayed_signal = np.pad(delayed_signal, (0, len(output) - len(delayed_signal)), mode='constant')
                else:
                    delayed_signal = multi_channel_audio[:, mic_idx]
                
                output += delayed_signal
            
            # Normalize by number of microphones
            output /= self.num_mics
            
            return output
            
        except Exception as e:
            self.logger.error(f"Error in delay-and-sum beamforming: {str(e)}")
            return multi_channel_audio[:, 0] if multi_channel_audio.ndim > 1 else multi_channel_audio
    
    def set_target_angle(self, angle_degrees: float):
        """Set target angle for beamforming"""
        self.target_angle = angle_degrees
        self.delays = self._calculate_delays()
        self.logger.debug(f"Target angle set to {angle_degrees} degrees")

class AdvancedAudioProcessor:
    """Main advanced audio processor combining all enhancement techniques"""
    
    def __init__(self, enable_noise_reduction: bool = True, enable_echo_cancellation: bool = True,
                 enable_agc: bool = True, enable_multi_mic: bool = False, num_mics: int = 1):
        self.logger = get_logger(__name__)
        
        # Configuration
        self.enable_noise_reduction = enable_noise_reduction
        self.enable_echo_cancellation = enable_echo_cancellation
        self.enable_agc = enable_agc
        self.enable_multi_mic = enable_multi_mic and num_mics > 1
        
        # Initialize processors
        self.noise_reducer = NoiseReducer() if enable_noise_reduction else None
        self.echo_canceller = EchoCanceller() if enable_echo_cancellation else None
        self.agc = AutomaticGainControl() if enable_agc else None
        self.multi_mic_processor = MultiMicrophoneProcessor(num_mics) if self.enable_multi_mic else None
        
        # Processing statistics
        self.processing_times = deque(maxlen=100)
        self.processed_frames = 0
        
        # Thread safety
        self.processing_lock = threading.Lock()
        
        self.logger.info("AdvancedAudioProcessor initialized")
    
    def initialize_noise_profile(self, noise_audio: np.ndarray) -> bool:
        """Initialize noise profile from background noise sample"""
        if self.noise_reducer:
            return self.noise_reducer.estimate_noise_profile(noise_audio)
        return False
    
    def process_audio(self, audio: np.ndarray, reference_audio: Optional[np.ndarray] = None) -> np.ndarray:
        """Process audio through all enabled enhancement stages"""
        try:
            with self.processing_lock:
                start_time = time.time()
                processed_audio = audio.copy()
                
                # Multi-microphone processing (if enabled and multi-channel input)
                if self.enable_multi_mic and self.multi_mic_processor and processed_audio.ndim > 1:
                    processed_audio = self.multi_mic_processor.delay_and_sum(processed_audio)
                
                # Ensure single channel for subsequent processing
                if processed_audio.ndim > 1:
                    processed_audio = processed_audio[:, 0]
                
                # Echo cancellation
                if self.enable_echo_cancellation and self.echo_canceller and reference_audio is not None:
                    if reference_audio.ndim > 1:
                        reference_audio = reference_audio[:, 0]
                    processed_audio = self.echo_canceller.process(processed_audio, reference_audio)
                
                # Noise reduction
                if self.enable_noise_reduction and self.noise_reducer:
                    processed_audio = self.noise_reducer.reduce_noise(processed_audio)
                
                # Automatic gain control
                if self.enable_agc and self.agc:
                    processed_audio = self.agc.process(processed_audio)
                
                # Update statistics
                processing_time = time.time() - start_time
                self.processing_times.append(processing_time)
                self.processed_frames += 1
                
                return processed_audio
                
        except Exception as e:
            self.logger.error(f"Error in audio processing: {str(e)}")
            return audio[:, 0] if audio.ndim > 1 else audio
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get processing performance statistics"""
        with self.processing_lock:
            if not self.processing_times:
                return {}
            
            times = list(self.processing_times)
            return {
                'processed_frames': self.processed_frames,
                'avg_processing_time_ms': np.mean(times) * 1000,
                'max_processing_time_ms': np.max(times) * 1000,
                'min_processing_time_ms': np.min(times) * 1000,
                'real_time_factor': np.mean(times) / (AudioConfig.CHUNK_SIZE / AudioConfig.SAMPLE_RATE),
                'enabled_features': {
                    'noise_reduction': self.enable_noise_reduction,
                    'echo_cancellation': self.enable_echo_cancellation,
                    'automatic_gain_control': self.enable_agc,
                    'multi_microphone': self.enable_multi_mic
                }
            }
    
    def configure(self, **kwargs):
        """Configure processor parameters"""
        if 'noise_reduction' in kwargs:
            self.enable_noise_reduction = kwargs['noise_reduction']
        
        if 'echo_cancellation' in kwargs:
            self.enable_echo_cancellation = kwargs['echo_cancellation']
        
        if 'agc' in kwargs:
            self.enable_agc = kwargs['agc']
        
        if 'target_angle' in kwargs and self.multi_mic_processor:
            self.multi_mic_processor.set_target_angle(kwargs['target_angle'])
        
        self.logger.info(f"Processor configuration updated: {kwargs}")
    
    def reset_statistics(self):
        """Reset processing statistics"""
        with self.processing_lock:
            self.processing_times.clear()
            self.processed_frames = 0
        
        self.logger.info("Processing statistics reset")

if __name__ == "__main__":
    # Test advanced audio processor
    processor = AdvancedAudioProcessor(
        enable_noise_reduction=True,
        enable_echo_cancellation=True,
        enable_agc=True
    )
    
    # Generate test audio
    sample_rate = 16000
    duration = 2.0
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # Clean speech signal
    speech = 0.5 * np.sin(2 * np.pi * 440 * t)
    
    # Add noise
    noise = 0.1 * np.random.randn(len(speech))
    noisy_speech = speech + noise
    
    # Initialize noise profile with noise
    processor.initialize_noise_profile(noise)
    
    # Process audio
    processed = processor.process_audio(noisy_speech)
    
    # Get statistics
    stats = processor.get_processing_statistics()
    print(f"Processing statistics: {stats}")
    
    print("Advanced audio processing test completed!")
