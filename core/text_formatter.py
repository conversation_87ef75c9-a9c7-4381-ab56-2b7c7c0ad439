"""
Advanced Text Processing & Formatting
Handles pause detection, filler word detection, punctuation, and metadata integration
"""

import re
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import threading

from config import TextProcessingConfig, AudioConfig
from utils import get_logger

@dataclass
class FillerWord:
    """Data class for detected filler words"""
    word: str
    position: int
    confidence: float

@dataclass
class PauseInfo:
    """Data class for pause information"""
    duration: float
    position: int
    pause_type: str  # 'short', 'medium', 'long'

class TextFormatter:
    """
    Advanced text formatter with comprehensive metadata integration
    Handles pause detection, filler word detection, and formatting
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Configuration
        self.filler_words = TextProcessingConfig.FILLER_WORDS
        self.pause_format = TextProcessingConfig.PAUSE_FORMAT
        self.emotion_format = TextProcessingConfig.EMOTION_FORMAT
        self.filler_format = TextProcessingConfig.FILLER_FORMAT
        
        # Formatting options
        self.add_pause_indicators = TextProcessingConfig.ADD_PAUSE_INDICATORS
        self.add_emotion_tags = TextProcessingConfig.ADD_EMOTION_TAGS
        self.add_filler_tags = TextProcessingConfig.ADD_FILLER_TAGS
        self.ensure_sentence_case = TextProcessingConfig.ENSURE_SENTENCE_CASE
        self.ensure_period_ending = TextProcessingConfig.ENSURE_PERIOD_ENDING
        
        # Filler word patterns (compiled for performance)
        self.filler_patterns = self._compile_filler_patterns()
        
        # Statistics
        self.formatting_count = 0
        self.filler_words_detected = 0
        self.pauses_detected = 0
        
        # Thread safety
        self.format_lock = threading.Lock()
        
        self.logger.info("TextFormatter initialized")
    
    def initialize(self):
        """Initialize the text formatter"""
        self.logger.info("TextFormatter ready for processing")
    
    def _compile_filler_patterns(self) -> Dict[str, re.Pattern]:
        """Compile regex patterns for filler word detection"""
        patterns = {}
        
        for filler in self.filler_words:
            # Create pattern that matches the filler word with word boundaries
            # Handle multi-word fillers like "you know"
            if ' ' in filler:
                # Multi-word filler
                pattern = r'\b' + re.escape(filler) + r'\b'
            else:
                # Single word filler - be more flexible with variations
                pattern = r'\b' + re.escape(filler) + r'[hmm]*\b'
            
            patterns[filler] = re.compile(pattern, re.IGNORECASE)
        
        return patterns
    
    def format_realtime_text(self, text: str, pause_info: Optional[Dict] = None) -> str:
        """
        Format text for real-time display (lightweight formatting)
        
        Args:
            text: Input text to format
            pause_info: Optional pause information
            
        Returns:
            Formatted text string
        """
        try:
            if not text.strip():
                return text
            
            formatted_text = text
            
            # Basic capitalization
            if self.ensure_sentence_case:
                formatted_text = self._ensure_sentence_case(formatted_text)
            
            # Add pause indicators if available
            if pause_info and self.add_pause_indicators:
                formatted_text = self._add_pause_indicator(formatted_text, pause_info)
            
            # Quick filler word detection (simplified for real-time)
            if self.add_filler_tags:
                formatted_text = self._detect_and_tag_fillers_simple(formatted_text)
            
            return formatted_text
            
        except Exception as e:
            self.logger.error(f"Error in real-time text formatting: {str(e)}")
            return text
    
    def format_final_text(self, text: str, emotion_result: Optional[Dict] = None, 
                         pause_info: Optional[Dict] = None) -> str:
        """
        Format final text with comprehensive metadata
        
        Args:
            text: Input text to format
            emotion_result: Emotion detection result
            pause_info: Pause information
            
        Returns:
            Fully formatted text with metadata
        """
        try:
            with self.format_lock:
                self.formatting_count += 1
            
            if not text.strip():
                return text
            
            formatted_text = text
            
            # Comprehensive text processing
            formatted_text = self._clean_text(formatted_text)
            formatted_text = self._ensure_sentence_case(formatted_text)
            formatted_text = self._add_punctuation(formatted_text)
            
            # Detect and tag filler words
            if self.add_filler_tags:
                formatted_text, filler_count = self._detect_and_tag_fillers(formatted_text)
                self.filler_words_detected += filler_count
            
            # Add pause indicators
            if pause_info and self.add_pause_indicators:
                formatted_text = self._add_pause_indicator(formatted_text, pause_info)
                self.pauses_detected += 1
            
            # Add emotion tags
            if emotion_result and self.add_emotion_tags:
                formatted_text = self._add_emotion_tag(formatted_text, emotion_result)
            
            # Final cleanup
            formatted_text = self._final_cleanup(formatted_text)
            
            return formatted_text
            
        except Exception as e:
            self.logger.error(f"Error in final text formatting: {str(e)}", exc_info=True)
            return text
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text"""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove leading/trailing whitespace
        text = text.strip()
        
        # Fix common transcription issues
        text = re.sub(r'\b([a-z])\1{2,}\b', r'\1', text)  # Remove repeated letters (e.g., "sooo" -> "so")
        
        return text
    
    def _ensure_sentence_case(self, text: str) -> str:
        """Ensure proper sentence capitalization"""
        if not text:
            return text
        
        # Capitalize first letter
        text = text[0].upper() + text[1:] if len(text) > 1 else text.upper()
        
        # Capitalize after sentence endings
        text = re.sub(r'([.!?]\s+)([a-z])', lambda m: m.group(1) + m.group(2).upper(), text)
        
        return text
    
    def _add_punctuation(self, text: str) -> str:
        """Add appropriate punctuation"""
        if not text:
            return text
        
        # Add period at the end if missing
        if self.ensure_period_ending and text[-1] not in '.!?':
            text += '.'
        
        # Add commas for natural pauses (simple heuristic)
        # This is a basic implementation - could be enhanced with NLP
        text = re.sub(r'\b(and|but|or|so|then|well|now)\b(?!\s*[,.!?])', r'\1,', text)
        
        return text
    
    def _detect_and_tag_fillers_simple(self, text: str) -> str:
        """Simple filler word detection for real-time processing"""
        for filler, pattern in self.filler_patterns.items():
            if pattern.search(text):
                # Just highlight the first occurrence for real-time
                text = pattern.sub(
                    lambda m: f"{m.group(0)} {self.filler_format.format(word='filler')}",
                    text, count=1
                )
                break
        
        return text
    
    def _detect_and_tag_fillers(self, text: str) -> Tuple[str, int]:
        """Comprehensive filler word detection and tagging"""
        filler_count = 0
        
        for filler, pattern in self.filler_patterns.items():
            matches = list(pattern.finditer(text))
            
            if matches:
                # Replace from right to left to maintain positions
                for match in reversed(matches):
                    filler_tag = self.filler_format.format(word=filler)
                    text = text[:match.end()] + f" {filler_tag}" + text[match.end():]
                    filler_count += 1
        
        return text, filler_count
    
    def _add_pause_indicator(self, text: str, pause_info: Dict) -> str:
        """Add pause indicator to text"""
        if not pause_info or 'duration' not in pause_info:
            return text
        
        duration = pause_info['duration']
        pause_tag = self.pause_format.format(duration=duration)
        
        # Add pause indicator at the end for now
        # Could be enhanced to detect optimal position
        return f"{text} {pause_tag}"
    
    def _add_emotion_tag(self, text: str, emotion_result) -> str:
        """Add emotion tag to text"""
        try:
            # Handle both dict and EmotionResult object formats
            if hasattr(emotion_result, 'primary_emotion') and hasattr(emotion_result, 'confidence'):
                # EmotionResult object
                emotion = emotion_result.primary_emotion
                confidence = emotion_result.confidence * 100  # Convert to percentage
            elif isinstance(emotion_result, dict) and 'primary_emotion' in emotion_result and 'confidence' in emotion_result:
                # Dictionary format
                emotion = emotion_result['primary_emotion']
                confidence = emotion_result['confidence'] * 100  # Convert to percentage
            else:
                return text

            # Only add tag if confidence is above threshold
            if confidence >= 60:  # 60% threshold
                emotion_tag = self.emotion_format.format(
                    emotion=emotion,
                    confidence=confidence
                )
                return f"{text} {emotion_tag}"

            return text
            
        except Exception as e:
            self.logger.error(f"Error adding emotion tag: {str(e)}")
            return text
    
    def _final_cleanup(self, text: str) -> str:
        """Final text cleanup and formatting"""
        # Remove extra spaces
        text = re.sub(r'\s+', ' ', text)
        
        # Fix spacing around punctuation
        text = re.sub(r'\s+([,.!?])', r'\1', text)
        text = re.sub(r'([,.!?])([a-zA-Z])', r'\1 \2', text)
        
        # Fix spacing around parentheses
        text = re.sub(r'\s*\(\s*', ' (', text)
        text = re.sub(r'\s*\)\s*', ') ', text)
        
        # Remove leading/trailing whitespace
        text = text.strip()
        
        return text
    
    def detect_filler_words(self, text: str) -> List[FillerWord]:
        """
        Detect filler words and return detailed information
        
        Args:
            text: Input text to analyze
            
        Returns:
            List of detected filler words with positions
        """
        filler_words = []
        
        for filler, pattern in self.filler_patterns.items():
            for match in pattern.finditer(text):
                filler_words.append(FillerWord(
                    word=filler,
                    position=match.start(),
                    confidence=0.9  # High confidence for exact matches
                ))
        
        return filler_words
    
    def analyze_text_complexity(self, text: str) -> Dict[str, Any]:
        """
        Analyze text complexity and characteristics
        
        Args:
            text: Input text to analyze
            
        Returns:
            Dictionary with text analysis results
        """
        try:
            words = text.split()
            sentences = re.split(r'[.!?]+', text)
            
            # Basic statistics
            word_count = len(words)
            sentence_count = len([s for s in sentences if s.strip()])
            avg_words_per_sentence = word_count / sentence_count if sentence_count > 0 else 0
            
            # Filler word analysis
            filler_words = self.detect_filler_words(text)
            filler_ratio = len(filler_words) / word_count if word_count > 0 else 0
            
            # Complexity metrics
            unique_words = len(set(word.lower() for word in words))
            lexical_diversity = unique_words / word_count if word_count > 0 else 0
            
            return {
                'word_count': word_count,
                'sentence_count': sentence_count,
                'avg_words_per_sentence': avg_words_per_sentence,
                'filler_word_count': len(filler_words),
                'filler_ratio': filler_ratio,
                'unique_words': unique_words,
                'lexical_diversity': lexical_diversity,
                'detected_fillers': [fw.word for fw in filler_words]
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing text complexity: {str(e)}")
            return {}
    
    def get_formatting_statistics(self) -> Dict[str, Any]:
        """Get text formatting statistics"""
        with self.format_lock:
            return {
                'total_formatted': self.formatting_count,
                'filler_words_detected': self.filler_words_detected,
                'pauses_detected': self.pauses_detected,
                'avg_fillers_per_text': self.filler_words_detected / self.formatting_count if self.formatting_count > 0 else 0
            }
    
    def reset_statistics(self):
        """Reset formatting statistics"""
        with self.format_lock:
            self.formatting_count = 0
            self.filler_words_detected = 0
            self.pauses_detected = 0
        
        self.logger.info("Text formatting statistics reset")
    
    def configure_formatting(self, **options):
        """Configure formatting options"""
        for option, value in options.items():
            if hasattr(self, option):
                setattr(self, option, value)
                self.logger.info(f"Formatting option '{option}' set to {value}")
            else:
                self.logger.warning(f"Unknown formatting option: {option}")
    
    def get_configuration(self) -> Dict[str, Any]:
        """Get current formatting configuration"""
        return {
            'add_pause_indicators': self.add_pause_indicators,
            'add_emotion_tags': self.add_emotion_tags,
            'add_filler_tags': self.add_filler_tags,
            'ensure_sentence_case': self.ensure_sentence_case,
            'ensure_period_ending': self.ensure_period_ending,
            'filler_words': self.filler_words
        }
