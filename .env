# Real-time Voice-to-Text System Environment Variables
# Copy this file and customize for your environment

# Model Configuration
REALSTT_MODEL=medium.en
REALSTT_DEVICE=cuda
REALSTT_COMPUTE_TYPE=float16

# API Configuration
REALSTT_API_PORT=8000
REALSTT_API_HOST=127.0.0.1

# Logging Configuration
REALSTT_LOG_LEVEL=INFO
REALSTT_DEBUG_MODE=false

# Performance Tuning
REALSTT_BATCH_SIZE=4
REALSTT_BEAM_SIZE=5
REALSTT_VAD_SENSITIVITY=0.7

# Audio Configuration
REALSTT_SAMPLE_RATE=16000
REALSTT_CHUNK_SIZE=1024

# Feature Flags
REALSTT_ENABLE_EMOTION_DETECTION=true
REALSTT_ENABLE_PAUSE_DETECTION=true
REALSTT_ENABLE_FILLER_DETECTION=true
REALSTT_ENABLE_API=true

# External API Keys (if needed for cloud services)
# OPENAI_API_KEY=your_openai_key_here
# AZURE_SPEECH_KEY=your_azure_key_here
# GOOGLE_CLOUD_KEY=your_google_key_here

# Development Settings
REALSTT_DEV_MODE=false
REALSTT_PROFILE_PERFORMANCE=false
