# Kubernetes Deployment for Real-time Voice-to-Text System
# Includes deployment, service, ingress, and GPU resource management

apiVersion: apps/v1
kind: Deployment
metadata:
  name: realstt-app
  namespace: realstt
  labels:
    app: realstt
    component: app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: realstt
      component: app
  template:
    metadata:
      labels:
        app: realstt
        component: app
    spec:
      containers:
      - name: realstt
        image: realstt:latest
        ports:
        - containerPort: 8000
          name: http
        - containerPort: 8001
          name: websocket
        env:
        - name: CUDA_VISIBLE_DEVICES
          value: "0"
        - name: LOG_LEVEL
          value: "INFO"
        - name: REDIS_URL
          value: "redis://realstt-redis:6379"
        - name: POSTGRES_URL
          value: "***********************************************************/realstt"
        resources:
          requests:
            memory: "4Gi"
            cpu: "2"
            nvidia.com/gpu: 1
          limits:
            memory: "8Gi"
            cpu: "4"
            nvidia.com/gpu: 1
        volumeMounts:
        - name: data-volume
          mountPath: /app/data
        - name: logs-volume
          mountPath: /app/logs
        - name: models-volume
          mountPath: /app/models
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: data-volume
        persistentVolumeClaim:
          claimName: realstt-data-pvc
      - name: logs-volume
        persistentVolumeClaim:
          claimName: realstt-logs-pvc
      - name: models-volume
        persistentVolumeClaim:
          claimName: realstt-models-pvc
      nodeSelector:
        accelerator: nvidia-tesla-gpu
      tolerations:
      - key: nvidia.com/gpu
        operator: Exists
        effect: NoSchedule

---
apiVersion: v1
kind: Service
metadata:
  name: realstt-service
  namespace: realstt
  labels:
    app: realstt
spec:
  selector:
    app: realstt
    component: app
  ports:
  - name: http
    port: 80
    targetPort: 8000
    protocol: TCP
  - name: websocket
    port: 8001
    targetPort: 8001
    protocol: TCP
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: realstt-ingress
  namespace: realstt
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/websocket-services: "realstt-service"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - realstt.yourdomain.com
    secretName: realstt-tls
  rules:
  - host: realstt.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: realstt-service
            port:
              number: 80
      - path: /ws
        pathType: Prefix
        backend:
          service:
            name: realstt-service
            port:
              number: 8001

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: realstt-data-pvc
  namespace: realstt
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: realstt-logs-pvc
  namespace: realstt
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: realstt-models-pvc
  namespace: realstt
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: fast-ssd

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: realstt-hpa
  namespace: realstt
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: realstt-app
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: realstt-config
  namespace: realstt
data:
  config.yaml: |
    model:
      primary_model: "medium.en"
      realtime_model: "tiny.en"
      device: "cuda"
      compute_type: "float16"
    
    audio:
      sample_rate: 16000
      chunk_size: 1024
      vad_sensitivity: 0.7
    
    api:
      host: "0.0.0.0"
      port: 8000
      cors_origins: ["*"]
    
    performance:
      target_latency_ms: 200
      max_vram_usage_mb: 4500
      batch_size: 4

---
apiVersion: v1
kind: Secret
metadata:
  name: realstt-secrets
  namespace: realstt
type: Opaque
data:
  postgres-password: cmVhbHN0dF9wYXNzd29yZA==  # base64 encoded "realstt_password"
  redis-password: ""  # empty for no password
  jwt-secret: eW91cl9qd3Rfc2VjcmV0X2tleQ==  # base64 encoded "your_jwt_secret_key"
